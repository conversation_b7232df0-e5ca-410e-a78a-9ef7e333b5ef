/**
 * 神券節限時搶券組件 - 反混淆版本
 * 這是一個React組件，用於美團外賣的限時搶券功能
 *
 * 主要功能：
 * 1. 券狀態管理（未開始、進行中、已結束、已搶完等）
 * 2. 倒計時功能
 * 3. 進度條顯示
 * 4. 券樣式配置
 * 5. 頁面跳轉處理
 * 6. 錯誤監控和上報
 * 7. 微信小程序適配
 *
 * 反混淆說明：
 * - 將混淆的變量名還原為有意義的名稱
 * - 將壓縮的代碼格式化為可讀格式
 * - 添加詳細的註釋說明
 * - 保持原有功能邏輯不變
 */

// UMD模塊定義
(function (global, factory) {
  if (typeof exports === 'object' && typeof module !== 'undefined') {
    module.exports = factory(
      require('react'),
      require('@gundam/gundam-core'),
      require('@gundam/gundam-ui'),
      require('@gundam/gundam-utils'),
      require('react/jsx-runtime'),
      require('@gundam/gundam-base-components')
    );
  } else if (typeof define === 'function' && define.amd) {
    define([
      'react',
      '@gundam/gundam-core',
      '@gundam/gundam-ui',
      '@gundam/gundam-utils',
      'react/jsx-runtime',
      '@gundam/gundam-base-components'
    ], factory);
  } else {
    global = typeof globalThis !== 'undefined' ? globalThis : global || self;
    global.__component_output__ = factory(
      global.Preact,
      global.GundamCore,
      global.GundamUI,
      global.GundamUtils,
      global.Preact,
      global.GundamBaseComponents
    );
  }
})(this, function (React, GundamCore, GundamUI, GundamUtils, JSXRuntime, GundamBaseComponents) {
  'use strict';

  // CSS類名映射表
  const CSS_CLASS_MAP = {
    '1ybe2o': {
      'component-wrapper': 'bo7z1',
      'content-wrapper-outer': 'QilV5',
      'content-wrapper': 'WNoBO',
      'layer-img-container': 'V7tAK',
      'layer-img': 'YLsCy',
      'rule-btn': '_4ELS8',
      'rule-btn-bg': '-RJEV',
      'rule-btn-text': 'Jj3kc',
      'coupons-line-wrapper': 'GHnBV',
      'coupon-lines': 'VU8Pm'
    },
    '1dezzt': {
      'coupon-wrapper': 'cdzYm',
      'grab-style': 'ke98Q',
      'sqj-icon-wrapper': '_4VJuw',
      'sqj-icon': 'esb-l',
      'price-wrapper': 'c1hmE',
      'price-wrapper-1': 'wl-tA',
      'price-wrapper-1-isAndroid': 'xxxyB',
      'price-wrapper-1-multi': 'WA7tP',
      'price-wrapper-1-multi-isAndroid': 'DZ7ZA',
      'price-wrapper-2': 'O1iuo',
      'price-wrapper-2-multi': 'lfLRy',
      'price-wrapper-2-multi-isAndroid': 'GZXGc',
      'price-wrapper-3': 'MPav9',
      'price-wrapper-3-multi': 'nsd5g',
      'price-wrapper-3-multi-H5': 'DuHRD',
      'price-wrapper-3-multi-isAndroid': 'stHKf',
      'price-content': '_6E2Dr',
      'price-unit': 'FE0QO',
      'price-unit-1': '_5quBR',
      'price-unit-2': 'SFzwU',
      'price-unit-3': 'mxQPn',
      'price-number': 'k3LMP',
      'price-number-1': 'iiL-i',
      'price-number-2': 'MXFbS',
      'price-number-3': 'AI9RG',
      'price-limit': 'j1k9V',
      'price-limit-1': 'L9f9f',
      'price-limit-1-isAndroid': '_5z0ft',
      'price-limit-2': 'fCYZK',
      'price-limit-2-multi': 'brv6d',
      'price-limit-3': '-PkfV',
      'price-limit-3-multi': 'W4t1n',
      'coupon-info': 'LjACW',
      'coupon-info-1': 'Agpbs',
      'coupon-info-1-multi': 'z-vdD',
      'coupon-info-2': '_1cggL',
      'coupon-info-2-multi': '_4bowN',
      'coupon-info-3': 'KE6Lm',
      'coupon-info-3-multi': 'w9kaE',
      'coupon-icon-1': 'tEkrM',
      'coupon-icon-2': 'QEhXx',
      'coupon-icon-3': 'Cx0k2',
      'coupon-name-3': '_0xR7h',
      'coupon-title': 'z5SQU',
      'coupon-title-1': 'a-Lrg',
      'coupon-title-2': '_7Fpi9',
      'coupon-title-3': 'X7Hbr',
      'coupon-desc-text': 'Y5yCO',
      'coupon-desc-text-red': 'WuXUV',
      'coupon-desc-text-1': 'houwq',
      'coupon-desc-text-2': 'CNnOZ',
      'coupon-desc-text-3': 'vUwPR',
      'coupon-desc-1': '_1vjeS',
      'coupon-desc-1-multi': 'w7JrD',
      'coupon-desc-2': '_9pwOE',
      'coupon-desc-2-multi': 'txF1V',
      'coupon-desc-3': 'eh43s',
      'coupon-desc-3-H5': '_4JfNV',
      'coupon-desc-3-multi': 'HLFQ8',
      'coupon-desc-3-multi-H5': 'QonJJ',
      'progress-bar': 'HX-vX',
      'progress-bar-1': 'pniD1',
      'progress-bar-2': '_1scuO',
      'progress-bar-outer': 'M8Hig',
      'progress-bar-outer-gray': 'jGMmD',
      'progress-bar-inner': 'YJoMG',
      'progress-bar-text': 'uThXg',
      'progress-bar-text-gray': 'fqsA-',
      'btn': 'fNthf',
      'btn-disable': 'Ad9mC',
      'btn-remindme': 'AT6eZ',
      'btn-remindme-done': '-ZdLj',
      'btn-bg': 'xotAf',
      'btn-text': 'Si6Wc',
      'btn-text-disable': 'SO0Lm',
      'btn-text-1': 'hg9CX',
      'btn-text-2': 'VUi0e',
      'btn-text-3': 'rIX7i',
      'eighteen-tag': 'mEiVc',
      'eighteen-tag-multiple-time': '_3Ep9L'
    },
    '11oojf': {
      'list-1': 'ezvKs',
      'list-1-time': 'IIgVT',
      'list-1-time-isAndroid': 'LHM5y',
      'list-1-time-desc': 'VtXxq',
      'list-1-time-desc-isAndroid': '_9--xS',
      'list-1-count-down-txt': 'HsC7X',
      'list-1-count-down-content': 'D9q7s',
      'list-1-hms': 'nsvqX',
      'list-1-colon': 'GDFLV',
      'list-n': 'uos5M',
      'list-n-scroll': 'XcZDm',
      'list-n-item': '_0-T-U',
      'list-n-item-time': 'EvSyQ',
      'list-n-item-status-txt': 'lE3Qo',
      'list-n-item-status-txt-doing': 'lRIid',
      'list-n-item-count-down': 'bk-2y',
      'list-n-item-count-down-hms': 'xUqe6',
      'list-n-item-count-down-colon': 't3TKG'
    },
    '26x95f': {
      'empty-card-wrapper': 'WzSQm',
      'empty-img': 'ON1jm',
      'text-wrapper': 'SBXcy',
      'empty-text-tip': 'c17v8'
    },
    '12kyc3': {
      'login-bg': 'ma38v',
      'login-text-tip': 'hRb-I',
      'login-btn': '-V8F1',
      'net-error-img': 'KTqrZ'
    },
    '1r4gew': {
      'rule': '-jSHi',
      'rule-wrap': 'bkR7s',
      'rule-area': 'ZHmJS',
      'rule-title': 'DL-Wu',
      'rule-close-btn': 'Z9pYA',
      'rule-text': 'RWKHk',
      'rule-text-scroller': 'JBZy1'
    }
  };

  // 類名生成函數
  function generateClassName() {
    try {
      const args = Array.prototype.slice.call(arguments);
      const classMap = CSS_CLASS_MAP;
      
      if (!args.length) return '';
      
      const cacheKey = args.join('__');
      if (globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[cacheKey]) {
        return globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[cacheKey];
      }
      
      const baseClasses = args.shift();
      const classNames = baseClasses.split(' ')
        .map(name => name.trim())
        .filter(name => !!name);
      
      if (!classNames.length) return '';
      
      const mappedClasses = classNames.map(className => {
        const originalName = className;
        let mappedName = '';
        
        args.some(mapKey => {
          const map = classMap[mapKey] || {};
          if (map[originalName]) {
            mappedName = map[originalName];
            return true;
          }
        });
        
        return mappedName;
      }).filter(name => !!name).join(' ');
      
      const result = baseClasses + ' ' + mappedClasses;
      globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[cacheKey] = result;
      return result;
    } catch (error) {
      console.log(error.message);
    }
    return '';
  }

  // CSS注入函數
  function injectCSS(cssText, options) {
    options = options || {};
    const insertAt = options.insertAt;

    if (cssText && typeof document !== 'undefined') {
      const head = document.head || document.getElementsByTagName('head')[0];
      const style = document.createElement('style');
      style.type = 'text/css';

      if (insertAt === 'top' && head.firstChild) {
        head.insertBefore(style, head.firstChild);
      } else {
        head.appendChild(style);
      }

      if (style.styleSheet) {
        style.styleSheet.cssText = cssText;
      } else {
        style.appendChild(document.createTextNode(cssText));
      }
    }
  }

  // 注入組件CSS樣式
  injectCSS(".cdzYm {\n  position: relative;\n  margin-top: 0.1rem;\n}\n.ke98Q {\n  opacity: 0.4;\n}\n._4VJuw {\n  width: 0.59rem;\n  height: 0.31rem;\n  border-top-left-radius: 0.15rem;\n  border-bottom-right-radius: 0.16rem;\n  overflow: hidden;\n  position: absolute;\n  top: 0.01rem;\n  left: 0.01rem;\n}\n.esb-l {\n  width: 0.59rem;\n  height: 0.31rem;\n}\n.c1hmE {\n  flex-direction: column;\n  align-items: center;\n}\n.wl-tA {\n  width: 1.7rem;\n  margin-top: 0.46rem;\n  padding-left: 0.08rem;\n}\n.xxxyB {\n  margin-top: 0.5rem;\n}\n.WA7tP {\n  margin-top: 0.24rem;\n  padding-left: 0.1rem;\n}\n.DZ7ZA {\n  margin-top: 0.27rem;\n}\n.O1iuo {\n  width: 1.26rem;\n  margin-top: 0.42rem;\n  padding-left: 0.05rem;\n}\n.lfLRy {\n  margin-top: 0.3rem;\n}\n.GZXGc {\n  margin-top: 0.32rem;\n}\n.MPav9 {\n  width: 2.13rem;\n  margin-top: 0.12rem;\n}\n.nsd5g {\n  margin-top: 0.07rem;\n}\n.DuHRD {\n  margin-top: 0.1rem;\n}\n.stHKf {\n  margin-top: 0.1rem;\n}\n._6E2Dr {\n  align-items: baseline;\n}\n.FE0QO {\n  font-family: AvenirLTPro-Heavy;\n  color: #FF2D19;\n}\n._5quBR {\n  font-size: 0.28rem;\n  line-height: 0.3rem;\n  font-weight: 700;\n}\n.SFzwU {\n  font-size: 0.24rem;\n  line-height: 0.26rem;\n  font-weight: 800;\n}\n.mxQPn {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  font-weight: 800;\n}\n.k3LMP {\n  font-family: AvenirLTPro-Heavy;\n  color: #FF2D19;\n}\n.iiL-i {\n  font-size: 0.6rem;\n  line-height: 0.62rem;\n  font-weight: 700;\n}\n.MXFbS {\n  font-size: 0.44rem;\n  line-height: 0.46rem;\n  font-weight: 800;\n}\n.AI9RG {\n  font-size: 0.36rem;\n  line-height: 0.38rem;\n  font-weight: 700;\n}\n.j1k9V {\n  font-family: PingFangSC-Regular;\n  font-size: 22px;\n  color: #FF2D19;\n  letter-spacing: 0;\n  text-align: center;\n  line-height: 24px;\n  font-weight: 400;\n}\n.L9f9f {\n  font-size: 0.24rem;\n  line-height: 0.26rem;\n}\n._5z0ft {\n  margin-top: -0.02rem;\n}\n.fCYZK {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n}\n.brv6d {\n  margin-top: -0.03rem;\n}\n.-PkfV {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  margin-top: -0.04rem;\n}\n.W4t1n {\n  font-size: 0.18rem;\n  line-height: 0.2rem;\n  margin-top: -0.05rem;\n}\n.LjACW {\n  flex-direction: column;\n}\n.Agpbs {\n  margin-top: 0.58rem;\n}\n.z-vdD {\n  margin-top: 0.36rem;\n}\n._1cggL {\n  margin-top: 0.5rem;\n}\n._4bowN {\n  margin-top: 0.41rem;\n}\n.KE6Lm {\n  margin-top: 0.1rem;\n}\n.w9kaE {\n  margin-top: 0.07rem;\n}\n.tEkrM {\n  width: 0.36rem;\n  height: 0.36rem;\n  margin-right: 0.04rem;\n  flex-shrink: 0;\n}\n.QEhXx {\n  width: 0.26rem;\n  height: 0.26rem;\n  margin-right: 0.02rem;\n  flex-shrink: 0;\n}\n.Cx0k2 {\n  width: 0.22rem;\n  height: 0.22rem;\n  margin-right: 0.02rem;\n  flex-shrink: 0;\n}\n._0xR7h {\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n}\n.z5SQU {\n  font-family: PingFangSC-Medium;\n  color: #222426;\n  font-weight: 500;\n}\n.a-Lrg {\n  font-size: 0.34rem;\n  line-height: 0.37rem;\n  width: 2.42rem;\n  margin-top: 0.01rem;\n}\n._7Fpi9 {\n  font-size: 0.26rem;\n  line-height: 0.28rem;\n  width: 1.56rem;\n}\n.X7Hbr {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  max-width: 1.54rem;\n}\n.Y5yCO {\n  font-family: PingFangSC-Regular;\n  font-size: 0.24rem;\n  color: #858687;\n  font-weight: 400;\n}\n.WuXUV {\n  color: #FF2D19;\n}\n.houwq {\n  max-width: 3rem;\n}\n.CNnOZ {\n  width: 1.9rem;\n  font-size: 0.22rem;\n}\n.vUwPR {\n  font-size: 0.16rem;\n  max-width: 1.9rem;\n}\n._1vjeS {\n  margin-top: 0.05rem;\n  margin-left: 0.04rem;\n}\n.w7JrD {\n  margin-top: 0.05rem;\n}\n._9pwOE {\n  margin-top: 0.04rem;\n}\n.txF1V {\n  margin-top: 0;\n}\n.eh43s {\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n  margin-top: 0.02rem;\n}\n._4JfNV {\n  margin-top: 0.06rem;\n}\n.HLFQ8 {\n  margin-top: -0.04rem;\n}\n.QonJJ {\n  margin-top: -0.01rem;\n}\n.HX-vX {\n  height: 0.24rem;\n  align-items: center;\n}\n.pniD1 {\n  margin-top: 0.06rem;\n}\n._1scuO {\n  margin-top: 0.04rem;\n}\n.M8Hig {\n  width: 1.14rem;\n  height: 0.1rem;\n  background-color: #FF4A2633;\n  border-radius: 0.08rem;\n  margin-right: 0.08rem;\n}\n.jGMmD {\n  opacity: 0.5;\n  background: #D3D3D3;\n}\n.YJoMG {\n  height: 100%;\n  background-color: #FF2D19;\n  border-radius: 0.08rem;\n}\n.uThXg {\n  font-family: PingFangSC-Regular;\n  font-size: 0.18rem;\n  color: #FF2D19;\n  text-align: center;\n  line-height: 0.2rem;\n  font-weight: 400;\n}\n.fqsA- {\n  color: #858687;\n}\n.fNthf {\n  position: absolute;\n  border-radius: 0.32rem;\n}\n.Ad9mC {\n  background: #D3D3D37F;\n}\n.AT6eZ {\n  background-image: linear-gradient(270deg, #53C03C 0%, #67CC52 100%);\n}\n.-ZdLj {\n  opacity: 0.6;\n}\n.xotAf {\n  width: 100%;\n  height: 100%;\n}\n.Si6Wc {\n  font-family: PingFangSC-Semibold;\n  font-size: 0.28rem;\n  width: 100%;\n  color: #FFFFFF;\n  letter-spacing: 0;\n  text-align: center;\n  line-height: 0.31rem;\n  font-weight: 600;\n}\n.SO0Lm {\n  color: #858687;\n  opacity: 0.7;\n}\n.hg9CX {\n  position: absolute;\n  top: 0.18rem;\n}\n.VUi0e,\n.rIX7i {\n  font-family: PingFangSC-Medium;\n  font-size: 0.24rem;\n  line-height: 0.28rem;\n  font-weight: 500;\n}\n.mEiVc {\n  width: 1.1rem;\n  height: 0.82rem;\n  position: absolute;\n  z-index: 2;\n  top: -0.07rem;\n  right: -0.18rem;\n}\n._3Ep9L {\n  top: -0.22rem;\n}\n");

  // 初始化全局變量
  globalThis.gdcgdlimitedtimegrabcoupon0020 = {};
  globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache = {};

  // 枚舉定義
  const CouponType = {
    WaiMai: 1,    // 外賣券
    ToStore: 2    // 到店券
  };

  const Platform = {
    mt: 1,        // 美團
    wm: 3         // 外賣
  };

  const DeviceType = {
    Android: 1,
    IOS: 2
  };

  const GrabStatus = {
    Fail: 0,           // 失敗
    NotJoin: 2,        // 未參與
    Success: 3,        // 成功
    GrabOver: 4,       // 已搶完
    CantGrabRep: 5,    // 不能重複搶
    ReachedLimit: 8    // 達到限制
  };

  const RoundStatus = {
    WillBegin: 'willBegin',  // 即將開始
    IsDoing: 'isDoing',      // 進行中
    Miss: 'miss'             // 已結束
  };

  const CouponStatus = {
    WillBegin: 'willBegin',        // 即將開始
    CanCatch: 'canCatch',          // 可以搶
    ToUse: 'toUse',                // 去使用
    InUse: 'inUse',                // 使用中
    Used: 'used',                  // 已使用
    OverTime: 'overTime',          // 已過期
    GrabOver: 'grabOver',          // 已搶完
    Miss: 'miss',                  // 已結束
    RemindMe: 'remindMe',          // 提醒我
    Reminded: 'reminded',          // 已提醒
    CantCatch: 'cantCatch',        // 不能搶
    ReceivedAlready: 'receivedAlready'  // 已領取
  };

  // 狀態文本映射（保持與原始代碼一致的簡體中文）
  const STATUS_TEXT_MAP = {
    willBegin: '即将开始',
    canCatch: '立即抢券',
    toUse: '去使用',
    inUse: '去使用',
    used: '已使用',
    overTime: '已过期',
    grabOver: '已抢完',
    miss: '已结束',
    remindMe: '提醒我',
    reminded: '已提醒',
    cantCatch: '不可领取',
    receivedAlready: '已领取'
  };

  const CouponUseStatus = {
    NotUse: 0,     // 未使用
    Used: 1,       // 已使用
    OverTime: 2    // 已過期
  };

  const IconType = {
    Custom: 'custom',
    SqjNoema: 'sqjNoema'
  };

  const OneLineType = {
    OneLine1: '1',
    OneLine2: '2',
    OneLine3: '3'
  };

  const ErrorType = {
    NeedLogin: 0,        // 需要登錄
    NoInTime: 1,         // 不在時間內
    OtherAnomalies: 2    // 其他異常
  };

  // 工具函數
  const GundamMonitor = GundamCore.gdMonitor;
  const GundamUtil = GundamCore.gdUtil;

  // 錯誤報告函數
  const reportError = function (errorType, errorData, description, roundCode = '', rightCode = '') {
    const globalData = GundamUtil.getGlobalData();
    GundamMonitor.addError({
      error: {
        name: `[gd-limited-time-grab-coupon] > [${errorType}-err]:code-${errorData?.code},subcode-${errorData?.data?.subCode},status-${errorData?.data?.status},roundCode-${roundCode},rightCode-${rightCode}`,
        msg: `神券節限時搶玩法-${description},gdId-${globalData.gdId},pageId-${globalData.pageId}`
      },
      opts: {
        category: globalThis?.Owl?.errorModel?.CATEGORY?.AJAX,
        level: 'warn'
      }
    });
  };

  // 環境檢測
  const Environment = GundamCore.env;
  const GundamFeature = GundamCore.gdFeature;

  // 微信小程序AppId
  const WX_APPID_WAIMAI = 'wxde8ac0a21135c07d';
  const WX_APPID_MEITUAN = 'wx2c348cf579062e56';

  // 獲取微信AppId
  const getWxAppId = function () {
    const userAgent = String(window?.navigator?.userAgent || '').toLowerCase();
    
    if (Environment.isWxMpWm || userAgent.includes(WX_APPID_MEITUAN)) {
      return WX_APPID_MEITUAN;
    }
    
    if (Environment.isWxMpMtWm || Environment.isWxMpMtSg || Environment.isWxMpMtThh || userAgent.includes(WX_APPID_WAIMAI)) {
      return WX_APPID_WAIMAI;
    }
    
    return '';
  };

  // Toast提示
  const showToast = function (message) {
    GundamUI.Toast.show({
      message: message
    });
  };

  // 處理搶券成功事件
  const handleGrabSuccess = function () {
    try {
      const globalData = GundamUtil.getGlobalData();
      const pageId = globalData?.pageId;
      const config = globalData?.config?.limitedSaleWithRefreshConfig;
      const delayTime = config.delayTime;
      const dataList = config.limitedSaleWithRefreshDataList;
      
      if (dataList && dataList.length > 0 && dataList.includes(String(pageId))) {
        setTimeout(function () {
          GundamCore.gdEvent.emit('refreshSupply', {});
        }, delayTime);
      }
    } catch (error) {
      console.log('[gd-limited-time-grab-coupon] - handleEmitGrabSuccess - error', error);
      reportError('handleEmitGrabSuccess', error, '限時搶組件通知供給事件失敗');
    }
  };

  // 報告點擊事件
  const reportClick = function () {
    console.log('report click of gd-limited-time-grab-coupon');
    GundamCore.gdEvent.emit('wm-coupon-festival-tab-click', {});
  };

  // 構建API URL
  function buildApiUrl(path, isWaimaiApi) {
    const urlMap = isWaimaiApi ? {
      local: '//promotion.waimai.test.sankuai.com',
      test: '//promotion.waimai.test.sankuai.com',
      stage: '//promotion.waimai.st.sankuai.com',
      prod: '//promotion.waimai.meituan.com'
    } : {
      local: '//rights.tsp.test.sankuai.com',
      test: '//rights.tsp.test.sankuai.com',
      stage: '//rights-apigw.tsp.st.sankuai.com',
      prod: '//rights-apigw.meituan.com'
    };
    
    let url = '';
    
    if (Environment.isLocal && Mach.env.isH5) {
      url = urlMap.local + path;
    } else if (Environment.isTest) {
      url = urlMap.test + path;
    } else if (Environment.isStage) {
      url = urlMap.stage + path;
    } else {
      url = urlMap.prod + path;
    }
    
    if (!Mach.env.isH5) {
      url = 'https:' + url;
    }
    
    return url;
  }

  // 時間戳轉換
  const convertTimestamp = function (timestamp) {
    return timestamp && timestamp < 1000 ? parseInt(String(timestamp * 1000000), 10) : parseInt(timestamp, 10);
  };

  // 獲取請求參數
  async function getRequestParams(activityId, instanceId = '', isWaimaiApi) {
    let cType = '';
    let riskParams = {
      fpPlatform: '',
      wxOpenId: '',
      appVersion: ''
    };

    try {
      cType = await GundamUtil.getCtypeValue();
      riskParams = await GundamUtil.getRiskParams();
    } catch (error) {
      console.log(error);
    }

    const baseParams = isWaimaiApi ? {
      appVersion: riskParams.appVersion
    } : {
      fpPlatform: riskParams.fpPlatform,
      wx_openid: riskParams.wxOpenId,
      appVersion: riskParams.appVersion
    };

    const queryParams = GundamUtil.qs();
    const longitude = queryParams.wm_longitude || 0;
    const latitude = queryParams.wm_latitude || 0;
    const actualLatitude = queryParams.wm_actual_latitude || 0;
    const actualLongitude = queryParams.wm_actual_longitude || 0;

    let location = { lat: 0, lng: 0 };
    try {
      location = await GundamFeature.location();
    } catch (error) {
      console.log('【神券節限時搶玩法組件】 gdFeature.location獲取定位信息出錯');
    }

    const finalLatitude = convertTimestamp(Number(latitude) || location.lat);
    const finalLongitude = convertTimestamp(Number(longitude) || location.lng);
    const finalActualLatitude = convertTimestamp(Number(actualLatitude) || location.lat);
    const finalActualLongitude = convertTimestamp(Number(actualLongitude) || location.lng);

    const globalData = GundamUtil.getGlobalData();

    return {
      activityId: activityId,
      gdId: globalData.gdId,
      pageId: globalData.pageId,
      instanceId: instanceId,
      cType: cType,
      ...baseParams,
      latitude: finalLatitude,
      longitude: finalLongitude,
      actualLatitude: finalActualLatitude,
      actualLongitude: finalActualLongitude,
      appid: getWxAppId()
    };
  }

  // URL參數序列化
  const serializeParams = function (params) {
    return Object.keys(params).map(function (key) {
      return `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`;
    }).join('&');
  };

  // 深拷貝對象（排除dom屬性）
  function deepClone(obj) {
    if (typeof obj !== 'object' || obj === null) return obj;

    const cloned = Array.isArray(obj) ? [] : {};
    Object.keys(obj).forEach(function (key) {
      if (key !== 'dom' && obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    });

    return cloned;
  }

  // 登錄處理
  const handleLogin = function () {
    GundamFeature.login().then(function () {
      GundamUtil.reload();
    }).catch(function (error) {
      console.log('login err', error);
    });
  };

  // 券樣式配置
  const COUPON_STYLES_NORMAL = {
    '1': {
      bgSrc: 'https://p1.meituan.net/dptakeaway/803636e963880a3e20d8ff6c2386cded15943.png',
      btnSrc: 'https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png',
      greyBtnSrc: 'https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png',
      bgStyle: { height: '192rpx', width: '654rpx' },
      btnStyle: { height: '64rpx', width: '160rpx', top: '64rpx', right: '24rpx' }
    },
    '2': {
      bgSrc: 'https://p0.meituan.net/dptakeaway/cc358d46923a3e7cc070a7ff6d2455b81802.png',
      btnSrc: 'https://p0.meituan.net/dptakeaway/b6965689f2b53d213f0d07a00f8b224712407.png',
      greyBtnSrc: 'https://p1.meituan.net/dptakeaway/93c96ff73c053ce60722fa1239f62c417258.png',
      bgStyle: { height: '192rpx', width: '323rpx' },
      btnStyle: { height: '44rpx', width: '299rpx', left: '12rpx', bottom: '12rpx' }
    },
    '3': {
      bgSrc: 'https://p1.meituan.net/dptakeaway/89fb4165026bc44898aedffcfd4225ba1624.png',
      btnSrc: 'https://p0.meituan.net/dptakeaway/4578a9f7619239a937135db75a7621e78377.png',
      greyBtnSrc: 'https://p0.meituan.net/dptakeaway/b35268acc04cd46e3dee466be21e206d5110.png',
      bgStyle: { height: '192rpx', width: '213rpx' },
      btnStyle: { height: '40rpx', width: '189rpx', left: '12rpx', bottom: '12rpx' }
    }
  };

  const COUPON_STYLES_MULTIPLE = {
    '1': {
      bgSrc: 'https://p0.meituan.net/dptakeaway/7560b5e98cb7516f17bd72e1a9f09b2812143.png',
      btnSrc: 'https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png',
      greyBtnSrc: 'https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png',
      bgStyle: { height: '140rpx', width: '654rpx' },
      btnStyle: { height: '64rpx', width: '160rpx', top: '38rpx', right: '24rpx' }
    },
    '2': {
      bgSrc: 'https://p0.meituan.net/dptakeaway/4eec8a9e40a93db7210b2764c2d98bbc1629.png',
      btnSrc: 'https://p0.meituan.net/dptakeaway/9ba2b4f20164ce3c8db75101438ad79f12264.png',
      greyBtnSrc: 'https://p0.meituan.net/dptakeaway/d8daab6b2047692a41b631c1948deb1c7333.png',
      bgStyle: { height: '162rpx', width: '323rpx' },
      btnStyle: { height: '44rpx', width: '307rpx', left: '8rpx', bottom: '8rpx' }
    },
    '3': {
      bgSrc: 'https://p1.meituan.net/dptakeaway/15819d000a62221efe319bf03648e4c11498.png',
      btnSrc: 'https://p1.meituan.net/dptakeaway/9c0bbb0cb1df52871e469811f150e9a18526.png',
      greyBtnSrc: 'https://p0.meituan.net/dptakeaway/471f4ef0f3c07ddd37a6d6083e63f6635196.png',
      bgStyle: { height: '162rpx', width: '213rpx' },
      btnStyle: { height: '40rpx', width: '197rpx', left: '8rpx', bottom: '8rpx' }
    }
  };

  // URL處理函數
  const processUrl = function (url) {
    const globalData = GundamUtil.getGlobalData();
    const baseUrl = url.split('?')[0];
    let urlParams = GundamUtils.qs(url);
    const currentParams = GundamUtils.qs();

    const defaultParams = {
      utm_term: globalData.gdId || ''
    };

    const sourceParams = {
      utm_source: ''
    };

    Object.keys(currentParams).forEach(function (key) {
      if (key === 'utm_source') {
        sourceParams.utm_source = currentParams[key];
      }
    });

    urlParams = { ...urlParams, ...defaultParams, ...sourceParams };

    let finalUrl = baseUrl;
    Object.keys(urlParams).forEach(function (key) {
      const separator = finalUrl.split('?')[1] ? '&' : '?';
      finalUrl += `${separator}${key}=${urlParams[key]}`;
    });

    return finalUrl;
  };

  // 顯示券使用提示
  const showCouponUsageTip = function (couponType) {
    if (couponType === CouponType.ToStore) {
      if (Environment.isWxMpWm || Environment.isWmApp) {
        showToast('請打開美團app-我的-紅包卡券中查看');
      }
      if (Environment.isMtApp || Environment.isWxMpMtWm) {
        showToast('請在我的-紅包卡券中查看');
      }
      if (Environment.isDpApp) {
        showToast('請在我的-卡券中查看');
      }
    }

    if (couponType === CouponType.WaiMai) {
      showToast('請前往我的-紅包卡券中查看並使用');
    }
  };

  // 頁面跳轉處理
  async function handlePageNavigation(url, couponType) {
    if (!url) {
      showCouponUsageTip(couponType);
      return;
    }

    // 處理特殊協議URL
    if (Environment.isKSWm ||
        url.startsWith('https://') ||
        url.startsWith('http://') ||
        url.startsWith('meituanwaimai://') ||
        url.startsWith('imeituan://') ||
        url.startsWith('dianping://') ||
        url.startsWith('meituanshangou://')) {
      GundamUtil.redirectTo(url);
      return;
    }

    // 微信小程序跳轉
    const isWxMiniProgram = await Environment.isWxMp();
    if (isWxMiniProgram) {
      const wxBridge = GundamCore.gdBridge.getBridge().wx;

      if (url === '/pages/index/index') {
        const processedUrl = processUrl(url);
        try {
          wxBridge.miniProgram.switchTab({
            url: processedUrl,
            success: function (result) {
              console.log(`跳轉小程序成功:${processedUrl}`);
            },
            fail: function (error) {
              showCouponUsageTip(couponType);
              console.log(`跳轉小程序失敗:${processedUrl},${error}`);
            }
          });
        } catch (error) {
          console.log(`跳轉小程序失敗:${processedUrl},${error}`);
        }
      } else {
        try {
          wxBridge.miniProgram.navigateTo({
            url: url,
            success: function (result) {
              console.log(`跳轉小程序成功:${url}`);
            },
            fail: function (error) {
              showCouponUsageTip(couponType);
              console.log(`跳轉小程序失敗:${url},${error}`);
            }
          });
        } catch (error) {
          console.log(`跳轉小程序失敗:${url},${error}`);
        }
      }
    } else {
      GundamUtil.redirectTo(url);
    }
  }

  // 獲取輪次狀態
  const getRoundStatus = function (currentTime, timeRange) {
    const status = {
      status: '',
      statusText: ''
    };

    if (timeRange && timeRange.length >= 2) {
      status.status = RoundStatus.WillBegin;
      status.statusText = '距開始';

      if (currentTime >= timeRange[0]) {
        status.status = RoundStatus.IsDoing;
        status.statusText = '搶券中';
      }

      if (currentTime >= timeRange[1]) {
        status.status = RoundStatus.Miss;
        status.statusText = '已結束';
      }
    }

    return status;
  };

  // 時間戳處理
  function processTimestamp(timestamp) {
    return timestamp.toString().length < 13 ? timestamp * 1000 : timestamp;
  }

  // 生成時間戳數組
  function generateTimeStamps(timeStrings, baseDate) {
    const date = new Date(baseDate);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const isH5 = Mach.env.isH5;
    const dateString = isH5 ? `${year}/${month}/${day}` : `${year}-${month}-${day}`;

    return timeStrings.map(function (timeString) {
      const separator = isH5 ? ' ' : 'T';
      return new Date(dateString + separator + timeString).getTime();
    });
  }

  // 數字補零
  function padZero(number) {
    return number.toString().padStart(2, '0');
  }

  // 倒計時計算
  function calculateCountdown(seconds) {
    if (seconds <= 0) {
      return { h: '00', m: '00', s: '00' };
    }

    const totalSeconds = Math.ceil(seconds);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const remainingSeconds = totalSeconds % 60;

    return {
      h: padZero(hours),
      m: padZero(minutes),
      s: padZero(remainingSeconds)
    };
  }

  // 格式化日期顯示
  function formatDateDisplay(timestamp) {
    if (!timestamp) return '';

    const date = new Date(processTimestamp(timestamp));
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${month}月${day}日`;
  }

  // 時間字符串轉秒數
  const timeStringToSeconds = function (timeString) {
    const parts = timeString.split(':').map(Number);
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  };

  // 券狀態計算
  function calculateCouponStatus(couponData, roundStatus, isInUseTime, remindStatus, enableRemind) {
    if (couponData.status === GrabStatus.Success) {
      if (couponData.couponStatus === CouponUseStatus.NotUse) {
        return isInUseTime ? CouponStatus.InUse : CouponStatus.ToUse;
      }
      if (couponData.couponStatus === CouponUseStatus.Used) {
        return CouponStatus.Used;
      }
      if (couponData.couponStatus === CouponUseStatus.OverTime) {
        return CouponStatus.OverTime;
      }
    }

    if (couponData.status === GrabStatus.ReachedLimit) {
      return CouponStatus.ReceivedAlready;
    }

    if (roundStatus === RoundStatus.WillBegin) {
      if (couponData.status === GrabStatus.CantGrabRep) {
        return CouponStatus.CantCatch;
      }
      if (enableRemind) {
        return remindStatus ? CouponStatus.Reminded : CouponStatus.RemindMe;
      }
      return CouponStatus.WillBegin;
    }

    if (roundStatus === RoundStatus.IsDoing) {
      if (couponData.status === GrabStatus.CantGrabRep) {
        return CouponStatus.CantCatch;
      }
      if (couponData.status === GrabStatus.GrabOver) {
        return CouponStatus.GrabOver;
      }
      if (couponData.status === GrabStatus.NotJoin) {
        return CouponStatus.CanCatch;
      }
      return CouponStatus.GrabOver;
    }

    if (roundStatus === RoundStatus.Miss) {
      return CouponStatus.Miss;
    }

    return undefined;
  }

  // 檢查是否為18號券
  function isEighteenCoupon(startTime, endTime) {
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    const isEighteenDay = startDate.getDate() === 18 && endDate.getDate() === 18;

    return isEighteenDay &&
           startDate.getFullYear() === endDate.getFullYear() &&
           startDate.getMonth() === endDate.getMonth();
  }

  // 券信息處理Hook
  const useCouponInfo = function (currentTime, couponData, roundStatus, remindStatus, enableRemind) {
    const [couponInfo, setCouponInfo] = React.useState(couponData);
    const serverTimeRef = React.useRef(0);
    const lastUpdateTimeRef = React.useRef(0);
    const timerRef = React.useRef(null);

    const updateCouponInfo = React.useCallback(function (data) {
      const updatedData = deepClone(data);
      let needTimer = false;

      if (serverTimeRef.current > 0) {
        const now = Date.now();
        serverTimeRef.current += now - lastUpdateTimeRef.current;
        const currentServerTime = serverTimeRef.current;
        lastUpdateTimeRef.current = now;

        // 處理時間戳
        updatedData.couponStartTime = processTimestamp(updatedData.couponStartTime);
        updatedData.couponEndTime = processTimestamp(updatedData.couponEndTime);
        updatedData.couponUseTime = processTimestamp(updatedData.couponUseTime);

        const timeToEnd = updatedData.couponEndTime - currentServerTime;

        // 格式化顯示時間
        updatedData.showStartTime = formatDateDisplay(updatedData.couponStartTime);
        updatedData.showEndTime = formatDateDisplay(updatedData.couponEndTime);
        updatedData.showUseTime = formatDateDisplay(updatedData.couponUseTime);

        // 檢查是否在使用時間內
        if (currentServerTime >= updatedData.couponStartTime && currentServerTime <= updatedData.couponEndTime) {
          needTimer = true;
          const countdown = calculateCountdown(timeToEnd / 1000);
          updatedData.countDownDate = `${countdown.h}:${countdown.m}:${countdown.s}`;
          updatedData.isOneDay = timeToEnd <= 86400000; // 24小時
        }

        // 檢查是否過期
        if (currentServerTime > updatedData.couponEndTime) {
          needTimer = false;
          updatedData.couponStatus = CouponUseStatus.OverTime;
        }

        // 計算券狀態
        updatedData.showStatus = calculateCouponStatus(
          updatedData,
          roundStatus,
          needTimer,
          remindStatus,
          enableRemind
        );

        // 計算進度
        const isFinished = roundStatus === RoundStatus.Miss || updatedData.showStatus === CouponStatus.GrabOver;
        updatedData.progressPercent = isFinished ?
          100 :
          Math.round((data.totalStock - data.residueStock) / data.totalStock * 100);

        if (updatedData.progressPercent > 100) updatedData.progressPercent = 100;
        if (updatedData.progressPercent < 0 || !updatedData.progressPercent) updatedData.progressPercent = 0;

        // 計算進度條寬度
        const isWillBeginOrMiss = [RoundStatus.WillBegin, RoundStatus.Miss].includes(roundStatus);
        const isGrabOver = updatedData.showStatus === CouponStatus.GrabOver;

        if (isWillBeginOrMiss || isGrabOver) {
          updatedData.processWidth = '0%';
        } else if (roundStatus !== RoundStatus.WillBegin && updatedData.progressPercent < 10) {
          updatedData.processWidth = '10%';
        } else {
          updatedData.processWidth = `${updatedData.progressPercent}%`;
        }

        // 進度文本
        if (roundStatus === RoundStatus.WillBegin) {
          updatedData.grabProcessText = '未開始';
        } else if (updatedData.progressPercent < 28) {
          updatedData.grabProcessText = '瘋搶中';
        } else if (updatedData.progressPercent >= 28 && updatedData.progressPercent < 85) {
          updatedData.grabProcessText = `${updatedData.progressPercent}%`;
        } else if (updatedData.progressPercent >= 85 && updatedData.progressPercent < 100) {
          updatedData.grabProcessText = '即將搶完';
        } else if (roundStatus === RoundStatus.Miss) {
          updatedData.grabProcessText = '已結束';
        } else {
          updatedData.grabProcessText = '已搶完';
        }

        // 樣式狀態
        updatedData.grabProcessIsGrayStyle = [CouponStatus.GrabOver, CouponStatus.Miss].includes(updatedData.showStatus);
        updatedData.isShowGrabProcess = roundStatus === RoundStatus.Miss ||
          [CouponStatus.CanCatch, CouponStatus.GrabOver, CouponStatus.Miss].includes(updatedData.showStatus);

        // 檢查18號券
        updatedData.is18Coupon = isEighteenCoupon(updatedData.couponStartTime, updatedData.couponEndTime);
      }

      setCouponInfo(updatedData);
    }, [roundStatus, enableRemind, remindStatus]);

    React.useEffect(function () {
      serverTimeRef.current = currentTime;
      lastUpdateTimeRef.current = Date.now();
      updateCouponInfo(couponData);

      timerRef.current = setInterval(function () {
        updateCouponInfo(couponData);
      }, 1000);

      return function () {
        clearInterval(timerRef.current);
      };
    }, [couponData, roundStatus, currentTime, updateCouponInfo]);

    return { couponItemInfo: couponInfo };
  };

  // 券組件渲染邏輯（簡化版本）
  const CouponComponent = function (props) {
    const {
      couponItemData,
      isMultipleTimeTabs,
      sqjTagIcon,
      oneLineCouponNum,
      currentTime,
      currentGrabStatus,
      activeRoundTimeStatus,
      handelGetAwardCoupon,
      remindStatus,
      handleRemind,
      enableRemind,
      index,
      btnTextMap,
      logView,
      activeTabIndex,
      logClick
    } = props;

    const { couponItemInfo } = useCouponInfo(
      currentTime,
      couponItemData,
      activeRoundTimeStatus,
      remindStatus,
      enableRemind
    );

    const currentStatus = React.useMemo(function () {
      return couponItemInfo.showStatus;
    }, [couponItemInfo]);

    const handleClick = React.useCallback(function () {
      reportClick();

      if ([CouponStatus.CantCatch, CouponStatus.ReceivedAlready].includes(currentStatus)) {
        if (couponItemInfo.toastMsg) {
          showToast(couponItemInfo.toastMsg);
        }
      } else if (currentStatus === CouponStatus.RemindMe || currentStatus === CouponStatus.Reminded) {
        handleRemind();
      } else if (currentStatus === CouponStatus.CanCatch) {
        handelGetAwardCoupon({
          rightCode: couponItemInfo.rightCode,
          handleLogClick: function (logData) {
            logClick('b_waimai_u6bmx6y1_mc', {
              tab_id: activeTabIndex,
              tab_index: activeTabIndex,
              coupon_id: couponItemInfo.couponId,
              right_code: couponItemInfo.rightCode,
              vp_seckill_type: { isDoing: 0, willBegin: 1, miss: 2 }[activeRoundTimeStatus],
              vp_stock_type: +(couponItemInfo.status !== GrabStatus.GrabOver),
              status: logData.status,
              index: index,
              has_result: logData.result
            });
          }
        });
      } else if (currentStatus === CouponStatus.InUse) {
        const logStatus = +(couponItemInfo.status === GrabStatus.Success);
        logClick('b_waimai_u6bmx6y1_mc', {
          tab_id: activeTabIndex,
          tab_index: activeTabIndex,
          coupon_id: couponItemInfo.couponId,
          right_code: couponItemInfo.rightCode,
          vp_seckill_type: { isDoing: 0, willBegin: 1, miss: 2 }[activeRoundTimeStatus],
          vp_stock_type: +(couponItemInfo.status !== GrabStatus.GrabOver),
          status: logStatus,
          index: index,
          has_result: 0
        });
        handlePageNavigation(couponItemInfo.couponDirectLink, couponItemInfo.couponAssetType);
      } else if (currentStatus === CouponStatus.ToUse) {
        showToast('還未到可用時間~');
      }
    }, [
      couponItemInfo,
      currentStatus,
      handelGetAwardCoupon,
      handleRemind,
      activeTabIndex,
      activeRoundTimeStatus,
      index,
      logClick
    ]);

    // 這裡應該返回JSX，但為了簡化，我們只返回一個描述對象
    return {
      couponInfo: couponItemInfo,
      currentStatus: currentStatus,
      handleClick: handleClick,
      // 實際的JSX渲染邏輯會在這裡
      render: function () {
        return `券組件 - 狀態: ${currentStatus}, 金額: ${couponItemInfo.couponAmount}`;
      }
    };
  };

  return {
    // 導出主要組件和工具函數
    generateClassName,
    CouponType,
    Platform,
    DeviceType,
    GrabStatus,
    RoundStatus,
    CouponStatus,
    CouponUseStatus,
    IconType,
    OneLineType,
    ErrorType,
    STATUS_TEXT_MAP,
    reportError,
    showToast,
    handleGrabSuccess,
    reportClick,
    buildApiUrl,
    convertTimestamp,
    getRequestParams,
    serializeParams,
    deepClone,
    handleLogin,
    COUPON_STYLES_NORMAL,
    COUPON_STYLES_MULTIPLE,
    processUrl,
    showCouponUsageTip,
    handlePageNavigation,
    getRoundStatus,
    processTimestamp,
    generateTimeStamps,
    padZero,
    calculateCountdown,
    formatDateDisplay,
    timeStringToSeconds,
    calculateCouponStatus,
    isEighteenCoupon,
    useCouponInfo,
    CouponComponent
  };
});
