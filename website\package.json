{"name": "website", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "typecheck": "nuxi typecheck", "release": "bumpp && tsx scripts/release.ts", "postinstall": "nuxt prepare"}, "dependencies": {"deob": "workspace:*", "fflate": "^0.8.1", "json-to-ast": "^2.1.0", "json5": "^2.2.3", "memfs": "^4.6.0", "monaco-editor": "^0.44.0", "splitpanes": "^3.1.5"}, "devDependencies": {"@iconify-json/ri": "^1.1.12", "@iconify-json/vscode-icons": "^1.1.29", "@nuxt/devtools": "latest", "@types/json-to-ast": "^2.1.4", "@types/splitpanes": "^2.2.5", "@unocss/nuxt": "^0.57.4", "@unocss/transformer-variant-group": "^0.57.4", "@vueuse/nuxt": "^10.6.1", "nuxt": "^3.8.1", "nuxt-monaco-editor": "^1.2.3", "vite-plugin-node-polyfills": "^0.16.0", "vue": "^3.3.8", "vue-router": "^4.2.5"}}