(function (_0x36b819, _0x5367fa) {
  function _0xc04109(_0x3901f7, _0x4850c6, _0x36c2ad, _0x1f8df9) {
    return _0x4b57(_0x1f8df9 - -0xca, _0x36c2ad);
  }
  var _0x2d7027 = _0x36b819();
  function _0x302195(_0x54f0e1, _0x16faf3, _0x390e41, _0x43f473) {
    return _0x4b57(_0x43f473 - 0x1fd, _0x16faf3);
  }
  while (!![]) {
    try {
      var _0x506c83 = parseInt(_0x302195(0x3ab, 0x3b8, 0x3b3, 0x397)) / (-0xe * 0x279 + 0x2 * -0x5dd + 0x2e59) * (-parseInt(_0xc04109(0xa5, 0x98, 0xb2, 0xb9)) / (-0x10 * -0x12b + -0x172c + 0x47e)) + parseInt(_0xc04109(0xa5, 0xbc, 0xad, 0xb0)) / (-0x26ba + 0x965 * 0x1 + 0x1d58) * (parseInt(_0xc04109(0x8e, 0xa4, 0x8f, 0xac)) / (-0x20a3 + 0x10 * 0xb4 + 0x1 * 0x1567)) + parseInt(_0xc04109(0xc3, 0xae, 0xb4, 0xc2)) / (0xb8e + -0xf * 0xde + 0x179) * (-parseInt(_0x302195(0x390, 0x384, 0x373, 0x38f)) / (0xe8 + 0x1e4e + 0x1f30 * -0x1)) + -parseInt(_0xc04109(0xad, 0xad, 0x97, 0x98)) / (0x2 * -0x8b3 + 0x15 * 0x1e + 0xef7) * (-parseInt(_0x302195(0x36d, 0x372, 0x359, 0x367)) / (-0x1afc + 0x18d2 + 0x232)) + parseInt(_0x302195(0x371, 0x3a2, 0x36a, 0x387)) / (0x1 * -0x202d + 0x1 * -0xdf3 + 0x2e29) + parseInt(_0xc04109(0xb5, 0xc5, 0xd3, 0xcb)) / (-0x13d9 + 0x9eb + 0x9f8) * (-parseInt(_0x302195(0x353, 0x378, 0x37e, 0x363)) / (-0x152b + 0x21bb + 0x281 * -0x5)) + parseInt(_0x302195(0x38e, 0x3ae, 0x37e, 0x398)) / (-0x1131 + -0xdfa + 0x1 * 0x1f37) * (-parseInt(_0x302195(0x399, 0x3a3, 0x399, 0x394)) / (-0x15bf + 0x31d * -0x1 + 0x38f * 0x7));
      if (_0x506c83 === _0x5367fa) break;else _0x2d7027['push'](_0x2d7027['shift']());
    } catch (_0x4dbf71) {
      _0x2d7027['push'](_0x2d7027['shift']());
    }
  }
})(_0x4e5f, -0xccffd * -0x1 + 0xd5157 + -0xe7645);
function _0x4b57(_0x577169, _0x4d3616) {
  var _0x1be6d5 = _0x4e5f();
  return _0x4b57 = function (_0x413cee, _0x15fa4e) {
    _0x413cee = _0x413cee - (0x1 * -0x49d + 0x399 + 0x265 * 0x1);
    var _0x39d90b = _0x1be6d5[_0x413cee];
    if (_0x4b57['JqJTIE'] === undefined) {
      var _0x1a1df0 = function (_0x1b9943) {
        var _0x44677c = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';
        var _0xc90986 = '',
          _0x2a918d = '';
        for (var _0x27c78d = -0x122 * -0x14 + 0x5da + 0x1 * -0x1c82, _0x4d628c, _0x3ebe34, _0x317b3b = 0x179 * -0x19 + -0x15 * -0xb1 + 0x1 * 0x164c; _0x3ebe34 = _0x1b9943['charAt'](_0x317b3b++); ~_0x3ebe34 && (_0x4d628c = _0x27c78d % (0x1963 * -0x1 + -0x72f + 0x1 * 0x2096) ? _0x4d628c * (-0x2 * 0x2de + -0xa08 + 0x1004) + _0x3ebe34 : _0x3ebe34, _0x27c78d++ % (0xc7 * -0x20 + 0x25f4 + -0xd10)) ? _0xc90986 += String['fromCharCode'](0x1 * -0x2f2 + -0xab7 * -0x1 + -0x6c6 & _0x4d628c >> (-(0x1 * -0x10da + -0x17c0 + 0x289c) * _0x27c78d & -0x19e2 + -0x1 * -0x106e + 0x97a)) : -0x5 * -0x598 + 0xb * 0x348 + -0x4010) {
          _0x3ebe34 = _0x44677c['indexOf'](_0x3ebe34);
        }
        for (var _0x30faf2 = -0x16a3 + 0x64 * 0x3f + -0x1f9, _0xc2c3bc = _0xc90986['length']; _0x30faf2 < _0xc2c3bc; _0x30faf2++) {
          _0x2a918d += '%' + ('00' + _0xc90986['charCodeAt'](_0x30faf2)['toString'](0x329 * 0xb + -0x26 * 0x3d + -0x521 * 0x5))['slice'](-(-0x3 * -0x881 + -0x1 * 0xf1c + -0x377 * 0x3));
        }
        return decodeURIComponent(_0x2a918d);
      };
      _0x4b57['llNKUm'] = _0x1a1df0, _0x577169 = arguments, _0x4b57['JqJTIE'] = !![];
    }
    var _0x43b524 = _0x1be6d5[0xc82 + -0x2 * -0x319 + -0x12b4],
      _0x4e28da = _0x413cee + _0x43b524,
      _0x167082 = _0x577169[_0x4e28da];
    return !_0x167082 ? (_0x39d90b = _0x4b57['llNKUm'](_0x39d90b), _0x577169[_0x4e28da] = _0x39d90b) : _0x39d90b = _0x167082, _0x39d90b;
  }, _0x4b57(_0x577169, _0x4d3616);
}
function _0x4e5f() {
  var _0x4d29d7 = ['vxLduuS', 'E30Uy29UC3rYDq', 'y29UC29Szq', 'wwjxAxK', 'mhWZFdr8mxWYFa', 'qunvt3G', 'z1vREvG', 'rLHTq0y', 'n1rhAvHxva', 'sM5Qrfa', 'rNnNvfy', 'yvPPCwi', 'nZDXr1HAwem', 'yxbWBhK', 'z0vmtLe', 'EM9xDgO', 'mJqXodK5mNvwqKzYsq', 'AKnRu20', 'x19WCM90B19F', 'vLfWsMW', 'q1z2s28', 'BgvUz3rO', 'BvLzsfG', 'zfzODwm', 'ChjVDg90ExbL', 'D2fYBG', 'CM4GDgHPCYiPka', 'renLsfO', 'ntKWme5yD3nisW', 'C3bSAxq', 'zxHJzxb0Aw9U', 'D0PZqvm', 'mJC3oeztu0XKrW', 'y29UC3rYDwn0BW', 'qwPewNu', 'zxjYB3i', 'Ag5Zzxq', 'v0rvAeu', 'u0DgzeK', 'AvfIvLy', 'D0Xovue', 'mtCWnNnIrNP2vG', 'yMLUza', 's1DTCwS', 'sgvSBg8Gv29YBa', 'BerLCNO', 'tufeueC', 'DgfIBgu', 'mte5odKYntfwuNjHu1u', 'z3jXquC', 'mJaWnZm1CgfIr0jl', 'shfKreC', 'q2HsDMG', 'sufbz0y', 'yw9SCMW', 'svDnwem', 'ndHJr2XnsfO', 'Bg9N', 'Dg9tDhjPBMC', 'odaYmJiWCKf6vNf0', 'CMv0DxjUicHMDq', 'mJK4mdK1mLjYv2TuuW', 'uw5uvwm', 'DhjHy2u', 'mJqYuMjntMzA', 'nJbwC2XvDvm', 'y3rVCIGICMv0Dq'];
  _0x4e5f = function () {
    return _0x4d29d7;
  };
  return _0x4e5f();
}
function hi() {
  function _0x588477(_0x1781d6, _0x73ebed, _0x504684, _0x5f0168) {
    return _0x4b57(_0x1781d6 - 0x349, _0x73ebed);
  }
  var _0x31a68e = {
      'zoWtj': _0x557424(0x26a, 0x269, 0x257, 0x260),
      'SGFdI': 'FoKQm',
      'wLNUA': function (_0x394564, _0x480da2) {
        return _0x394564 !== _0x480da2;
      },
      'YbWiy': _0x588477(0x4d8, 0x4d4, 0x4e2, 0x4e5),
      'gELNQ': function (_0x484132, _0x106295) {
        return _0x484132 + _0x106295;
      },
      'ChRvh': _0x557424(0x2a9, 0x2a8, 0x285, 0x289) + 'nction()\x20',
      'MADPG': _0x557424(0x25d, 0x265, 0x27f, 0x278),
      'DCeHZ': function (_0x1cf8b7, _0x4512d3) {
        return _0x1cf8b7 !== _0x4512d3;
      },
      'cmFKI': _0x588477(0x4aa, 0x490, 0x4bf, 0x488),
      'WDUhE': function (_0x34adf5, _0x3d0770) {
        return _0x34adf5(_0x3d0770);
      },
      'CVvKo': function (_0x1db337, _0x2c4712) {
        return _0x1db337 + _0x2c4712;
      },
      'bSZaR': _0x557424(0x277, 0x275, 0x2a1, 0x291) + _0x588477(0x4e5, 0x4f2, 0x4f2, 0x4f2) + _0x557424(0x250, 0x26f, 0x26f, 0x267) + '\x20)',
      'KSAlG': function (_0x5c08e7, _0x25eed3) {
        return _0x5c08e7 === _0x25eed3;
      },
      'zwxzW': _0x588477(0x4c7, 0x4cf, 0x4df, 0x4c0),
      'lDerz': _0x557424(0x245, 0x284, 0x282, 0x263),
      'ACUOx': function (_0x1a5d2e) {
        return _0x1a5d2e();
      },
      'dVhuc': _0x557424(0x2a0, 0x283, 0x278, 0x286),
      'UyCQK': _0x588477(0x4bc, 0x4db, 0x4d5, 0x4a2),
      'IWMXC': _0x557424(0x291, 0x25a, 0x25a, 0x270),
      'aolrl': _0x557424(0x28b, 0x292, 0x26c, 0x27c),
      'ClhBR': _0x557424(0x281, 0x281, 0x2a3, 0x28c),
      'gUkyX': function (_0x26aa8b, _0x1a9ec5) {
        return _0x26aa8b < _0x1a9ec5;
      },
      'wJsAS': function (_0x14af43, _0x2049da) {
        return _0x14af43 !== _0x2049da;
      },
      'aZiqb': _0x588477(0x4e1, 0x4fe, 0x4c6, 0x4d9),
      'jCkSm': _0x588477(0x4ea, 0x4f3, 0x4fc, 0x4fb) + '5',
      'grqAG': _0x557424(0x259, 0x296, 0x26e, 0x279) + 'd!'
    },
    _0x13918c = function () {
      var _0x5601ab = {};
      function _0x48bde4(_0x38aa9b, _0x237d6c, _0x563ee1, _0x4f2bde) {
        return _0x557424(_0x38aa9b - 0x4a, _0x237d6c, _0x563ee1 - 0x181, _0x38aa9b - 0x246);
      }
      _0x5601ab['UywnN'] = _0x31a68e[_0x48bde4(0x4a2, 0x488, 0x4a9, 0x487)], _0x5601ab[_0x48bde4(0x4b5, 0x4c2, 0x4a1, 0x4b1)] = _0x31a68e[_0x294cfa(-0x18f, -0x189, -0x18f, -0x19d)];
      function _0x294cfa(_0x95d0a4, _0x2b4d9c, _0x1a5bbe, _0x54c4d3) {
        return _0x588477(_0x1a5bbe - -0x658, _0x54c4d3, _0x1a5bbe - 0x40, _0x54c4d3 - 0xc9);
      }
      var _0x49afe4 = function (_0x254ae1, _0x559602, _0x3dfa50, _0x21855f, _0x13ee81) {
        return _0x4698(_0x13ee81 - -674, _0x3dfa50);
      };
      var _0x42856f = _0x5601ab;
      if (_0x31a68e['wLNUA'](_0x31a68e[_0x48bde4(0x4d9, 0x4e9, 0x4d2, 0x4df)], _0x31a68e['YbWiy'])) {
        var _0x5e750e = _0x5e3bfb[_0x48bde4(0x4a0, 0x4b9, 0x4ad, 0x494)](_0x34aa6b, arguments);
        return _0x24359e = null, _0x5e750e;
      } else {
        var _0x7159b6 = !![];
        return function (_0x330efc, _0x46a7b3) {
          var _0x7fa39c = _0x7159b6 ? function () {
            function _0x25a8b0(_0x21187d, _0x52f148, _0x4048b3, _0x436085) {
              return _0x4b57(_0x4048b3 - 0x313, _0x21187d);
            }
            function _0x39f18d(_0x19097e, _0x10c36f, _0x1ea555, _0x487882) {
              return _0x4b57(_0x487882 - -0x141, _0x10c36f);
            }
            if (_0x42856f['UywnN'] !== _0x42856f[_0x39f18d(0x54, 0x5c, 0x1e, 0x3b)]) {
              if (_0x46a7b3) {
                var _0x3d6c1e = _0x46a7b3[_0x39f18d(0x11, 0x22, 0x1e, 0x26)](_0x330efc, arguments);
                return _0x46a7b3 = null, _0x3d6c1e;
              }
            } else _0x22db6b = _0x4bbd53;
          } : function () {};
          return _0x7159b6 = ![], _0x7fa39c;
        };
      }
    }();
  function _0x557424(_0x132859, _0x324157, _0x5094ec, _0xc47d7a) {
    return _0x4b57(_0xc47d7a - 0xf3, _0x324157);
  }
  var _0x6cbcff = _0x13918c(this, function () {
    var _0x5b4336 = {
        'JnjDP': function (_0x5a2ec6, _0x31f876) {
          return _0x5a2ec6(_0x31f876);
        },
        'FsgTV': function (_0x304f17, _0x38f5d1) {
          return _0x31a68e['gELNQ'](_0x304f17, _0x38f5d1);
        },
        'iQbVV': _0x31a68e[_0x5c7c8e(-0x1ca, -0x1b2, -0x1ac, -0x19e)],
        'HqdDG': _0x31a68e['bSZaR']
      },
      _0x2bba98 = function () {
        var _0xe69083 = {
          'IabtK': function (_0x1fcdcc, _0x5c0be6) {
            function _0x804055(_0xd3b87d, _0x23c2a7, _0x4597d6, _0x24f270) {
              return _0x4b57(_0xd3b87d - -0x15a, _0x23c2a7);
            }
            return _0x31a68e[_0x804055(0xe, 0x4, 0x24, 0x29)](_0x1fcdcc, _0x5c0be6);
          },
          'ivvXM': function (_0x1452f9, _0x55f18d) {
            return _0x31a68e['gELNQ'](_0x1452f9, _0x55f18d);
          },
          'UDxIx': _0x31a68e[_0x1d94f8(0x24e, 0x26c, 0x257, 0x24d)]
        };
        function _0x270a64(_0x2008c0, _0x4dc7d9, _0x1b1ac9, _0x1ba7a3) {
          return _0x5c7c8e(_0x2008c0 - 0xb0, _0x4dc7d9 - 0xe5, _0x4dc7d9 - 0x66f, _0x2008c0);
        }
        function _0x1d94f8(_0x565f85, _0x220072, _0x579013, _0x464588) {
          return _0x5c7c8e(_0x565f85 - 0x11a, _0x220072 - 0xb6, _0x579013 - 0x403, _0x220072);
        }
        if (_0x31a68e[_0x270a64(0x4b1, 0x4b7, 0x4a7, 0x4c6)](_0x31a68e[_0x1d94f8(0x24c, 0x253, 0x251, 0x262)], _0x31a68e[_0x1d94f8(0x231, 0x236, 0x251, 0x25d)])) _0x36c667 = _0x2b4c83(DdgdbA['IabtK'](DdgdbA['ivvXM'](DdgdbA['UDxIx'], _0x1d94f8(0x279, 0x26f, 0x267, 0x27e) + _0x270a64(0x4d3, 0x4d1, 0x4f1, 0x4e1) + _0x1d94f8(0x254, 0x25b, 0x23d, 0x21d) + '\x20)'), ');'))();else {
          var _0x6f0388;
          try {
            if (_0x31a68e[_0x1d94f8(0x236, 0x222, 0x23e, 0x24d)](_0x31a68e['cmFKI'], 'JEvas')) _0x6f0388 = _0x31a68e[_0x1d94f8(0x242, 0x258, 0x248, 0x228)](Function, _0x31a68e[_0x270a64(0x499, 0x49d, 0x492, 0x48d)](_0x31a68e[_0x1d94f8(0x229, 0x23c, 0x237, 0x22d)](_0x31a68e[_0x270a64(0x4aa, 0x4c3, 0x4d3, 0x4c2)], _0x31a68e['bSZaR']), ');'))();else {
              var _0x5a6f56 = _0x40defb ? function () {
                function _0x8ea9c4(_0x28725d, _0x534eb0, _0x5dcbcd, _0x57e070) {
                  return _0x1d94f8(_0x28725d - 0xb9, _0x5dcbcd, _0x57e070 - 0x3a, _0x57e070 - 0x159);
                }
                if (_0x24e3b2) {
                  var _0x3f7b59 = _0x237167[_0x8ea9c4(0x288, 0x26b, 0x280, 0x26a)](_0x4b4d1b, arguments);
                  return _0x269724 = null, _0x3f7b59;
                }
              } : function () {};
              return _0x542fef = ![], _0x5a6f56;
            }
          } catch (_0x10880c) {
            if (_0x31a68e['KSAlG'](_0x31a68e['zwxzW'], _0x31a68e[_0x1d94f8(0x25f, 0x23d, 0x250, 0x246)])) {
              var _0x5f7acf = _0x506f16[_0x1d94f8(0x249, 0x233, 0x244, 0x247) + 'r'][_0x270a64(0x4a4, 0x4a7, 0x4c2, 0x4b5)][_0x270a64(0x498, 0x4b9, 0x4d1, 0x4ca)](_0x7d4867),
                _0xc56e0 = _0x4ed3a6[_0x1d3f2a],
                _0x4982de = _0x3457ce[_0xc56e0] || _0x5f7acf;
              _0x5f7acf[_0x1d94f8(0x24b, 0x242, 0x235, 0x239)] = _0x25f351[_0x1d94f8(0x231, 0x232, 0x24d, 0x236)](_0x592499), _0x5f7acf[_0x1d94f8(0x26f, 0x26a, 0x25d, 0x25e)] = _0x4982de[_0x270a64(0x4c6, 0x4c9, 0x4c3, 0x4be)]['bind'](_0x4982de), _0x2b5296[_0xc56e0] = _0x5f7acf;
            } else _0x6f0388 = window;
          }
          return _0x6f0388;
        }
      };
    function _0x5c7c8e(_0x3f0f84, _0x5e4578, _0x410143, _0x1ce8db) {
      return _0x557424(_0x3f0f84 - 0xdf, _0x1ce8db, _0x410143 - 0x12b, _0x410143 - -0x42d);
    }
    var _0x45bfdc = _0x31a68e[_0x38b7d1(0x408, 0x3ed, 0x3e7, 0x407)](_0x2bba98),
      _0x5d2030 = _0x45bfdc['console'] = _0x45bfdc[_0x5c7c8e(-0x187, -0x1b9, -0x19b, -0x1a9)] || {},
      _0x4a4cd0 = [_0x31a68e[_0x38b7d1(0x3c8, 0x3bc, 0x3a0, 0x3b0)], _0x31a68e[_0x38b7d1(0x3f9, 0x3e8, 0x3de, 0x3f5)], 'info', _0x31a68e[_0x5c7c8e(-0x18b, -0x1a9, -0x1a9, -0x19e)], _0x38b7d1(0x3cb, 0x3c3, 0x3ae, 0x3ce), _0x31a68e[_0x5c7c8e(-0x195, -0x1af, -0x1aa, -0x19c)], _0x31a68e['ClhBR']];
    function _0x38b7d1(_0x3f27ff, _0x38444a, _0x45b3f8, _0x32c69e) {
      return _0x588477(_0x38444a - -0xfe, _0x45b3f8, _0x45b3f8 - 0xe, _0x32c69e - 0xfa);
    }
    for (var _0xbcb23b = 0x8 * 0x71 + 0x9a * -0x5 + -0x86; _0x31a68e[_0x38b7d1(0x3fb, 0x3ee, 0x3db, 0x3e1)](_0xbcb23b, _0x4a4cd0[_0x5c7c8e(-0x1df, -0x1e3, -0x1cb, -0x1d4)]); _0xbcb23b++) {
      if (_0x31a68e[_0x5c7c8e(-0x1bb, -0x1bb, -0x1c1, -0x1de)](_0x31a68e[_0x38b7d1(0x3ae, 0x3b0, 0x3b3, 0x39e)], _0x31a68e['aZiqb'])) {
        var _0x406dbc;
        try {
          _0x406dbc = GpJdGj[_0x38b7d1(0x3cb, 0x3ae, 0x3b5, 0x394)](_0x2e907d, GpJdGj['FsgTV'](GpJdGj[_0x5c7c8e(-0x1cc, -0x1f6, -0x1d6, -0x1d1)](GpJdGj[_0x38b7d1(0x3be, 0x3cc, 0x3e2, 0x3bc)], GpJdGj[_0x38b7d1(0x3c4, 0x3d8, 0x3fa, 0x3ce)]), ');'))();
        } catch (_0x1e80ef) {
          _0x406dbc = _0x51d375;
        }
        return _0x406dbc;
      } else {
        var _0x3ca3d2 = _0x31a68e[_0x5c7c8e(-0x1c8, -0x1b7, -0x1cf, -0x1c9)][_0x38b7d1(0x3e1, 0x3c2, 0x3da, 0x3ad)]('|'),
          _0x4af79f = 0x5 * 0x99 + 0x1bf0 + -0x1eed;
        while (!![]) {
          switch (_0x3ca3d2[_0x4af79f++]) {
            case '0':
              var _0x12c43a = _0x13918c[_0x5c7c8e(-0x1c4, -0x1b1, -0x1bf, -0x1b8) + 'r'][_0x38b7d1(0x3c9, 0x3bd, 0x3aa, 0x3bb)][_0x38b7d1(0x3bf, 0x3cf, 0x3dc, 0x3c4)](_0x13918c);
              continue;
            case '1':
              _0x12c43a[_0x5c7c8e(-0x1d1, -0x1b6, -0x1ce, -0x1c5)] = _0x13918c[_0x5c7c8e(-0x1d5, -0x1a9, -0x1b6, -0x1a3)](_0x13918c);
              continue;
            case '2':
              _0x12c43a[_0x5c7c8e(-0x1b1, -0x1b2, -0x1a6, -0x18b)] = _0x1faa06[_0x38b7d1(0x3fa, 0x3df, 0x3ff, 0x3fa)]['bind'](_0x1faa06);
              continue;
            case '3':
              var _0x545650 = _0x4a4cd0[_0xbcb23b];
              continue;
            case '4':
              var _0x1faa06 = _0x5d2030[_0x545650] || _0x12c43a;
              continue;
            case '5':
              _0x5d2030[_0x545650] = _0x12c43a;
              continue;
          }
          break;
        }
      }
    }
  });
  _0x31a68e[_0x557424(0x298, 0x29f, 0x2af, 0x295)](_0x6cbcff), console[_0x588477(0x4dc, 0x4d0, 0x4d2, 0x4c1)](_0x31a68e[_0x557424(0x268, 0x26a, 0x271, 0x27e)]);
}
hi();