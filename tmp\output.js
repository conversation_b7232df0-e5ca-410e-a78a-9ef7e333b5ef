(function (e, t) {
  if (typeof exports == "object" && typeof module != "undefined") {
    module.exports = t(require("react"), require("@gundam/gundam-core"), require("@gundam/gundam-ui"), require("@gundam/gundam-utils"), require("react/jsx-runtime"), require("@gundam/gundam-base-components"));
  } else if (typeof define == "function" && define.amd) {
    define(["react", "@gundam/gundam-core", "@gundam/gundam-ui", "@gundam/gundam-utils", "react/jsx-runtime", "@gundam/gundam-base-components"], t);
  } else {
    (e = typeof globalThis != "undefined" ? globalThis : e || self).__component_output__ = t(e.<PERSON><PERSON>, e.GundamCore, e.GundamUI, e.GundamUtils, e.<PERSON>act, e.GundamBaseComponents);
  }
})(this, function (e, t, n, r, i, o) {
  "use strict";

  const c = {
    "1ybe2o": {
      "component-wrapper": "bo7z1",
      "content-wrapper-outer": "QilV5",
      "content-wrapper": "WNoBO",
      "layer-img-container": "V7tAK",
      "layer-img": "YLsCy",
      "rule-btn": "_4ELS8",
      "rule-btn-bg": "-RJEV",
      "rule-btn-text": "Jj3kc",
      "coupons-line-wrapper": "GHnBV",
      "coupon-lines": "VU8Pm"
    },
    "1dezzt": {
      "coupon-wrapper": "cdzYm",
      "grab-style": "ke98Q",
      "sqj-icon-wrapper": "_4VJuw",
      "sqj-icon": "esb-l",
      "price-wrapper": "c1hmE",
      "price-wrapper-1": "wl-tA",
      "price-wrapper-1-isAndroid": "xxxyB",
      "price-wrapper-1-multi": "WA7tP",
      "price-wrapper-1-multi-isAndroid": "DZ7ZA",
      "price-wrapper-2": "O1iuo",
      "price-wrapper-2-multi": "lfLRy",
      "price-wrapper-2-multi-isAndroid": "GZXGc",
      "price-wrapper-3": "MPav9",
      "price-wrapper-3-multi": "nsd5g",
      "price-wrapper-3-multi-H5": "DuHRD",
      "price-wrapper-3-multi-isAndroid": "stHKf",
      "price-content": "_6E2Dr",
      "price-unit": "FE0QO",
      "price-unit-1": "_5quBR",
      "price-unit-2": "SFzwU",
      "price-unit-3": "mxQPn",
      "price-number": "k3LMP",
      "price-number-1": "iiL-i",
      "price-number-2": "MXFbS",
      "price-number-3": "AI9RG",
      "price-limit": "j1k9V",
      "price-limit-1": "L9f9f",
      "price-limit-1-isAndroid": "_5z0ft",
      "price-limit-2": "fCYZK",
      "price-limit-2-multi": "brv6d",
      "price-limit-3": "-PkfV",
      "price-limit-3-multi": "W4t1n",
      "coupon-info": "LjACW",
      "coupon-info-1": "Agpbs",
      "coupon-info-1-multi": "z-vdD",
      "coupon-info-2": "_1cggL",
      "coupon-info-2-multi": "_4bowN",
      "coupon-info-3": "KE6Lm",
      "coupon-info-3-multi": "w9kaE",
      "coupon-icon-1": "tEkrM",
      "coupon-icon-2": "QEhXx",
      "coupon-icon-3": "Cx0k2",
      "coupon-name-3": "_0xR7h",
      "coupon-title": "z5SQU",
      "coupon-title-1": "a-Lrg",
      "coupon-title-2": "_7Fpi9",
      "coupon-title-3": "X7Hbr",
      "coupon-desc-text": "Y5yCO",
      "coupon-desc-text-red": "WuXUV",
      "coupon-desc-text-1": "houwq",
      "coupon-desc-text-2": "CNnOZ",
      "coupon-desc-text-3": "vUwPR",
      "coupon-desc-1": "_1vjeS",
      "coupon-desc-1-multi": "w7JrD",
      "coupon-desc-2": "_9pwOE",
      "coupon-desc-2-multi": "txF1V",
      "coupon-desc-3": "eh43s",
      "coupon-desc-3-H5": "_4JfNV",
      "coupon-desc-3-multi": "HLFQ8",
      "coupon-desc-3-multi-H5": "QonJJ",
      "progress-bar": "HX-vX",
      "progress-bar-1": "pniD1",
      "progress-bar-2": "_1scuO",
      "progress-bar-outer": "M8Hig",
      "progress-bar-outer-gray": "jGMmD",
      "progress-bar-inner": "YJoMG",
      "progress-bar-text": "uThXg",
      "progress-bar-text-gray": "fqsA-",
      btn: "fNthf",
      "btn-disable": "Ad9mC",
      "btn-remindme": "AT6eZ",
      "btn-remindme-done": "-ZdLj",
      "btn-bg": "xotAf",
      "btn-text": "Si6Wc",
      "btn-text-disable": "SO0Lm",
      "btn-text-1": "hg9CX",
      "btn-text-2": "VUi0e",
      "btn-text-3": "rIX7i",
      "eighteen-tag": "mEiVc",
      "eighteen-tag-multiple-time": "_3Ep9L"
    },
    "11oojf": {
      "list-1": "ezvKs",
      "list-1-time": "IIgVT",
      "list-1-time-isAndroid": "LHM5y",
      "list-1-time-desc": "VtXxq",
      "list-1-time-desc-isAndroid": "_9--xS",
      "list-1-count-down-txt": "HsC7X",
      "list-1-count-down-content": "D9q7s",
      "list-1-hms": "nsvqX",
      "list-1-colon": "GDFLV",
      "list-n": "uos5M",
      "list-n-scroll": "XcZDm",
      "list-n-item": "_0-T-U",
      "list-n-item-time": "EvSyQ",
      "list-n-item-status-txt": "lE3Qo",
      "list-n-item-status-txt-doing": "lRIid",
      "list-n-item-count-down": "bk-2y",
      "list-n-item-count-down-hms": "xUqe6",
      "list-n-item-count-down-colon": "t3TKG"
    },
    "26x95f": {
      "empty-card-wrapper": "WzSQm",
      "empty-img": "ON1jm",
      "text-wrapper": "SBXcy",
      "empty-text-tip": "c17v8"
    },
    "12kyc3": {
      "login-bg": "ma38v",
      "login-text-tip": "hRb-I",
      "login-btn": "-V8F1",
      "net-error-img": "KTqrZ"
    },
    "1r4gew": {
      rule: "-jSHi",
      "rule-wrap": "bkR7s",
      "rule-area": "ZHmJS",
      "rule-title": "DL-Wu",
      "rule-close-btn": "Z9pYA",
      "rule-text": "RWKHk",
      "rule-text-scroller": "JBZy1"
    }
  };
  function a() {
    try {
      const e = Array.prototype.slice.call(arguments);
      const t = c;
      if (!e.length) {
        return "";
      }
      const n = e.join("__");
      if (globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n]) {
        return globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n];
      }
      const r = e.shift();
      const i = r.split(" ").map(e => e.trim()).filter(e => !!e);
      if (!i.length) {
        return "";
      }
      const o = i.map(n => {
        const r = n;
        let i = "";
        e.some(e => {
          const n = t[e] || {};
          if (n[r]) {
            i = n[r];
            return true;
          }
        });
        return i;
      }).filter(e => !!e).join(" ");
      globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n] = r + " " + o;
      return globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n];
    } catch (e) {
      console.log(e.message);
    }
    return "";
  }
  function s(e) {
    if (e && typeof e == "object" && "default" in e) {
      return e;
    } else {
      return {
        default: e
      };
    }
  }
  globalThis.gdcgdlimitedtimegrabcoupon0020 = {};
  globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache = {};
  var u = s(t);
  function l(e, t) {
    if (t == null || t > e.length) {
      t = e.length;
    }
    for (var n = 0, r = Array(t); n < t; n++) {
      r[n] = e[n];
    }
    return r;
  }
  function d(e, t, n, r, i, o, c) {
    try {
      var a = e[o](c);
      var s = a.value;
    } catch (e) {
      n(e);
      return;
    }
    if (a.done) {
      t(s);
    } else {
      Promise.resolve(s).then(r, i);
    }
  }
  function f(e) {
    return function () {
      var t = this;
      var n = arguments;
      return new Promise(function (r, i) {
        var o = e.apply(t, n);
        function c(e) {
          d(o, r, i, c, a, "next", e);
        }
        function a(e) {
          d(o, r, i, c, a, "throw", e);
        }
        c(undefined);
      });
    };
  }
  function m(e, t, n) {
    t = v(t);
    return function (e, t) {
      if (t && (typeof t == "object" || typeof t == "function")) {
        return t;
      }
      if (t !== undefined) {
        throw new TypeError("Derived constructors may only return object or undefined");
      }
      return function (e) {
        if (e === undefined) {
          throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        }
        return e;
      }(e);
    }(e, w() ? Reflect.construct(t, n || [], v(e).constructor) : t.apply(e, n));
  }
  function h(e, t) {
    if (!(e instanceof t)) {
      throw new TypeError("Cannot call a class as a function");
    }
  }
  function p(e, t) {
    for (var n = 0; n < t.length; n++) {
      var r = t[n];
      r.enumerable = r.enumerable || false;
      r.configurable = true;
      if ("value" in r) {
        r.writable = true;
      }
      Object.defineProperty(e, A(r.key), r);
    }
  }
  function g(e, t, n) {
    if (t) {
      p(e.prototype, t);
    }
    if (n) {
      p(e, n);
    }
    Object.defineProperty(e, "prototype", {
      writable: false
    });
    return e;
  }
  function b(e, t, n) {
    if ((t = A(t)) in e) {
      Object.defineProperty(e, t, {
        value: n,
        enumerable: true,
        configurable: true,
        writable: true
      });
    } else {
      e[t] = n;
    }
    return e;
  }
  function v(e) {
    v = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (e) {
      return e.__proto__ || Object.getPrototypeOf(e);
    };
    return v(e);
  }
  function y(e, t) {
    if (typeof t != "function" && t !== null) {
      throw new TypeError("Super expression must either be null or a function");
    }
    e.prototype = Object.create(t && t.prototype, {
      constructor: {
        value: e,
        writable: true,
        configurable: true
      }
    });
    Object.defineProperty(e, "prototype", {
      writable: false
    });
    if (t) {
      C(e, t);
    }
  }
  function w() {
    try {
      var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));
    } catch (e) {}
    return (w = function () {
      return !!e;
    })();
  }
  function x(e, t) {
    var n = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
      var r = Object.getOwnPropertySymbols(e);
      if (t) {
        r = r.filter(function (t) {
          return Object.getOwnPropertyDescriptor(e, t).enumerable;
        });
      }
      n.push.apply(n, r);
    }
    return n;
  }
  function k(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t] != null ? arguments[t] : {};
      if (t % 2) {
        x(Object(n), true).forEach(function (t) {
          b(e, t, n[t]);
        });
      } else if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(e, Object.getOwnPropertyDescriptors(n));
      } else {
        x(Object(n)).forEach(function (t) {
          Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
        });
      }
    }
    return e;
  }
  function T() {
    /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */
    var e;
    var t;
    var n = typeof Symbol == "function" ? Symbol : {};
    var r = n.iterator || "@@iterator";
    var i = n.toStringTag || "@@toStringTag";
    function o(n, r, i, o) {
      var s = r && r.prototype instanceof a ? r : a;
      var u = Object.create(s.prototype);
      S(u, "_invoke", function (n, r, i) {
        var o;
        var a;
        var s;
        var u = 0;
        var l = i || [];
        var d = false;
        var f = {
          p: 0,
          n: 0,
          v: e,
          a: m,
          f: m.bind(e, 4),
          d: function (t, n) {
            o = t;
            a = 0;
            s = e;
            f.n = n;
            return c;
          }
        };
        function m(n, r) {
          a = n;
          s = r;
          t = 0;
          for (; !d && u && !i && t < l.length; t++) {
            var i;
            var o = l[t];
            var m = f.p;
            var h = o[2];
            if (n > 3) {
              if (i = h === r) {
                s = o[(a = o[4]) ? 5 : (a = 3, 3)];
                o[4] = o[5] = e;
              }
            } else if (o[0] <= m) {
              if (i = n < 2 && m < o[1]) {
                a = 0;
                f.v = r;
                f.n = o[1];
              } else if (m < h && (i = n < 3 || o[0] > r || r > h)) {
                o[4] = n;
                o[5] = r;
                f.n = h;
                a = 0;
              }
            }
          }
          if (i || n > 1) {
            return c;
          }
          d = true;
          throw r;
        }
        return function (i, l, h) {
          if (u > 1) {
            throw TypeError("Generator is already running");
          }
          if (d && l === 1) {
            m(l, h);
          }
          a = l;
          s = h;
          for (; (t = a < 2 ? e : s) || !d;) {
            if (!o) {
              if (a) {
                if (a < 3) {
                  if (a > 1) {
                    f.n = -1;
                  }
                  m(a, s);
                } else {
                  f.n = s;
                }
              } else {
                f.v = s;
              }
            }
            try {
              u = 2;
              if (o) {
                if (!a) {
                  i = "next";
                }
                if (t = o[i]) {
                  if (!(t = t.call(o, s))) {
                    throw TypeError("iterator result is not an object");
                  }
                  if (!t.done) {
                    return t;
                  }
                  s = t.value;
                  if (a < 2) {
                    a = 0;
                  }
                } else {
                  if (a === 1 && (t = o.return)) {
                    t.call(o);
                  }
                  if (a < 2) {
                    s = TypeError("The iterator does not provide a '" + i + "' method");
                    a = 1;
                  }
                }
                o = e;
              } else if ((t = (d = f.n < 0) ? s : n.call(r, f)) !== c) {
                break;
              }
            } catch (t) {
              o = e;
              a = 1;
              s = t;
            } finally {
              u = 1;
            }
          }
          return {
            value: t,
            done: d
          };
        };
      }(n, i, o), true);
      return u;
    }
    var c = {};
    function a() {}
    function s() {}
    function u() {}
    t = Object.getPrototypeOf;
    var l = [][r] ? t(t([][r]())) : (S(t = {}, r, function () {
      return this;
    }), t);
    var d = u.prototype = a.prototype = Object.create(l);
    function f(e) {
      if (Object.setPrototypeOf) {
        Object.setPrototypeOf(e, u);
      } else {
        e.__proto__ = u;
        S(e, i, "GeneratorFunction");
      }
      e.prototype = Object.create(d);
      return e;
    }
    s.prototype = u;
    S(d, "constructor", u);
    S(u, "constructor", s);
    s.displayName = "GeneratorFunction";
    S(u, i, "GeneratorFunction");
    S(d);
    S(d, i, "Generator");
    S(d, r, function () {
      return this;
    });
    S(d, "toString", function () {
      return "[object Generator]";
    });
    return (T = function () {
      return {
        w: o,
        m: f
      };
    })();
  }
  function S(e, t, n, r) {
    var i = Object.defineProperty;
    try {
      i({}, "", {});
    } catch (e) {
      i = 0;
    }
    S = function (e, t, n, r) {
      if (t) {
        if (i) {
          i(e, t, {
            value: n,
            enumerable: !r,
            configurable: !r,
            writable: !r
          });
        } else {
          e[t] = n;
        }
      } else {
        function o(t, n) {
          S(e, t, function (e) {
            return this._invoke(t, n, e);
          });
        }
        o("next", 0);
        o("throw", 1);
        o("return", 2);
      }
    };
    S(e, t, n, r);
  }
  function C(e, t) {
    C = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (e, t) {
      e.__proto__ = t;
      return e;
    };
    return C(e, t);
  }
  function j(e, t) {
    return function (e) {
      if (Array.isArray(e)) {
        return e;
      }
    }(e) || function (e, t) {
      var n = e == null ? null : typeof Symbol != "undefined" && e[Symbol.iterator] || e["@@iterator"];
      if (n != null) {
        var r;
        var i;
        var o;
        var c;
        var a = [];
        var s = true;
        var u = false;
        try {
          o = (n = n.call(e)).next;
          if (t === 0) {
            if (Object(n) !== n) {
              return;
            }
            s = false;
          } else {
            for (; !(s = (r = o.call(n)).done) && (a.push(r.value), a.length !== t); s = true);
          }
        } catch (e) {
          u = true;
          i = e;
        } finally {
          try {
            if (!s && n.return != null && (c = n.return(), Object(c) !== c)) {
              return;
            }
          } finally {
            if (u) {
              throw i;
            }
          }
        }
        return a;
      }
    }(e, t) || function (e, t) {
      if (e) {
        if (typeof e == "string") {
          return l(e, t);
        }
        var n = {}.toString.call(e).slice(8, -1);
        if (n === "Object" && e.constructor) {
          n = e.constructor.name;
        }
        if (n === "Map" || n === "Set") {
          return Array.from(e);
        } else if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) {
          return l(e, t);
        } else {
          return undefined;
        }
      }
    }(e, t) || function () {
      throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
    }();
  }
  function A(e) {
    var t = function (e, t) {
      if (typeof e != "object" || !e) {
        return e;
      }
      var n = e[Symbol.toPrimitive];
      if (n !== undefined) {
        var r = n.call(e, t || "default");
        if (typeof r != "object") {
          return r;
        }
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return (t === "string" ? String : Number)(e);
    }(e, "string");
    if (typeof t == "symbol") {
      return t;
    } else {
      return t + "";
    }
  }
  function I(e) {
    I = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function (e) {
      return typeof e;
    } : function (e) {
      if (e && typeof Symbol == "function" && e.constructor === Symbol && e !== Symbol.prototype) {
        return "symbol";
      } else {
        return typeof e;
      }
    };
    return I(e);
  }
  function M(e, t) {
    if (t === undefined) {
      t = {};
    }
    var n = t.insertAt;
    if (e && typeof document != "undefined") {
      var r = document.head || document.getElementsByTagName("head")[0];
      var i = document.createElement("style");
      i.type = "text/css";
      if (n === "top" && r.firstChild) {
        r.insertBefore(i, r.firstChild);
      } else {
        r.appendChild(i);
      }
      if (i.styleSheet) {
        i.styleSheet.cssText = e;
      } else {
        i.appendChild(document.createTextNode(e));
      }
    }
  }
  var P;
  var O;
  var R;
  var N;
  var G;
  var z;
  M(`.cdzYm {
  position: relative;
  margin-top: 0.1rem;
}
.ke98Q {
  opacity: 0.4;
}
._4VJuw {
  width: 0.59rem;
  height: 0.31rem;
  border-top-left-radius: 0.15rem;
  border-bottom-right-radius: 0.16rem;
  overflow: hidden;
  position: absolute;
  top: 0.01rem;
  left: 0.01rem;
}
.esb-l {
  width: 0.59rem;
  height: 0.31rem;
}
.c1hmE {
  flex-direction: column;
  align-items: center;
}
.wl-tA {
  width: 1.7rem;
  margin-top: 0.46rem;
  padding-left: 0.08rem;
}
.xxxyB {
  margin-top: 0.5rem;
}
.WA7tP {
  margin-top: 0.24rem;
  padding-left: 0.1rem;
}
.DZ7ZA {
  margin-top: 0.27rem;
}
.O1iuo {
  width: 1.26rem;
  margin-top: 0.42rem;
  padding-left: 0.05rem;
}
.lfLRy {
  margin-top: 0.3rem;
}
.GZXGc {
  margin-top: 0.32rem;
}
.MPav9 {
  width: 2.13rem;
  margin-top: 0.12rem;
}
.nsd5g {
  margin-top: 0.07rem;
}
.DuHRD {
  margin-top: 0.1rem;
}
.stHKf {
  margin-top: 0.1rem;
}
._6E2Dr {
  align-items: baseline;
}
.FE0QO {
  font-family: AvenirLTPro-Heavy;
  color: #FF2D19;
}
._5quBR {
  font-size: 0.28rem;
  line-height: 0.3rem;
  font-weight: 700;
}
.SFzwU {
  font-size: 0.24rem;
  line-height: 0.26rem;
  font-weight: 800;
}
.mxQPn {
  font-size: 0.22rem;
  line-height: 0.24rem;
  font-weight: 800;
}
.k3LMP {
  font-family: AvenirLTPro-Heavy;
  color: #FF2D19;
}
.iiL-i {
  font-size: 0.6rem;
  line-height: 0.62rem;
  font-weight: 700;
}
.MXFbS {
  font-size: 0.44rem;
  line-height: 0.46rem;
  font-weight: 800;
}
.AI9RG {
  font-size: 0.36rem;
  line-height: 0.38rem;
  font-weight: 700;
}
.j1k9V {
  font-family: PingFangSC-Regular;
  font-size: 22px;
  color: #FF2D19;
  letter-spacing: 0;
  text-align: center;
  line-height: 24px;
  font-weight: 400;
}
.L9f9f {
  font-size: 0.24rem;
  line-height: 0.26rem;
}
._5z0ft {
  margin-top: -0.02rem;
}
.fCYZK {
  font-size: 0.22rem;
  line-height: 0.24rem;
}
.brv6d {
  margin-top: -0.03rem;
}
.-PkfV {
  font-size: 0.22rem;
  line-height: 0.24rem;
  margin-top: -0.04rem;
}
.W4t1n {
  font-size: 0.18rem;
  line-height: 0.2rem;
  margin-top: -0.05rem;
}
.LjACW {
  flex-direction: column;
}
.Agpbs {
  margin-top: 0.58rem;
}
.z-vdD {
  margin-top: 0.36rem;
}
._1cggL {
  margin-top: 0.5rem;
}
._4bowN {
  margin-top: 0.41rem;
}
.KE6Lm {
  margin-top: 0.1rem;
}
.w9kaE {
  margin-top: 0.07rem;
}
.tEkrM {
  width: 0.36rem;
  height: 0.36rem;
  margin-right: 0.04rem;
  flex-shrink: 0;
}
.QEhXx {
  width: 0.26rem;
  height: 0.26rem;
  margin-right: 0.02rem;
  flex-shrink: 0;
}
.Cx0k2 {
  width: 0.22rem;
  height: 0.22rem;
  margin-right: 0.02rem;
  flex-shrink: 0;
}
._0xR7h {
  width: 100%;
  align-items: center;
  justify-content: center;
}
.z5SQU {
  font-family: PingFangSC-Medium;
  color: #222426;
  font-weight: 500;
}
.a-Lrg {
  font-size: 0.34rem;
  line-height: 0.37rem;
  width: 2.42rem;
  margin-top: 0.01rem;
}
._7Fpi9 {
  font-size: 0.26rem;
  line-height: 0.28rem;
  width: 1.56rem;
}
.X7Hbr {
  font-size: 0.22rem;
  line-height: 0.24rem;
  max-width: 1.54rem;
}
.Y5yCO {
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #858687;
  font-weight: 400;
}
.WuXUV {
  color: #FF2D19;
}
.houwq {
  max-width: 3rem;
}
.CNnOZ {
  width: 1.9rem;
  font-size: 0.22rem;
}
.vUwPR {
  font-size: 0.16rem;
  max-width: 1.9rem;
}
._1vjeS {
  margin-top: 0.05rem;
  margin-left: 0.04rem;
}
.w7JrD {
  margin-top: 0.05rem;
}
._9pwOE {
  margin-top: 0.04rem;
}
.txF1V {
  margin-top: 0;
}
.eh43s {
  width: 100%;
  align-items: center;
  justify-content: center;
  margin-top: 0.02rem;
}
._4JfNV {
  margin-top: 0.06rem;
}
.HLFQ8 {
  margin-top: -0.04rem;
}
.QonJJ {
  margin-top: -0.01rem;
}
.HX-vX {
  height: 0.24rem;
  align-items: center;
}
.pniD1 {
  margin-top: 0.06rem;
}
._1scuO {
  margin-top: 0.04rem;
}
.M8Hig {
  width: 1.14rem;
  height: 0.1rem;
  background-color: #FF4A2633;
  border-radius: 0.08rem;
  margin-right: 0.08rem;
}
.jGMmD {
  opacity: 0.5;
  background: #D3D3D3;
}
.YJoMG {
  height: 100%;
  background-color: #FF2D19;
  border-radius: 0.08rem;
}
.uThXg {
  font-family: PingFangSC-Regular;
  font-size: 0.18rem;
  color: #FF2D19;
  text-align: center;
  line-height: 0.2rem;
  font-weight: 400;
}
.fqsA- {
  color: #858687;
}
.fNthf {
  position: absolute;
  border-radius: 0.32rem;
}
.Ad9mC {
  background: #D3D3D37F;
}
.AT6eZ {
  background-image: linear-gradient(270deg, #53C03C 0%, #67CC52 100%);
}
.-ZdLj {
  opacity: 0.6;
}
.xotAf {
  width: 100%;
  height: 100%;
}
.Si6Wc {
  font-family: PingFangSC-Semibold;
  font-size: 0.28rem;
  width: 100%;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  line-height: 0.31rem;
  font-weight: 600;
}
.SO0Lm {
  color: #858687;
  opacity: 0.7;
}
.hg9CX {
  position: absolute;
  top: 0.18rem;
}
.VUi0e,
.rIX7i {
  font-family: PingFangSC-Medium;
  font-size: 0.24rem;
  line-height: 0.28rem;
  font-weight: 500;
}
.mEiVc {
  width: 1.1rem;
  height: 0.82rem;
  position: absolute;
  z-index: 2;
  top: -0.07rem;
  right: -0.18rem;
}
._3Ep9L {
  top: -0.22rem;
}
`);
  (function (e) {
    e[e.WaiMai = 1] = "WaiMai";
    e[e.ToStore = 2] = "ToStore";
  })(P || (P = {}));
  (function (e) {
    e[e.mt = 1] = "mt";
    e[e.wm = 3] = "wm";
  })(O || (O = {}));
  (function (e) {
    e[e.Android = 1] = "Android";
    e[e.IOS = 2] = "IOS";
  })(R || (R = {}));
  (function (e) {
    e[e.Fail = 0] = "Fail";
    e[e.NotJoin = 2] = "NotJoin";
    e[e.Success = 3] = "Success";
    e[e.GrabOver = 4] = "GrabOver";
    e[e.CantGrabRep = 5] = "CantGrabRep";
    e[e.ReachedLimit = 8] = "ReachedLimit";
  })(N || (N = {}));
  (function (e) {
    e.WillBegin = "willBegin";
    e.IsDoing = "isDoing";
    e.Miss = "miss";
  })(G || (G = {}));
  (function (e) {
    e.WillBegin = "willBegin";
    e.CanCatch = "canCatch";
    e.ToUse = "toUse";
    e.InUse = "inUse";
    e.Used = "used";
    e.OverTime = "overTime";
    e.GrabOver = "grabOver";
    e.Miss = "miss";
    e.RemindMe = "remindMe";
    e.Reminded = "reminded";
    e.CantCatch = "cantCatch";
    e.ReceivedAlready = "receivedAlready";
  })(z || (z = {}));
  var D;
  var U;
  var E;
  var F;
  var _ = {
    willBegin: "即将开始",
    canCatch: "立即抢券",
    toUse: "去使用",
    inUse: "去使用",
    used: "已使用",
    overTime: "已过期",
    grabOver: "已抢完",
    miss: "已结束",
    remindMe: "提醒我",
    reminded: "已提醒",
    cantCatch: "不可领取",
    receivedAlready: "已领取"
  };
  (function (e) {
    e[e.NotUse = 0] = "NotUse";
    e[e.Used = 1] = "Used";
    e[e.OverTime = 2] = "OverTime";
  })(D || (D = {}));
  (function (e) {
    e.Custom = "custom";
    e.SqjNoema = "sqjNoema";
  })(U || (U = {}));
  (function (e) {
    e.OneLine1 = "1";
    e.OneLine2 = "2";
    e.OneLine3 = "3";
  })(E || (E = {}));
  (function (e) {
    e[e.NeedLogin = 0] = "NeedLogin";
    e[e.NoInTime = 1] = "NoInTime";
    e[e.OtherAnomalies = 2] = "OtherAnomalies";
  })(F || (F = {}));
  var L = u.default.gdMonitor;
  var V = u.default.gdUtil;
  function W(e, t, n) {
    var r;
    var i;
    var o;
    var c = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : "";
    var a = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : "";
    var s = V.getGlobalData();
    L.addError({
      error: {
        name: `[gd-limited-time-grab-coupon] > [${e}-err]:code-${t == null ? undefined : t.code},subcode-${t == null || (r = t.data) === null || r === undefined ? undefined : r.subCode},status-${t == null || (i = t.data) === null || i === undefined ? undefined : i.status},roundCode-${c},rightCode-${a}`,
        msg: `神券节限时抢玩法-${n},gdId-${s.gdId},pageId-${s.pageId}`
      },
      opts: {
        category: globalThis === null || globalThis === undefined || (o = globalThis.Owl) === null || o === undefined || (o = o.errorModel) === null || o === undefined || (o = o.CATEGORY) === null || o === undefined ? undefined : o.AJAX,
        level: "warn"
      }
    });
  }
  var B = u.default.env;
  var q = u.default.gdUtil;
  var H = u.default.gdFeature;
  var X = "wxde8ac0a21135c07d";
  var J = "wx2c348cf579062e56";
  function Q() {
    var e;
    var t = String(((e = window) === null || e === undefined || (e = e.navigator) === null || e === undefined ? undefined : e.userAgent) || "").toLowerCase();
    if (B.isWxMpWm || t.includes(J)) {
      return J;
    } else if (B.isWxMpMtWm || B.isWxMpMtSg || B.isWxMpMtThh || t.includes(X)) {
      return X;
    } else {
      return "";
    }
  }
  function Z(e) {
    n.Toast.show({
      message: e
    });
  }
  function K() {
    try {
      var e;
      var t = q.getGlobalData();
      var n = t == null ? undefined : t.pageId;
      var r = t == null || (e = t.config) === null || e === undefined ? undefined : e.limitedSaleWithRefreshConfig;
      var i = r.delayTime;
      var o = r.limitedSaleWithRefreshDataList;
      if (o && o.length > 0 && o.includes(String(n))) {
        // TOLOOK
        setTimeout(function () {
          u.default.gdEvent.emit("refreshSupply", {});
        }, i);
      }
    } catch (e) {
      console.log("[gd-limited-time-grab-coupon] - handleEmitGrabSuccess - error", e);
      W("handleEmitGrabSuccess", e, "限时抢组件通知供给事件失败");
    }
  }
  function Y() {
    console.log("report click of gd-limited-time-grab-coupon");
    u.default.gdEvent.emit("wm-coupon-festival-tab-click", {});
  }
  function $(e, t) {
    var n = t ? {
      local: "//promotion.waimai.test.sankuai.com",
      test: "//promotion.waimai.test.sankuai.com",
      stage: "//promotion.waimai.st.sankuai.com",
      prod: "//promotion.waimai.meituan.com"
    } : {
      local: "//rights.tsp.test.sankuai.com",
      test: "//rights.tsp.test.sankuai.com",
      stage: "//rights-apigw.tsp.st.sankuai.com",
      prod: "//rights-apigw.meituan.com"
    };
    var r = "";
    r = B.isLocal && Mach.env.isH5 ? n.local + e : B.isTest ? n.test + e : B.isStage ? n.stage + e : (B.isProd, n.prod + e);
    if (!Mach.env.isH5) {
      r = `https:${r}`;
    }
    return r;
  }
  function ee(e) {
    if (e && e < 1000) {
      return parseInt(String(e * 1000000), 10);
    } else {
      return parseInt(e, 10);
    }
  }
  function te(e) {
    return ne.apply(this, arguments);
  }
  function ne() {
    ne = f(T().m(function e(t) {
      var n;
      var r;
      var i;
      var o;
      var c;
      var a;
      var s;
      var l;
      var d;
      var f;
      var m;
      var h;
      var p;
      var g;
      var b;
      var v;
      var y;
      var w;
      var x;
      var S;
      var C;
      var j;
      var A;
      var I;
      var M;
      var P;
      var O;
      var R = arguments;
      return T().w(function (e) {
        for (;;) {
          switch (e.n) {
            case 0:
              n = R.length > 1 && R[1] !== undefined ? R[1] : "";
              r = R.length > 2 ? R[2] : undefined;
              i = "";
              o = {
                fpPlatform: "",
                wxOpenId: "",
                appVersion: ""
              };
              e.p = 1;
              e.n = 2;
              return u.default.gdUtil.getCtypeValue();
            case 2:
              i = e.v;
              e.n = 3;
              return u.default.gdUtil.getRiskParams();
            case 3:
              o = e.v;
              e.n = 5;
              break;
            case 4:
              e.p = 4;
              O = e.v;
              console.log(O);
            case 5:
              c = r ? {
                appVersion: o.appVersion
              } : {
                fpPlatform: o.fpPlatform,
                wx_openid: o.wxOpenId,
                appVersion: o.appVersion
              };
              a = q.qs();
              s = a.wm_longitude;
              l = s === undefined ? 0 : s;
              d = a.wm_latitude;
              f = d === undefined ? 0 : d;
              m = a.wm_actual_latitude;
              h = m === undefined ? 0 : m;
              p = a.wm_actual_longitude;
              g = p === undefined ? 0 : p;
              b = {
                lat: 0,
                lng: 0
              };
              e.p = 6;
              e.n = 7;
              return H.location();
            case 7:
              b = e.v;
              e.n = 9;
              break;
            case 8:
              e.p = 8;
              console.log("【神券节限时抢玩法组件】 gdFeature.location获取定位信息出错");
            case 9:
              y = (v = b).lat;
              w = y === undefined ? 0 : y;
              x = v.lng;
              S = x === undefined ? 0 : x;
              C = ee(Number(f) || w);
              j = ee(Number(l) || S);
              A = ee(Number(h) || w);
              I = ee(Number(g) || S);
              M = q.getGlobalData();
              P = k(k({
                gdId: M.gdId,
                pageId: M.pageId,
                instanceId: n,
                cType: i
              }, c), {}, {
                latitude: C,
                longitude: j,
                actualLatitude: A,
                actualLongitude: I,
                appid: Q()
              });
              return e.a(2, k({
                activityId: t
              }, P));
          }
        }
      }, e, null, [[6, 8], [1, 4]]);
    }));
    return ne.apply(this, arguments);
  }
  function re(e) {
    return Object.keys(e).map(function (t) {
      return `${encodeURIComponent(t)}=${encodeURIComponent(e[t])}`;
    }).join("&");
  }
  function ie(e) {
    if (I(e) !== "object" || e === null) {
      return e;
    }
    var t = Array.isArray(e) ? [] : {};
    Object.keys(e).forEach(function (n) {
      if (n !== "dom" && e.hasOwnProperty(n)) {
        t[n] = ie(e[n]);
      }
    });
    return t;
  }
  function oe() {
    H.login().then(function () {
      q.reload();
    }).catch(function (e) {
      console.log("login err", e);
    });
  }
  var ce = u.default.env;
  var ae = u.default.gdBridge;
  var se = u.default.gdUtil;
  var ue = b(b(b({}, E.OneLine1, {
    bgSrc: "https://p1.meituan.net/dptakeaway/803636e963880a3e20d8ff6c2386cded15943.png",
    btnSrc: "https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png",
    greyBtnSrc: "https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png",
    bgStyle: {
      height: "192rpx",
      width: "654rpx"
    },
    btnStyle: {
      height: "64rpx",
      width: "160rpx",
      top: "64rpx",
      right: "24rpx"
    }
  }), E.OneLine2, {
    bgSrc: "https://p0.meituan.net/dptakeaway/cc358d46923a3e7cc070a7ff6d2455b81802.png",
    btnSrc: "https://p0.meituan.net/dptakeaway/b6965689f2b53d213f0d07a00f8b224712407.png",
    greyBtnSrc: "https://p1.meituan.net/dptakeaway/93c96ff73c053ce60722fa1239f62c417258.png",
    bgStyle: {
      height: "192rpx",
      width: "323rpx"
    },
    btnStyle: {
      height: "44rpx",
      width: "299rpx",
      left: "12rpx",
      bottom: "12rpx"
    }
  }), E.OneLine3, {
    bgSrc: "https://p1.meituan.net/dptakeaway/89fb4165026bc44898aedffcfd4225ba1624.png",
    btnSrc: "https://p0.meituan.net/dptakeaway/4578a9f7619239a937135db75a7621e78377.png",
    greyBtnSrc: "https://p0.meituan.net/dptakeaway/b35268acc04cd46e3dee466be21e206d5110.png",
    bgStyle: {
      height: "192rpx",
      width: "213rpx"
    },
    btnStyle: {
      height: "40rpx",
      width: "189rpx",
      left: "12rpx",
      bottom: "12rpx"
    }
  });
  var le = b(b(b({}, E.OneLine1, {
    bgSrc: "https://p0.meituan.net/dptakeaway/7560b5e98cb7516f17bd72e1a9f09b2812143.png",
    btnSrc: "https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png",
    greyBtnSrc: "https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png",
    bgStyle: {
      height: "140rpx",
      width: "654rpx"
    },
    btnStyle: {
      height: "64rpx",
      width: "160rpx",
      top: "38rpx",
      right: "24rpx"
    }
  }), E.OneLine2, {
    bgSrc: "https://p0.meituan.net/dptakeaway/4eec8a9e40a93db7210b2764c2d98bbc1629.png",
    btnSrc: "https://p0.meituan.net/dptakeaway/9ba2b4f20164ce3c8db75101438ad79f12264.png",
    greyBtnSrc: "https://p0.meituan.net/dptakeaway/d8daab6b2047692a41b631c1948deb1c7333.png",
    bgStyle: {
      height: "162rpx",
      width: "323rpx"
    },
    btnStyle: {
      height: "44rpx",
      width: "307rpx",
      left: "8rpx",
      bottom: "8rpx"
    }
  }), E.OneLine3, {
    bgSrc: "https://p1.meituan.net/dptakeaway/15819d000a62221efe319bf03648e4c11498.png",
    btnSrc: "https://p1.meituan.net/dptakeaway/9c0bbb0cb1df52871e469811f150e9a18526.png",
    greyBtnSrc: "https://p0.meituan.net/dptakeaway/471f4ef0f3c07ddd37a6d6083e63f6635196.png",
    bgStyle: {
      height: "162rpx",
      width: "213rpx"
    },
    btnStyle: {
      height: "40rpx",
      width: "197rpx",
      left: "8rpx",
      bottom: "8rpx"
    }
  });
  function de(e) {
    var t = se.getGlobalData();
    var n = e.split("?")[0];
    var i = r.qs(e);
    var o = r.qs();
    var c = {
      utm_term: t.gdId || ""
    };
    var a = {
      utm_source: ""
    };
    Object.keys(o).forEach(function (e) {
      if (e === "utm_source") {
        a.utm_source = o[e];
      }
    });
    i = k(k(k({}, i), c), a);
    Object.keys(i).forEach(function (e) {
      n += `${(n.split("?")[1] ? "&" : "?") + e}=${i[e]}`;
    });
    return n;
  }
  function fe(e) {
    if (e === P.ToStore) {
      if (u.default.env.isWxMpWm || u.default.env.isWmApp) {
        Z("请打开美团app-我的-红包卡券中查看");
      }
      if (u.default.env.isMtApp || u.default.env.isWxMpMtWm) {
        Z("请在我的-红包卡券中查看");
      }
      if (u.default.env.isDpApp) {
        Z("请在我的-卡券中查看");
      }
    }
    if (e === P.WaiMai) {
      Z("请前往我的-红包卡券中查看并使用");
    }
  }
  function me() {
    return (me = f(T().m(function e(t, n) {
      var r;
      return T().w(function (e) {
        for (;;) {
          switch (e.n) {
            case 0:
              if (t) {
                e.n = 1;
                break;
              }
              fe(n);
              return e.a(2);
            case 1:
              if (!(ce.isKSWm || t.startsWith("https://") || t.startsWith("http://") || t.startsWith("meituanwaimai://") || t.startsWith("imeituan://") || t.startsWith("dianping://") || t.startsWith("meituanshangou://"))) {
                e.n = 2;
                break;
              }
              se.redirectTo(t);
              return e.a(2);
            case 2:
              e.n = 3;
              return ce.isWxMp();
            case 3:
              if (e.v) {
                r = ae.getBridge().wx;
                if (t === "/pages/index/index") {
                  t = de(t);
                  try {
                    r.miniProgram.switchTab({
                      url: t,
                      success: function (e) {
                        console.log(`跳转小程序成功:${t}`);
                      },
                      fail: function (e) {
                        fe(n);
                        console.log(`跳转小程序失败:${t},${e}`);
                      }
                    });
                  } catch (e) {
                    console.log(`跳转小程序失败:${t},${e}`);
                  }
                } else {
                  try {
                    r.miniProgram.navigateTo({
                      url: t,
                      success: function (e) {
                        console.log(`跳转小程序成功:${t}`);
                      },
                      fail: function (e) {
                        fe(n);
                        console.log(`跳转小程序失败:${t},${e}`);
                      }
                    });
                  } catch (e) {
                    console.log(`跳转小程序失败:${t},${e}`);
                  }
                }
              } else {
                se.redirectTo(t);
              }
            case 4:
              return e.a(2);
          }
        }
      }, e);
    }))).apply(this, arguments);
  }
  function he(e, t) {
    var n = {
      status: "",
      statusText: ""
    };
    if (t && t.length >= 2) {
      n.status = G.WillBegin;
      n.statusText = "距开始";
      if (e >= t[0]) {
        n.status = G.IsDoing;
        n.statusText = "抢券中";
      }
      if (e >= t[1]) {
        n.status = G.Miss;
        n.statusText = "已结束";
      }
    }
    return n;
  }
  function pe(e) {
    if (e.toString().length < 13) {
      return e * 1000;
    } else {
      return e;
    }
  }
  function ge(e, t) {
    var n = new Date(t);
    var r = n.getFullYear();
    var i = String(n.getMonth() + 1).padStart(2, "0");
    var o = String(n.getDate()).padStart(2, "0");
    var c = Mach.env.isH5;
    var a = c ? `${r}/${i}/${o}` : `${r}-${i}-${o}`;
    return e.map(function (e) {
      return new Date(a + (c ? " " : "T") + e).getTime();
    });
  }
  function be(e) {
    return e.toString().padStart(2, "0");
  }
  function ve(e) {
    if (e <= 0) {
      return {
        h: "00",
        m: "00",
        s: "00"
      };
    }
    var t = Math.ceil(e);
    var n = Math.floor(t / 3600);
    var r = Math.floor(t % 3600 / 60);
    var i = t % 60;
    return {
      h: be(n),
      m: be(r),
      s: be(i)
    };
  }
  function ye(e) {
    if (!e) {
      return "";
    }
    var t = new Date(pe(e));
    t.getFullYear();
    var n = String(t.getMonth() + 1).padStart(2, "0");
    var r = String(t.getDate()).padStart(2, "0");
    String(t.getHours()).padStart(2, "0");
    String(t.getMinutes()).padStart(2, "0");
    String(t.getSeconds()).padStart(2, "0");
    return `${n}月${r}日`;
  }
  function we(e) {
    var t = j(e.split(":").map(Number), 3);
    return t[0] * 3600 + t[1] * 60 + t[2];
  }
  function xe(t, n, r, i, o) {
    var c = j(e.useState(n), 2);
    var a = c[0];
    var s = c[1];
    var u = e.useRef(0);
    var l = e.useRef(0);
    var d = e.useRef(null);
    var f = e.useCallback(function (e) {
      var t;
      var n;
      var c;
      var a;
      var d;
      var f = ie(e);
      var m = false;
      if (u.current > 0) {
        var h = Date.now();
        u.current += h - l.current;
        var p = u.current;
        l.current = h;
        f.couponStartTime = pe(f.couponStartTime);
        f.couponEndTime = pe(f.couponEndTime);
        f.couponUseTime = pe(f.couponUseTime);
        var g = f.couponEndTime - p;
        f.showStartTime = ye(f.couponStartTime);
        f.showEndTime = ye(f.couponEndTime);
        f.showUseTime = ye(f.couponUseTime);
        if (p >= f.couponStartTime && p <= f.couponEndTime) {
          m = true;
          var b = ve(g / 1000);
          var v = b.h;
          var y = b.m;
          var w = b.s;
          f.countDownDate = `${v}:${y}:${w}`;
          f.isOneDay = g <= 86400000;
        }
        if (p > f.couponEndTime) {
          m = false;
          f.couponStatus = D.OverTime;
        }
        f.showStatus = function (e, t, n, r, i) {
          if (e.status === N.Success) {
            if (e.couponStatus === D.NotUse) {
              if (n) {
                return z.InUse;
              } else {
                return z.ToUse;
              }
            }
            if (e.couponStatus === D.Used) {
              return z.Used;
            }
            if (e.couponStatus === D.OverTime) {
              return z.OverTime;
            }
          }
          if (e.status === N.ReachedLimit) {
            return z.ReceivedAlready;
          } else if (t === G.WillBegin) {
            if (e.status === N.CantGrabRep) {
              return z.CantCatch;
            } else if (i) {
              if (r) {
                return z.Reminded;
              } else {
                return z.RemindMe;
              }
            } else {
              return z.WillBegin;
            }
          } else if (t === G.IsDoing) {
            if (e.status === N.CantGrabRep) {
              return z.CantCatch;
            } else if (e.status === N.GrabOver) {
              return z.GrabOver;
            } else if (e.status === N.NotJoin) {
              return z.CanCatch;
            } else {
              return z.GrabOver;
            }
          } else if (t === G.Miss) {
            return z.Miss;
          } else {
            return undefined;
          }
        }(f, r, m, i, o);
        f.progressPercent = r === G.Miss || z.GrabOver === f.showStatus ? 100 : Math.round((e.totalStock - e.residueStock) / e.totalStock * 100);
        if (f.progressPercent > 100) {
          f.progressPercent = 100;
        }
        if (f.progressPercent < 0 || !f.progressPercent) {
          f.progressPercent = 0;
        }
        f.processWidth = [G.WillBegin, G.Miss].includes(r) || z.GrabOver === f.showStatus ? "0%" : r !== G.WillBegin && f.progressPercent < 10 ? "10%" : `${f.progressPercent}%`;
        f.grabProcessText = r === G.WillBegin ? "未开始" : f.progressPercent < 28 ? "疯抢中" : f.progressPercent >= 28 && f.progressPercent < 85 ? `${f.progressPercent}%` : f.progressPercent >= 85 && f.progressPercent < 100 ? "即将抢完" : r === G.Miss ? "已结束" : "已抢完";
        f.grabProcessIsGrayStyle = [z.GrabOver, z.Miss].includes(f.showStatus);
        f.isShowGrabProcess = r === G.Miss || [z.CanCatch, z.GrabOver, z.Miss].includes(f.showStatus);
        t = f.couponStartTime;
        n = f.couponEndTime;
        c = new Date(t);
        a = new Date(n);
        f.is18Coupon = !!(d = c.getDate() === 18 && a.getDate() === 18) && c.getFullYear() === a.getFullYear() && c.getMonth() === a.getMonth() && d;
      }
      s(f);
    }, [r, o, i]);
    e.useEffect(function () {
      u.current = t;
      l.current = Date.now();
      f(n);
      d.current = // TOLOOK
      setInterval(function () {
        f(n);
      }, 1000);
      return function () {
        clearInterval(d.current);
      };
    }, [n, r, t, f]);
    return {
      couponItemInfo: a
    };
  }
  var ke = {
    isDoing: 0,
    willBegin: 1,
    miss: 2
  };
  var Te = Mach.env.isH5;
  var Se = Mach.env.isAndroid;
  function Ce(n) {
    var o = n.couponItemData;
    var c = n.isMultipleTimeTabs;
    var s = n.sqjTagIcon;
    var u = n.oneLineCouponNum;
    var l = n.currentTime;
    var d = n.currentGrabStatus;
    var f = n.activeRoundTimeStatus;
    var m = n.handelGetAwardCoupon;
    var h = n.remindStatus;
    var p = n.handleRemind;
    var g = n.enableRemind;
    var v = n.index;
    var y = n.btnTextMap;
    var w = n.logView;
    var x = n.activeTabIndex;
    var T = n.logClick;
    var S = xe(l, o, f, h, g).couponItemInfo;
    var C = e.useMemo(function () {
      return S.showStatus;
    }, [S]);
    var j = e.useCallback(function (e) {
      var t = e.status;
      var n = e.result;
      T("b_waimai_u6bmx6y1_mc", {
        tab_id: x,
        tab_index: x,
        coupon_id: S.couponId,
        right_code: S.rightCode,
        vp_seckill_type: ke[f],
        vp_stock_type: +(S.status !== N.GrabOver),
        status: t,
        index: v,
        has_result: n
      });
    }, [f, x, S.couponId, S.rightCode, S.status, v, T]);
    var A = e.useCallback(function () {
      w("b_waimai_u6bmx6y1_mv", {
        tab_id: x,
        tab_index: x,
        coupon_id: S.couponId,
        vp_seckill_type: ke[d],
        vp_stock_type: +(S.status !== N.GrabOver),
        index: v,
        status: +(S.status === N.Success),
        has_result: S.status === N.Success ? 0 : 1,
        right_code: S.rightCode
      });
    }, [x, S.couponId, S.rightCode, S.status, d, v, w]);
    var I = e.useCallback(function () {
      Y();
      if ([z.CantCatch, z.ReceivedAlready].includes(C)) {
        if (S.toastMsg) {
          Z(S.toastMsg);
        }
      } else if (C !== z.RemindMe && C !== z.Reminded) {
        if (C !== z.CanCatch) {
          if (C !== z.ToUse) {
            if (C === z.InUse) {
              var e = +(S.status === N.Success);
              j({
                status: e,
                result: 0
              });
              (function (e, t) {
                me.apply(this, arguments);
              })(S.couponDirectLink, S.couponAssetType);
            }
          } else {
            Z("还未到可用时间~");
          }
        } else {
          m({
            rightCode: S.rightCode,
            handleLogClick: j
          });
        }
      } else {
        p();
      }
    }, [S.couponAssetType, S.couponDirectLink, S.rightCode, S.toastMsg, C, S.status, m, j, p]);
    var M = e.useMemo(function () {
      return function (e, t) {
        if (e) {
          return le[`${t}`];
        } else {
          return ue[`${t}`];
        }
      }(c, u) || {};
    }, [c, u]);
    var P = e.useMemo(function () {
      if ([z.CanCatch, z.InUse].includes(C)) {
        if (M == null) {
          return undefined;
        } else {
          return M.btnSrc;
        }
      } else if ([z.WillBegin, z.ToUse].includes(C)) {
        if (M == null) {
          return undefined;
        } else {
          return M.greyBtnSrc;
        }
      } else {
        return "";
      }
    }, [C, M == null ? undefined : M.btnSrc, M == null ? undefined : M.greyBtnSrc]);
    var O = e.useMemo(function () {
      if ([z.CanCatch, z.InUse, z.WillBegin, z.ToUse].includes(C) && u === 1) {
        return k(k({}, M.btnStyle), {}, {
          height: "100rpx",
          width: "160rpx"
        });
      } else {
        return M.btnStyle;
      }
    }, [C, u, M.btnStyle]);
    var R = e.useMemo(function () {
      return [z.GrabOver, z.Miss, z.Used, z.OverTime, z.CantCatch, z.ReceivedAlready].includes(C);
    }, [C]);
    var G = e.useMemo(function () {
      return [z.GrabOver, z.Used, z.OverTime, z.CantCatch, z.ReceivedAlready].includes(C);
    }, [C]);
    var D = e.useMemo(function () {
      return i.jsxs(t.View, {
        className: a(r.classNames("progress-bar", `progress-bar-${u}`), "1dezzt"),
        children: [i.jsx(t.View, {
          className: a(r.classNames("progress-bar-outer", {
            "progress-bar-outer-gray": S.grabProcessIsGrayStyle
          }), "1dezzt"),
          children: i.jsx(t.View, {
            className: a("progress-bar-inner ", "1dezzt"),
            style: {
              width: `${S.processWidth}`
            }
          })
        }), i.jsx(t.Text, {
          content: S.grabProcessText,
          className: a(r.classNames("progress-bar-text", {
            "progress-bar-text-gray": S.grabProcessIsGrayStyle
          }), "1dezzt")
        })]
      });
    }, [u, S.grabProcessIsGrayStyle, S.processWidth, S.grabProcessText]);
    var U = e.useMemo(function () {
      if (C === z.Used) {
        return i.jsx(t.Text, {
          content: `${S.showUseTime}已使用`,
          className: a(r.classNames("coupon-desc-text", `coupon-desc-text-${u}`), "1dezzt")
        });
      } else if (C === z.OverTime) {
        return i.jsx(t.Text, {
          content: `${S.showEndTime}到期`,
          className: a(r.classNames("coupon-desc-text", `coupon-desc-text-${u}`), "1dezzt")
        });
      } else if (C === z.ToUse) {
        return i.jsx(t.Text, {
          content: `${S.showStartTime}起可用`,
          className: a(r.classNames("coupon-desc-text", `coupon-desc-text-${u}`), "1dezzt")
        });
      } else if (C === z.InUse) {
        if (S.isOneDay) {
          return i.jsx(t.Text, {
            content: `${S.countDownDate}后到期`,
            className: a(r.classNames("coupon-desc-text", `coupon-desc-text-${u}`), "1dezzt")
          });
        } else {
          return i.jsx(t.Text, {
            content: `${S.showStartTime}起可用`,
            className: a(r.classNames("coupon-desc-text", `coupon-desc-text-${u}`), "1dezzt")
          });
        }
      } else if ([z.CantCatch, z.ReceivedAlready].includes(C)) {
        return S.coverMsg && i.jsx(t.Text, {
          content: S.coverMsg,
          className: a(r.classNames("coupon-desc-text", "coupon-desc-text-red", `coupon-desc-text-${u}`), "1dezzt")
        });
      } else {
        return D;
      }
    }, [C, D, S.showUseTime, S.showEndTime, S.showStartTime, S.isOneDay, S.countDownDate, S.coverMsg, u]);
    var E = e.useMemo(function () {
      return S.is18Coupon && C === z.ToUse && u === 1;
    }, [S.is18Coupon, C, u]);
    return i.jsxs(t.ImageBackground, {
      resizeMode: "scaleToFill",
      src: M.bgSrc,
      style: k(k({}, M.bgStyle), {}, {
        flexDirection: u > 2 ? "column" : "row",
        flexWrap: u > 2 ? "nowrap" : "wrap"
      }),
      className: a("coupon-wrapper", "1dezzt"),
      onClick: I,
      onView: A,
      children: [i.jsx(t.View, {
        className: a("sqj-icon-wrapper", "1dezzt"),
        children: i.jsx(t.Image, {
          resizeMode: "scaleToFill",
          src: s,
          className: a("sqj-icon", "1dezzt")
        })
      }), i.jsxs(t.View, {
        className: a(r.classNames("price-wrapper", `price-wrapper-${u}`, b(b(b(b(b({}, `price-wrapper-${u}-multi`, c), `price-wrapper-${u}-isAndroid`, Se), `price-wrapper-${u}-multi-H5`, c && Te), `price-wrapper-${u}-multi-isAndroid`, c && Se), "grab-style", G)), "1dezzt"),
        children: [i.jsxs(t.View, {
          className: a("price-content", "1dezzt"),
          children: [i.jsx(t.Text, {
            content: "¥",
            className: a(`price-unit price-unit-${u}`, "1dezzt")
          }), i.jsx(t.Text, {
            content: `${S.couponAmount}`,
            className: a(`price-number price-number-${u}`, "1dezzt")
          })]
        }), i.jsx(t.Text, {
          className: a(r.classNames("price-limit", `price-limit-${u}`, b(b({}, `price-limit-${u}-isAndroid`, Se), `price-limit-${u}-multi`, c)), "1dezzt"),
          content: S.couponAmountLimit > 0 ? `满${S.couponAmountLimit}可用` : "无门槛"
        })]
      }), i.jsxs(t.View, {
        className: a(r.classNames("coupon-info", `coupon-info-${u}`, b({}, `coupon-info-${u}-multi`, c)), "1dezzt"),
        children: [i.jsxs(t.View, {
          className: a(r.classNames(`coupon-name-${u}`, {
            "grab-style": G
          }), "1dezzt"),
          children: [i.jsx(t.Image, {
            resizeMode: "scaleToFill",
            src: S.couponIconUrl,
            className: a(`coupon-icon-${u}`, "1dezzt")
          }), i.jsx(t.Text, {
            className: a(r.classNames("coupon-title", `coupon-title-${u}`), "1dezzt"),
            content: S.couponName
          })]
        }), i.jsx(t.View, {
          className: a(r.classNames(`coupon-desc-${u}`, b({}, `coupon-desc-${u}-H5`, Te), b({}, `coupon-desc-${u}-isAndroid`, Se), b({}, `coupon-desc-${u}-multi`, c), b({}, `coupon-desc-${u}-multi-H5`, c && Te)), "1dezzt"),
          children: U
        })]
      }), i.jsx(t.View, {
        className: a(r.classNames("btn", `btn-${u}`, {
          "btn-disable": R,
          "btn-remindme": [z.RemindMe, z.Reminded].includes(C),
          "btn-remindme-done": [z.Reminded].includes(C)
        }), "1dezzt"),
        style: k(k({}, O), {}, {
          flexShrink: 0
        }),
        children: P ? i.jsx(t.ImageBackground, {
          src: P,
          className: a("btn-bg", "1dezzt"),
          resizeMode: "scaleToFill",
          children: i.jsx(t.Text, {
            onClick: I,
            content: y[C],
            className: a(r.classNames("btn-text", `btn-text-${u}`, {
              "btn-text-disable": R
            }), "1dezzt")
          })
        }) : i.jsx(t.View, {
          className: a("btn-bg", "1dezzt"),
          children: i.jsx(t.Text, {
            onClick: I,
            content: y[C],
            className: a(r.classNames("btn-text", `btn-text-${u}`, {
              "btn-text-disable": R
            }), "1dezzt")
          })
        })
      }), E && i.jsx(t.Image, {
        resizeMode: "aspectFill",
        src: "https://p0.meituan.net/dptakeaway/0c251c605452992573bd88180df8ab769791.png",
        className: a(r.classNames("eighteen-tag", {
          "eighteen-tag-multiple-time": c
        }), "1dezzt")
      })]
    });
  }
  M(`.ezvKs {
  width: 7.02rem;
  height: 0.65rem;
  justify-content: space-between;
}
.IIgVT {
  font-family: AvenirLTPro-Heavy;
  font-size: 0.32rem;
  color: #222426;
  text-align: center;
  font-weight: 700;
  margin-top: 0.3rem;
}
.LHM5y {
  margin-top: 0.25rem;
}
.VtXxq {
  font-family: PingFangSC-Medium;
  font-size: 0.24rem;
  color: #222426;
  text-align: center;
  line-height: 0.26rem;
  font-weight: 500;
  margin-top: 0.27rem;
  margin-left: 0.12rem;
}
._9--xS {
  margin-top: 0.26rem;
  margin-left: 0.16rem;
}
.HsC7X {
  font-family: PingFangSC-Medium;
  font-size: 0.24rem;
  color: #222426;
  text-align: center;
  line-height: 0.26rem;
  font-weight: 500;
  margin-right: 0.12rem;
  margin-top: 0.25rem;
}
.D9q7s {
  height: 0.36rem;
  margin-top: 0.25rem;
}
.nsvqX {
  color: #ffffff;
  background: #222426;
  border-radius: 0.08rem;
  font-weight: 600;
  font-family: PingFangSC-Semibold;
  font-size: 0.22rem;
  width: 0.36rem;
  height: 0.36rem;
  line-height: 0.36rem;
  text-align: center;
}
.GDFLV {
  color: #222426;
  width: 0.12rem;
  font-weight: 600;
  font-family: PingFangSC-Semibold;
  font-size: 0.24rem;
  text-align: center;
}
.uos5M {
  height: 0.94rem;
  padding-top: 0.22rem;
}
.XcZDm {
  width: 6.54rem;
}
._0-T-U {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  flex-shrink: 0;
}
.EvSyQ {
  font-family: PingFangSC-Medium;
  font-size: 0.32rem;
  color: #1E1818;
  text-align: center;
  line-height: 0.34rem;
  font-weight: 500;
  margin-top: 0.04rem;
}
.lE3Qo {
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #1E1818;
  text-align: center;
  font-weight: 400;
  line-height: 0.26rem;
  margin-top: 0.08rem;
}
.lRIid {
  font-weight: 500;
}
.bk-2y {
  height: 0.36rem;
  flex-shrink: 0;
}
.xUqe6 {
  background: #222426;
  border-radius: 0.08rem;
  font-weight: 600;
  font-family: PingFangSC-Semibold;
  font-size: 0.22rem;
  width: 0.36rem;
  height: 0.36rem;
  line-height: 0.36rem;
  display: inline-block;
  letter-spacing: 0;
  text-align: center;
}
.t3TKG {
  color: #222426;
  width: 0.12rem;
  font-weight: 600;
  font-family: PingFangSC-Semibold;
  font-size: 0.24rem;
  letter-spacing: 0;
  text-align: center;
}
`);
  function je(t) {
    var n = t.grabRounds;
    var i = t.getData;
    var o = t.activeRoundCode;
    var c = t.currentTime;
    var a = t.isSqjNoema;
    var s = t.autoScrollTab;
    var l = t.logClick;
    var d = e.useRef(null);
    var f = e.useRef(0);
    var m = e.useRef(0);
    var h = e.useRef(null);
    var p = e.useRef(-1);
    var g = j(e.useState([]), 2);
    var b = g[0];
    var v = g[1];
    var y = e.useCallback(function (e) {
      var t = arguments.length > 1 && arguments[1] !== undefined && arguments[1];
      Y();
      if (a && t) {
        u.default.gdEvent.emit("wm-coupon-festival-tab-show", {
          isStop: true
        });
      }
      var n = b[e];
      l("b_waimai_gouv1y6s_mc", {
        activity_time: [n.startTime, n.endTime]
      });
      i({
        roundCode: n.roundCode
      });
    }, [a, b, l, i]);
    var w = e.useMemo(function () {
      return r.throttle(y, 1000);
    }, [y]);
    var x = e.useCallback(function () {
      var e;
      var t = new Date().getTime();
      f.current += t - m.current;
      m.current = t;
      v(function (e) {
        p.current = e.findIndex(function (e) {
          return e.status === G.WillBegin;
        });
        var t = n.map(function (t, r) {
          var o;
          var c;
          var a;
          var s;
          var u;
          var l;
          var d = ge([t.startTime, t.endTime], f.current);
          var m = he(f.current, d);
          var h = m.status;
          var g = m.statusText;
          var b = 0;
          var v = e[r];
          if (h === G.Miss) {
            if ((v == null ? undefined : v.status) === G.IsDoing && r < n.length - 1) {
              var y;
              var w = (y = n[r + 1]) === null || y === undefined ? undefined : y.roundCode;
              i({
                roundCode: w
              });
            }
            p.current = r < n.length - 1 ? r + 1 : r;
            b = 0;
          }
          if (h === G.IsDoing) {
            p.current = r;
            if ((v == null ? undefined : v.status) === G.WillBegin) {
              var x;
              var T = (x = n[r]) === null || x === undefined ? undefined : x.roundCode;
              i({
                roundCode: T
              });
            }
            b = (d[1] - f.current) / 1000;
          }
          if (h === G.WillBegin) {
            b = (d[0] - f.current) / 1000;
          }
          return k(k({}, t), {}, {
            status: h,
            statusText: g,
            timeTxt: (o = t.startTime, c = j(o.split(":"), 2), a = c[0], s = a === undefined ? "" : a, u = c[1], l = u === undefined ? "" : u, `${s}:${l}`),
            countDown: ve(b)
          });
        });
        return t;
      });
      if ((e = f.current) >= ge(["23:59:59"], e)[0]) {
        // TOLOOK
        setTimeout(function () {
          i({});
        }, 900);
      }
    }, [n, i]);
    var T = e.useMemo(function () {
      var e = b.findIndex(function (e) {
        return e.roundCode === o;
      });
      if (e < 0) {
        e = b.length - 1;
      }
      return e;
    }, [o, b]);
    e.useEffect(function () {
      if (b.length >= 5) {
        // TOLOOK
        setTimeout(function () {
          s(T);
        }, 0);
      }
    }, [T, s, b.length]);
    e.useEffect(function () {
      f.current = c;
      m.current = new Date().getTime();
      x();
      h.current = // TOLOOK
      setInterval(function () {
        x();
      }, 1000);
      return function () {
        clearInterval(h.current);
      };
    }, [c, x]);
    return {
      handleTabClick: w,
      tabRef: d,
      timeTabData: b,
      canUseIndex: p.current
    };
  }
  var Ae = {
    willBegin: "未开始",
    isDoing: "进行中",
    miss: "已结束"
  };
  var Ie = Mach.env.isAndroid;
  function Me(n) {
    var o;
    var c = n.grabRounds;
    var s = n.textColor;
    var u = n.timeTextColor;
    var l = n.isSqjNoema;
    var d = n.activeRoundCode;
    var m = n.currentTime;
    var h = n.activeTabIndex;
    var p = n.getData;
    var g = n.setActiveRoundTimeStatus;
    var b = n.logView;
    var v = n.logClick;
    var y = j(e.useState(0), 2);
    var w = y[0];
    var x = y[1];
    var k = e.useRef(null);
    var S = e.useRef([]);
    var C = e.useCallback(function () {
      var e = f(T().m(function e(t) {
        var n;
        var i;
        var o;
        var c;
        var a;
        var s;
        var u;
        var l;
        return T().w(function (e) {
          for (;;) {
            switch (e.n) {
              case 0:
                n = S.current[t];
                if (!Mach.env.isH5) {
                  e.n = 1;
                  break;
                }
                i = n.getBoundingClientRect().left;
                o = n.offsetWidth;
                if (i > w * 0.5 || i < o * 0.2) {
                  k.current.scrollLeft = k.current.scrollLeft + i - o * 0.4;
                }
                e.n = 4;
                break;
              case 1:
                e.n = 2;
                return r.measureInWindowAsync(n);
              case 2:
                c = e.v;
                a = c.x;
                s = c.width;
                if (!(a > w * 0.5 || a < s * 0.2)) {
                  e.n = 4;
                  break;
                }
                e.n = 3;
                return r.contentOffsetAsync(k.current);
              case 3:
                u = e.v;
                l = u.scrollLeft;
                k.current.scrollToOffset({
                  x: l + a - s * 0.4,
                  y: 0
                }, false);
              case 4:
                return e.a(2);
            }
          }
        }, e);
      }));
      return function (t) {
        return e.apply(this, arguments);
      };
    }(), [w]);
    var A = je({
      grabRounds: c,
      getData: p,
      activeRoundCode: d,
      currentTime: m,
      isSqjNoema: l,
      autoScrollTab: C,
      logClick: v
    });
    var I = A.handleTabClick;
    var M = A.timeTabData;
    var P = A.canUseIndex;
    e.useEffect(function () {
      var e;
      var t = M.findIndex(function (e) {
        return e.roundCode === d;
      });
      g((e = M[t]) === null || e === undefined ? undefined : e.status);
    }, [d, g, M]);
    var O = e.useCallback(function (e) {
      switch (M.length) {
        case 4:
          if (e) {
            return 136;
          } else {
            return 123;
          }
        case 3:
          return 218;
        case 2:
          return 327;
        default:
          if (e) {
            return 136;
          } else {
            return 86;
          }
      }
    }, [M]);
    var R = e.useMemo(function () {
      switch (M.length) {
        case 5:
        case 4:
          return 50;
        case 3:
        case 2:
        case 1:
          return 0;
        default:
          return 36;
      }
    }, [M.length]);
    var N = e.useCallback(function (e, t) {
      if (e) {
        return 1;
      } else if (t) {
        return 0.4;
      } else {
        return 0.6;
      }
    }, []);
    e.useEffect(function () {
      x(Mach.env.screenWidth);
    }, []);
    var z = e.useCallback(function (e) {
      b("b_waimai_gouv1y6s_mv", {
        activity_time: e
      });
    }, [b]);
    return i.jsxs(t.View, {
      className: a("time-tab-wrapper", "11oojf"),
      children: [M.length === 1 && i.jsxs(t.View, {
        className: a("list-1", "11oojf"),
        children: [((o = M[0]) === null || o === undefined ? undefined : o.status) === G.Miss && i.jsxs(t.View, {
          children: [i.jsx(t.Text, {
            className: a(r.classNames("list-1-time", {
              "list-1-time-isAndroid": Ie
            }), "11oojf"),
            content: M[0].timeTxt,
            style: {
              color: s
            }
          }), i.jsx(t.Text, {
            className: a(r.classNames("list-1-time-desc", {
              "list-1-time-desc-isAndroid": Ie
            }), "11oojf"),
            style: {
              color: s
            },
            content: `场${Ae[M[0].status]}`
          })]
        }), i.jsxs(t.View, {
          className: a("list-1-count-down-wrapper", "11oojf"),
          children: [i.jsx(t.Text, {
            className: a("list-1-count-down-txt", "11oojf"),
            style: {
              color: s
            },
            content: M[0] && M[0].status !== G.Miss ? M[0].statusText : ""
          }), M[0] && M[0].status !== G.Miss && M[0].countDown.h && i.jsxs(t.View, {
            className: a("list-1-count-down-content", "11oojf"),
            children: [i.jsx(t.Text, {
              className: a("list-1-hms", "11oojf"),
              style: {
                background: s,
                color: u
              },
              content: M[0].countDown.h
            }), i.jsx(t.Text, {
              className: a("list-1-colon", "11oojf"),
              style: {
                color: s
              },
              content: ":"
            }), i.jsx(t.Text, {
              className: a("list-1-hms", "11oojf"),
              style: {
                background: s,
                color: u
              },
              content: M[0].countDown.m
            }), i.jsx(t.Text, {
              className: a("list-1-colon", "11oojf"),
              style: {
                color: s
              },
              content: ":"
            }), i.jsx(t.Text, {
              className: a("list-1-hms", "11oojf"),
              style: {
                background: s,
                color: u
              },
              content: M[0].countDown.s
            })]
          })]
        })]
      }), M.length > 1 && i.jsx(t.View, {
        className: a("list-n", "11oojf"),
        children: i.jsx(t.Scroller, {
          className: a(`list-n-scroll  ${M.length > 5 ? "list-n-scroll-can" : ""}`, "11oojf"),
          ref: function (e) {
            return k.current = e;
          },
          showScrollIndicator: false,
          children: i.jsx("content", {
            className: a("list-n-scroll-content", "11oojf"),
            children: M.map(function (e, n) {
              return i.jsxs(t.View, {
                ref: function (e) {
                  return S.current[n] = e;
                },
                className: a(r.classNames("list-n-item", {
                  "list-n-item-selected": n === h
                }), "11oojf"),
                style: {
                  color: s,
                  marginRight: R,
                  width: `${O(e.status === G.IsDoing || P === n && e.status === G.WillBegin)}rpx`,
                  opacity: N(n === h, e.status === G.Miss)
                },
                onClick: function () {
                  return I(n, true);
                },
                onView: function () {
                  z([e.startTime, e.endTime]);
                },
                children: [P === n ? i.jsxs(t.View, {
                  className: a("list-n-item-count-down", "11oojf"),
                  children: [i.jsx(t.Text, {
                    className: a("list-n-item-count-down-hms", "11oojf"),
                    content: e.countDown.h,
                    style: {
                      background: s,
                      color: u
                    }
                  }), i.jsx(t.Text, {
                    className: a("list-n-item-count-down-colon", "11oojf"),
                    style: {
                      color: s
                    },
                    content: ":"
                  }), i.jsx(t.Text, {
                    className: a("list-n-item-count-down-hms", "11oojf"),
                    style: {
                      background: s,
                      color: u
                    },
                    content: e.countDown.m
                  }), i.jsx(t.Text, {
                    className: a("list-n-item-count-down-colon", "11oojf"),
                    style: {
                      color: s
                    },
                    content: ":"
                  }), i.jsx(t.Text, {
                    className: a("list-n-item-count-down-hms", "11oojf"),
                    style: {
                      background: s,
                      color: u
                    },
                    content: e.countDown.s
                  })]
                }) : i.jsx(t.Text, {
                  className: a("list-n-item-time", "11oojf"),
                  maxLines: 0,
                  style: {
                    color: s
                  },
                  content: e.timeTxt
                }), i.jsx(t.Text, {
                  className: a(r.classNames("list-n-item-status-txt", {
                    "list-n-item-status-txt-doing": P === n
                  }), "11oojf"),
                  style: {
                    color: s
                  },
                  content: P === n && e.status === G.WillBegin ? "距开始" : Ae[e.status]
                })]
              }, e.roundCode);
            })
          })
        })
      })]
    });
  }
  M(`.WzSQm {
  width: 6.54rem;
  background: #FFFFFF;
  border-radius: 0.16rem;
  align-items: center;
  justify-content: center;
}
.ON1jm {
  width: 1.64rem;
  height: 0.9rem;
}
.SBXcy {
  height: 0.9rem;
  flex-direction: column;
  justify-content: center;
}
.c17v8 {
  font-family: PingFangSC-Regular;
  font-size: 0.24rem;
  color: #858687;
  text-align: left;
  line-height: 0.35rem;
  font-weight: 400;
}
`);
  function Pe(e) {
    var n = e.isMultipleTimeTabs;
    return i.jsx(t.View, {
      className: a("empty-card-wrapper", "26x95f"),
      style: {
        height: n ? "140rpx" : "192rpx",
        marginTop: n ? "30rpx" : "10rpx"
      },
      children: i.jsxs(t.View, {
        children: [i.jsx(t.Image, {
          src: "https://p0.meituan.net/dptakeaway/4559401a9cd1e9d1dbb36153e7570d1923668.png",
          className: a("empty-img", "26x95f")
        }), i.jsxs(t.View, {
          className: a("text-wrapper", "26x95f"),
          children: [i.jsx(t.Text, {
            content: "本场没有您可领的红包",
            className: a("empty-text-tip", "26x95f")
          }), i.jsx(t.Text, {
            content: "看看其他优惠吧～",
            className: a("empty-text-tip", "26x95f")
          })]
        })]
      })
    });
  }
  M(`.ma38v {
  width: 7.02rem;
  height: 2.88rem;
  border-radius: 0.24rem;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.hRb-I {
  font-family: PingFangSC-Regular;
  font-size: 0.28rem;
  color: #575859;
  text-align: center;
  line-height: 0.35rem;
  font-weight: 400;
  margin-bottom: 0.12rem;
}
.-V8F1 {
  background-image: linear-gradient(90deg, #FFE74D 0%, #FFDD19 100%);
  border-radius: 0.32rem;
  height: 0.64rem;
  width: 1.68rem;
  text-align: center;
  line-height: 0.64rem;
  font-family: PingFangSC-Medium;
  font-size: 0.28rem;
  color: #33312D;
  font-weight: 500;
}
.KTqrZ {
  width: 2.32rem;
  height: 1.3rem;
}
`);
  var Oe = u.default.gdUtil;
  var Re = u.default.env.isPrerender;
  var Ne = [{
    img: "https://p0.meituan.net/dptakeaway/7510f5c6e1ec01e043fbda9c991bbc8615618.png",
    txt: "登录后可见详细信息~",
    btnTxt: "去登录"
  }, {
    img: "https://p0.meituan.net/dptakeaway/4559401a9cd1e9d1dbb36153e7570d1923668.png",
    txt: "不在活动时间内，看看其他优惠吧～",
    btnTxt: ""
  }, {
    img: "https://p0.meituan.net/dptakeaway/7510f5c6e1ec01e043fbda9c991bbc8615618.png",
    txt: "当前抢券人数过多，请稍后再试~",
    btnTxt: "重新加载"
  }];
  function Ge(n) {
    var r = n.isSqjNoema;
    var o = n.backgroundColor;
    var c = n.abnormalType;
    var s = n.bgImg;
    var u = e.useCallback(function () {
      Y();
      if (c === F.NeedLogin) {
        oe();
      }
      if (c === F.OtherAnomalies) {
        Oe.reload();
      }
    }, [c]);
    var l = e.useMemo(function () {
      return i.jsxs(t.ImageBackground, {
        className: a("login-bg", "12kyc3"),
        resizeMode: "scaleToFill",
        src: "https://p0.meituan.net/dptakeaway/770dabca1ad4bf8bc35f793571302f1c50090.png",
        children: [i.jsx(t.Image, {
          src: Ne[c].img,
          className: a("net-error-img", "12kyc3")
        }), !Re && i.jsxs(i.Fragment, {
          children: [i.jsx(t.Text, {
            content: Ne[c].txt,
            className: a("login-text-tip", "12kyc3")
          }), Ne[c].btnTxt && i.jsx(t.Text, {
            content: Ne[c].btnTxt,
            className: a("login-btn", "12kyc3")
          })]
        })]
      });
    }, [c]);
    if (r) {
      return i.jsx(t.ImageBackground, {
        onClick: u,
        className: a("login-bg", "12kyc3"),
        resizeMode: "scaleToFill",
        src: s || "https://p0.meituan.net/dptakeaway/81190aeb4118199d24f5e3d79fb8693123186.png",
        children: l
      });
    } else {
      return i.jsx(t.View, {
        onClick: u,
        className: a("login-bg", "12kyc3"),
        style: {
          backgroundColor: o || "#FFCBDF"
        },
        children: l
      });
    }
  }
  M(`.-jSHi {
  background-color: #000000b3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.bkR7s {
  width: 6.4rem;
  height: 8rem;
  overflow: hidden;
  border-radius: 0.2rem;
  background: #ffffff;
  margin-bottom: 0.45rem;
  justify-content: center;
}
.ZHmJS {
  flex-direction: column;
  width: 5.5rem;
}
.DL-Wu {
  width: 100%;
  padding: 0.5rem 0 0.38rem;
  text-align: center;
  font-size: 0.36rem;
  color: #ff8200;
  font-family: couriernew, courier, monospace;
}
.Z9pYA {
  width: 0.8rem;
  height: 0.8rem;
}
.RWKHk {
  text-align: left;
  font-size: 0.26rem;
  color: #292929;
  line-height: 0.4rem;
  font-family: couriernew, courier, monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-lines: 0;
}
.JBZy1 {
  max-height: 6rem;
  overflow-y: auto;
  flex-direction: column;
}
.JBZy1::-webkit-scrollbar {
  display: none;
}
`);
  var ze;
  var De = 0;
  var Ue = "";
  function Ee(n) {
    var r = n.ruleText;
    var c = n.onClose;
    e.useEffect(function () {
      if (Mach.env.isH5) {
        De = window.pageYOffset || document.documentElement.scrollTop || window.scrollY || 0;
        document.body.style.top = `-${De}px`;
        document.body.style.position = "fixed";
        document.body.style.width = "100%";
        document.body.style.height = "100%";
        Ue = document.body.style.overflow;
        document.body.style.overflow = "hidden";
      }
      return function () {
        if (Mach.env.isH5) {
          document.body.style.display = "block";
          document.body.style.position = "static";
          document.body.style.top = "0";
          document.documentElement.scrollTop = De;
          document.body.scrollTop = De;
          document.body.style.overflow = Ue;
        }
      };
    }, []);
    return i.jsx(o.Fixed, {
      offset: [0, 0],
      style: {
        width: "100%",
        height: "100%"
      },
      zIndex: 9999,
      liftToRoot: true,
      children: i.jsxs(t.View, {
        className: a("rule", "1r4gew"),
        style: {
          width: 750,
          height: Mach.env.isH5 ? "100%" : Mach.env.screenHeight * (750 / Mach.env.screenWidth)
        },
        children: [i.jsx(t.View, {
          className: a("rule-wrap", "1r4gew"),
          children: i.jsxs(t.View, {
            className: a("rule-area", "1r4gew"),
            children: [i.jsx(t.Text, {
              className: a("rule-title", "1r4gew"),
              content: "活动规则"
            }), i.jsx(t.Scroller, {
              className: a("rule-text-scroller", "1r4gew"),
              showScrollIndicator: false,
              children: i.jsx("content", {
                children: r.split("\n").map(function (e) {
                  return i.jsx(t.Text, {
                    className: a("rule-text", "1r4gew"),
                    content: e,
                    maxLines: 100000
                  });
                })
              })
            })]
          })
        }), i.jsx(t.Image, {
          className: a("rule-close-btn", "1r4gew"),
          src: "https://p0.meituan.net/ingee/a49527d0668ac9e6b2583eaab9d316f74165.png",
          onClick: c,
          resizeMode: "widthFix"
        })]
      })
    });
  }
  var Fe = u.default.gdAjax;
  var _e = u.default.gdEvent;
  var We = "72";
  var Be = "77";
  var qe = "84";
  var He = "87";
  var Xe = u.default.gdUtil.getCtypeValue;
  var Je = u.default.gdBridge.getBridge().knb;
  var Qe = function () {
    return g(function e(t) {
      h(this, e);
      this.url = "";
      this.path = "";
      this.setPath(t.path);
      this.setUrl();
    }, [{
      key: "setPath",
      value: function (e) {
        this.path = e;
      }
    }, {
      key: "setUrl",
      value: function (e) {
        this.url = this.getUrl(this.path, e);
      }
    }, {
      key: "getUrl",
      value: function (e, t) {
        var n = u.default.env;
        var r = n.isProd;
        var i = n.isStage;
        n.isLocal;
        if (t) {
          return `//${t}${e}`;
        } else {
          e = n.isDp() ? r ? `//promotion.waimai.meituan.com${e}` : i ? `//promotion.waimai.st.sankuai.com${e}` : `//promotion.waimai.test.sankuai.com${e}` : r ? `//promotion.waimai.meituan.com${e}` : i ? `//promotion.waimai.st.sankuai.com${e}` : `//promotion.waimai.test.sankuai.com${e}`;
          if (!(Mach.env.isH5 || n.isLocal)) {
            e = "https:" + e;
          }
          return e;
        }
      }
    }, {
      key: "buildQueryParams",
      value: function (e) {
        return Object.keys(e).map(function (t) {
          return encodeURIComponent(t) + "=" + encodeURIComponent(e[t]);
        }).join("&");
      }
    }, {
      key: "getToken",
      value: (e = f(T().m(function e() {
        var t;
        var n;
        var r;
        var i;
        var o;
        var c;
        var a;
        var s;
        return T().w(function (e) {
          for (;;) {
            switch (e.n) {
              case 0:
                t = u.default.env;
                n = u.default.gdUtil.qs() || {};
                r = n && n.wm_logintoken || "";
                i = u.default.gdUtil.getGlobalData();
                if (o = t.isTest && (i.group === We || i.group === qe) || (t.isStage || t.isProd) && (i.group === Be || i.group === He)) {
                  e.n = 1;
                  break;
                }
                return e.a(2, "");
              case 1:
                if (!t.isAndroid && !t.isHarmonyMSI || !t.isWmApp) {
                  e.n = 5;
                  break;
                }
                e.p = 2;
                e.n = 3;
                return this.getKnbUserInfo();
              case 3:
                c = e.v;
                if (a = c.wm_logintoken) {
                  r = a;
                }
                e.n = 5;
                break;
              case 4:
                e.p = 4;
                s = e.v;
                console.log(s);
              case 5:
                console.log(`[wm-playcenter-logic][getToken] isWelfareCenterGroup: ${o}, wm_logintoken: ${r}`);
                u.default.gdMonitor.addLogan(`[wm-playcenter-logic][getToken] isWelfareCenterGroup: ${o}, wm_logintoken: ${r}`);
                if (!o || !r) {
                  e.n = 6;
                  break;
                }
                return e.a(2, r);
              case 6:
                return e.a(2, "");
            }
          }
        }, e, this, [[2, 4]]);
      })), function () {
        return e.apply(this, arguments);
      })
    }, {
      key: "getKnbUserInfo",
      value: function () {
        return new Promise(function (e, t) {
          try {
            Je.ready(function () {
              Je.use("getUserInfo", {
                success: function (t) {
                  var n = {};
                  var r = t == null ? undefined : t.userId;
                  var i = t == null ? undefined : t.uuid;
                  var o = t == null ? undefined : t.token;
                  n.token = o;
                  n.wm_logintoken = o;
                  n.userId = r;
                  n.uuid = i;
                  e(n);
                },
                fail: function (e) {
                  t(e);
                }
              });
            });
          } catch (e) {
            t(e);
          }
        });
      }
    }, {
      key: "fetch",
      value: function (e) {
        return u.default.gdAjax.fetch(e);
      }
    }, {
      key: "extractRequestParams",
      value: function (e, t) {
        var n = e || {};
        return {
          GET: n.data,
          POST: n.params
        }[t] || {};
      }
    }, {
      key: "fetchAuth",
      value: function (e, t) {
        var n = this;
        return new Promise(function () {
          var r = f(T().m(function r(i, o) {
            var c;
            var a;
            var s;
            var l;
            var d;
            var m;
            var h;
            var p;
            var g;
            var b;
            var v;
            var y;
            var w;
            var x;
            return T().w(function (r) {
              for (;;) {
                switch (r.n) {
                  case 0:
                    c = u.default.env;
                    a = e.data;
                    s = a === undefined ? {} : a;
                    l = e.needSign;
                    d = l !== undefined && l;
                    m = e.needFingerprint;
                    h = m !== undefined && m;
                    p = e.fingerprintKey;
                    g = p === undefined ? "mtFingerprint" : p;
                    b = e.needCouponRefParams;
                    v = b !== undefined && b;
                    y = "";
                    r.p = 1;
                    r.n = 2;
                    return n.getToken();
                  case 2:
                    y = r.v;
                    r.n = 4;
                    break;
                  case 3:
                    r.p = 3;
                    x = r.v;
                    console.log(x);
                  case 4:
                    w = y ? `&token=${y}` : "";
                    c.isWxMp().then(function () {
                      var r = f(T().m(function r(a) {
                        var l;
                        var f;
                        return T().w(function (r) {
                          for (;;) {
                            switch (r.n) {
                              case 0:
                                l = false;
                                f = "";
                                if (!s.ctype) {
                                  r.n = 1;
                                  break;
                                }
                                f = s.ctype;
                                r.n = 3;
                                break;
                              case 1:
                                r.n = 2;
                                return Xe();
                              case 2:
                                f = r.v;
                              case 3:
                                n.fetch(Object.assign(Object.assign({}, e), {
                                  method: t,
                                  url: `${n.url}?isMini=${a ? 1 : 0}&ctype=${f}&isInDpEnv=${Number(c.isDp())}${w}`,
                                  data: s,
                                  params: n.extractRequestParams(e, t),
                                  withCredentials: true,
                                  mode: "cors",
                                  needFingerprint: h,
                                  fingerprintKey: g,
                                  needSign: d,
                                  needCouponRefParams: v
                                })).then(function (t) {
                                  var r = t || {};
                                  var c = r.code;
                                  var a = r.subcode;
                                  var s = r.msg;
                                  if (c === 0) {
                                    return i(t);
                                  } else if (c === 1) {
                                    n.checkNotLoginCase(e, t, a);
                                    return i(t);
                                  } else {
                                    if (!(c !== 500 && c !== 501)) {
                                      n.checkHandleCase(e, "handleServerError", s);
                                    }
                                    return o({
                                      error: false,
                                      response: t
                                    });
                                  }
                                }, function (e) {
                                  l = true;
                                  return Promise.reject(e);
                                }).catch(function (e) {
                                  u.default.gdMonitor.addError({
                                    error: {
                                      name: "[wm-playcenter-logic] + api fetchAuth catch error",
                                      msg: "[wm-playcenter-logic] + api fetchAuth catch error"
                                    },
                                    opts: {
                                      category: window.Owl.errorModel.CATEGORY.AJAX,
                                      level: l ? window.Owl.errorModel.LEVEL.WARN : window.Owl.errorModel.LEVEL.ERROR,
                                      tags: {
                                        info: "",
                                        componentInfos: "",
                                        errorMessage: e && e.message,
                                        errorStack: e && e.stack
                                      }
                                    }
                                  });
                                  o({
                                    error: e
                                  });
                                });
                              case 4:
                                return r.a(2);
                            }
                          }
                        }, r);
                      }));
                      return function (e) {
                        return r.apply(this, arguments);
                      };
                    }());
                  case 5:
                    return r.a(2);
                }
              }
            }, r, null, [[1, 3]]);
          }));
          return function (e, t) {
            return r.apply(this, arguments);
          };
        }());
      }
    }, {
      key: "fetchPostAuth",
      value: function (e) {
        return this.fetchAuth(e, "POST");
      }
    }, {
      key: "fetchGetAuth",
      value: function (e) {
        return this.fetchAuth(e, "GET");
      }
    }, {
      key: "login",
      value: function () {
        return u.default.gdFeature.login();
      }
    }, {
      key: "checkNotLoginCase",
      value: function (e, t, n) {
        if (!(n !== 4 && n !== 7)) {
          if (typeof e.handleLogin == "function") {
            e.handleLogin(t);
          } else {
            this.login().then(function () {
              if (typeof e.loginSuccess == "function") {
                e.loginSuccess(t);
              }
            }, function (n) {
              console.log("登录失败，请重新登录");
              if (typeof e.loginFail == "function") {
                e.loginFail(t);
              }
              u.default.gdMonitor.addError({
                error: {
                  name: "[wm-playcenter-logic] + api checkNotLoginCase error",
                  msg: "[wm-playcenter-logic] + api checkNotLoginCase error"
                },
                opts: {
                  category: window.Owl.errorModel.CATEGORY.SCRIPT,
                  tags: {
                    info: "",
                    componentInfos: "",
                    errorMessage: n && n.message,
                    errorStack: n && n.stack
                  }
                }
              });
            });
          }
        }
      }
    }, {
      key: "checkHandleCase",
      value: function (e, t, n) {
        if (typeof e[t] == "function") {
          e[t](n);
        } else {
          console.log(t);
        }
      }
    }]);
    var e;
  }();
  var Ze = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/doaction"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchDoAction",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }]);
  }(Qe);
  var Ke = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/orderLottery/taskStatus"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchTaskStatus",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var Ye = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/entry"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEntry",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var $e = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/treasurebox/signup/record"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSignRecord",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var et = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/treasurebox/signup/reward/get"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSignPrize",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var tt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/getreceivinginfo"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchGetReceivingInfo",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var nt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/myawards"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchMyAwards",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var rt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/myactions"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchMyActions",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var it = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/reminder"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchReminder",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var ot = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/reminder"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchReminderInfo",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var ct = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/playeraccount"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchPlayerAccount",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var at = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/receivinginfo"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchReceivingInfo",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var st = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/transferrecord"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchTransferRecord",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var ut = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/login"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchCheckLogin",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var lt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/supportInfo"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSupportInfo",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var dt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/leaderboard"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchLeaderBoard",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var ft = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/totalrewards"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchTotalRewards",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var mt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/lottery/limitcouponcomponent/getTime"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchServerTime",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var ht = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/preday/doaction/info"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchPredayDoActionInfo",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var pt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/coupon/multiable"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchCheckCouponMultiable",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var gt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/coupon/multiple"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchDoMultipleRedBag",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var bt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/mycoupons/shenquan"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchMyGodTickets",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var vt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/shenquan/amount/max"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchMaxExpandAmount",
      value: function (e) {
        if (e == null ? undefined : e.domain) {
          this.setUrl(e.domain);
        }
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var yt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/biztime/rw"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchFirstQuitTime",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var wt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/coupon/shenquan/used"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSaveAmount",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var xt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/subsinfo"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSubsInfo",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var kt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/sn/status"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSmartSubscribeStatus",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Tt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/sn/subscribe"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchChangeSubscribeStatus",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var St = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/achievement/shareimg/url"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchUploadImage",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var Ct = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/eurocup/guess/pop/confirm"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEurocupPopConfirm",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var jt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/eurocup/guess"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEurocupGuess",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var At = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/eurocup/guess/entry"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEurocupEntry",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var It = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/activity/getvalidactivity"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchValidActivity",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var Mt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/eurocup/guess/record"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEuroCupGuessRecord",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Pt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/eurocup/point"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchEuroCupPoint",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Ot = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v2/biz/count/add"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "addCount",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var Rt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/shenquan/amount/max"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchMaxCouponValue",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Nt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/clock-in/status"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchClockinStatus",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Gt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/clock-in/reward"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchClockinReward",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var zt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/clock-in/track"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchClockinTrack",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Dt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/clock-in"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchClockin",
      value: function (e) {
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }]);
  }(Qe);
  var Ut = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/user/toast/count/get"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "fetchSignModalTimes",
      value: function (e) {
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Et = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: "/playcenter/common/v1/user/toast/count/add"
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "addSignModalTimes",
      value: function (e) {
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var Ft = "/playcenter/signlottery/info";
  var _t = "/playcenter/signlottery/signIn";
  var Lt = "/playcenter/signlottery/lottery";
  var Vt = "/playcenter/signlottery/myAward";
  var Wt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: Ft
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "updateUrl",
      value: function (e) {
        this.setPath(e);
        this.setUrl();
      }
    }, {
      key: "getInfo",
      value: function (e) {
        this.updateUrl(Ft);
        return this.fetchGetAuth(e);
      }
    }, {
      key: "doSignIn",
      value: function (e) {
        this.updateUrl(_t);
        return this.fetchPostAuth(e);
      }
    }, {
      key: "doLottery",
      value: function (e) {
        this.updateUrl(Lt);
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }, {
      key: "getMyAward",
      value: function (e) {
        this.updateUrl(Vt);
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var Bt = "/playcenter/generalcoupon/info";
  var qt = "/playcenter/generalcoupon/fetch";
  var Ht = "/playcenter/generalcoupon/student/info";
  var Xt = "/playcenter/generalcoupon/student/fetch";
  var Jt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: Bt
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "updateUrl",
      value: function (e) {
        this.setPath(e);
        this.setUrl();
      }
    }, {
      key: "getInfo",
      value: function (e) {
        this.updateUrl(Bt);
        return this.fetchGetAuth(e);
      }
    }, {
      key: "doFetch",
      value: function (e) {
        this.updateUrl(qt);
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }, {
      key: "getStudentInfo",
      value: function (e) {
        this.updateUrl(Ht);
        return this.fetchGetAuth(e);
      }
    }, {
      key: "doStudentFetch",
      value: function (e) {
        this.updateUrl(Xt);
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }]);
  }(Qe);
  var Qt = "/playcenter/generalcoupon/magicalmember/info";
  var Zt = "/playcenter/generalcoupon/magicalmember/fetch";
  var Kt = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: Qt
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "updateUrl",
      value: function (e, t) {
        this.setPath(e);
        this.setUrl(t);
      }
    }, {
      key: "getInfo",
      value: function (e) {
        var t = (e == null ? undefined : e.domain) || "";
        this.updateUrl(Qt, t);
        return this.fetchGetAuth(e);
      }
    }, {
      key: "doFetch",
      value: function (e) {
        var t = (e == null ? undefined : e.domain) || "";
        this.updateUrl(Zt, t);
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }]);
  }(Qe);
  var Yt = "/placement/bannerforwx";
  var $t = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: Yt
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "updateUrl",
      value: function (e) {
        this.setPath(e);
        this.setUrl();
      }
    }, {
      key: "getInfo",
      value: function (e) {
        this.updateUrl(Yt);
        return this.fetchGetAuth(e);
      }
    }]);
  }(Qe);
  var en = "/playcenter/generalcoupon/wxcardbag/info";
  var tn = "/playcenter/generalcoupon/wxcardbag/fetch";
  var nn = "/playcenter/generalcoupon/wxcardbag/notifyIssueResult";
  var rn = function (e) {
    function t(e) {
      h(this, t);
      return m(this, t, [Object.assign({}, {
        path: en
      }, e || {})]);
    }
    y(t, e);
    return g(t, [{
      key: "updateUrl",
      value: function (e) {
        this.setPath(e);
        this.setUrl();
      }
    }, {
      key: "fetchGetCouponInfo",
      value: function (e) {
        this.updateUrl(en);
        return this.fetchGetAuth(e);
      }
    }, {
      key: "fetchPostCoupon",
      value: function (e) {
        this.updateUrl(tn);
        return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
          needCouponRefParams: true
        }));
      }
    }, {
      key: "fetchPostWxCardAddRes",
      value: function (e) {
        this.updateUrl(nn);
        return this.fetchPostAuth(e);
      }
    }]);
  }(Qe);
  var on = function () {
    function e() {
      h(this, e);
      this.reminder = new it();
      this.reminderInfo = new ot();
      this.doAction = new Ze();
      this.taskStatus = new Ke();
      this.entry = new Ye();
      this.signModal = new Ut();
      this.addSignModal = new Et();
      this.signRecord = new $e();
      this.signPrize = new et();
      this.getReceivingInfo = new tt();
      this.myAwards = new nt();
      this.myActions = new rt();
      this.playerAccount = new ct();
      this.receivingInfo = new at();
      this.transferRecord = new st();
      this.checkLogin = new ut();
      this.supportInfo = new lt();
      this.leaderboard = new dt();
      this.totalRewards = new ft();
      this.serverTime = new mt();
      this.predayDoActionInfo = new ht();
      this.checkCouponMultiable = new pt();
      this.doMultipleRedBag = new gt();
      this.myGodTickets = new bt();
      this.maxExpandAmount = new vt();
      this.firstQuitTime = new yt();
      this.saveAmount = new wt();
      this.subsInfo = new xt();
      this.smartSubscribeStatus = new kt();
      this.changeSubscribeStatus = new Tt();
      this.uploadImage = new St();
      this.eurocupPopConfirm = new Ct();
      this.eurocupGuess = new jt();
      this.eurocupEntry = new At();
      this.validActivity = new It();
      this.eurocupPoint = new Pt();
      this.eurocupGuessRecord = new Mt();
      this.pvCount = new Ot();
      this.maxCouponValue = new Rt();
      this.clockinStatus = new Nt();
      this.clockinReward = new Gt();
      this.clockin = new Dt();
      this.clockinTrack = new zt();
      this.signLottery = new Wt();
      this.commonCoupon = new Jt();
      this.welfareCommonCoupon = new Kt();
      this.carouselVenue = new $t();
      this.wxCardCoupon = new rn();
    }
    return g(e, [{
      key: "fetchDoAction",
      value: function (e) {
        return this.doAction.fetchDoAction(e);
      }
    }, {
      key: "fetchTaskStatus",
      value: function (e) {
        return this.taskStatus.fetchTaskStatus(e);
      }
    }, {
      key: "fetchEntry",
      value: function (e) {
        return this.entry.fetchEntry(e);
      }
    }, {
      key: "addSignModalTimes",
      value: function (e) {
        return this.addSignModal.addSignModalTimes(e);
      }
    }, {
      key: "fetchSignModalTimes",
      value: function (e) {
        return this.signModal.fetchSignModalTimes(e);
      }
    }, {
      key: "fetchSignRecord",
      value: function (e) {
        return this.signRecord.fetchSignRecord(e);
      }
    }, {
      key: "fetchSignPrize",
      value: function (e) {
        return this.signPrize.fetchSignPrize(e);
      }
    }, {
      key: "fetchGetReceivingInfo",
      value: function (e) {
        return this.getReceivingInfo.fetchGetReceivingInfo(e);
      }
    }, {
      key: "fetchMyAwards",
      value: function (e) {
        return this.myAwards.fetchMyAwards(e);
      }
    }, {
      key: "fetchMyActions",
      value: function (e) {
        return this.myActions.fetchMyActions(e);
      }
    }, {
      key: "fetchReminder",
      value: function (e) {
        return this.reminder.fetchReminder(e);
      }
    }, {
      key: "fetchReminderInfo",
      value: function (e) {
        return this.reminderInfo.fetchReminderInfo(e);
      }
    }, {
      key: "fetchPlayerAccount",
      value: function (e) {
        return this.playerAccount.fetchPlayerAccount(e);
      }
    }, {
      key: "fetchReceivingInfo",
      value: function (e) {
        return this.receivingInfo.fetchReceivingInfo(e);
      }
    }, {
      key: "fetchTransferRecord",
      value: function (e) {
        return this.transferRecord.fetchTransferRecord(e);
      }
    }, {
      key: "fetchCheckLogin",
      value: function (e) {
        return this.checkLogin.fetchCheckLogin(e);
      }
    }, {
      key: "fetchSupportInfo",
      value: function (e) {
        return this.supportInfo.fetchSupportInfo(e);
      }
    }, {
      key: "fetchLeaderBoard",
      value: function (e) {
        return this.leaderboard.fetchLeaderBoard(e);
      }
    }, {
      key: "fetchTotalRewards",
      value: function (e) {
        return this.totalRewards.fetchTotalRewards(e);
      }
    }, {
      key: "fetchServerTime",
      value: function (e) {
        return this.serverTime.fetchServerTime(e);
      }
    }, {
      key: "fetchPredayDoActionInfo",
      value: function (e) {
        return this.predayDoActionInfo.fetchPredayDoActionInfo(e);
      }
    }, {
      key: "fetchCheckCouponMultiable",
      value: function (e) {
        return this.checkCouponMultiable.fetchCheckCouponMultiable(e);
      }
    }, {
      key: "fetchDoMultipleRedBag",
      value: function (e) {
        return this.doMultipleRedBag.fetchDoMultipleRedBag(e);
      }
    }, {
      key: "fetchMyGodTickets",
      value: function (e) {
        return this.myGodTickets.fetchMyGodTickets(e);
      }
    }, {
      key: "fetchMaxExpandAmount",
      value: function (e) {
        return this.maxExpandAmount.fetchMaxExpandAmount(e);
      }
    }, {
      key: "fetchFirstQuitTime",
      value: function (e) {
        return this.firstQuitTime.fetchFirstQuitTime(e);
      }
    }, {
      key: "fetchSaveAmount",
      value: function (e) {
        return this.saveAmount.fetchSaveAmount(e);
      }
    }, {
      key: "fetchSubsInfo",
      value: function (e) {
        return this.subsInfo.fetchSubsInfo(e);
      }
    }, {
      key: "fetchSmartSubscribeStatus",
      value: function (e) {
        return this.smartSubscribeStatus.fetchSmartSubscribeStatus(e);
      }
    }, {
      key: "fetchChangeSubscribeStatus",
      value: function (e) {
        return this.changeSubscribeStatus.fetchChangeSubscribeStatus(e);
      }
    }, {
      key: "fetchUploadImage",
      value: function (e) {
        return this.uploadImage.fetchUploadImage(e);
      }
    }, {
      key: "fetchEurocupPopConfirm",
      value: function (e) {
        return this.eurocupPopConfirm.fetchEurocupPopConfirm(e);
      }
    }, {
      key: "fetchEurocupGuess",
      value: function (e) {
        return this.eurocupGuess.fetchEurocupGuess(e);
      }
    }, {
      key: "fetchEurocupEntry",
      value: function (e) {
        return this.eurocupEntry.fetchEurocupEntry(e);
      }
    }, {
      key: "fetchValidActivity",
      value: function (e) {
        return this.validActivity.fetchValidActivity(e);
      }
    }, {
      key: "fetchEuroCupGuessRecord",
      value: function (e) {
        return this.eurocupGuessRecord.fetchEuroCupGuessRecord(e);
      }
    }, {
      key: "fetchEuroCupPoint",
      value: function (e) {
        return this.eurocupPoint.fetchEuroCupPoint(e);
      }
    }, {
      key: "addPvCount",
      value: function (e) {
        return this.pvCount.addCount(e);
      }
    }, {
      key: "fetchMaxCouponValue",
      value: function (e) {
        return this.maxCouponValue.fetchMaxCouponValue(e);
      }
    }, {
      key: "fetchClockinStatus",
      value: function (e) {
        return this.clockinStatus.fetchClockinStatus(e);
      }
    }, {
      key: "fetchClockinReward",
      value: function (e) {
        return this.clockinReward.fetchClockinReward(e);
      }
    }, {
      key: "fetchClockin",
      value: function (e) {
        return this.clockin.fetchClockin(e);
      }
    }, {
      key: "fetchClockinTrack",
      value: function (e) {
        return this.clockinTrack.fetchClockinTrack(e);
      }
    }], [{
      key: "getInstance",
      value: function () {
        if (!e.instance) {
          e.instance = new e();
        }
        return e.instance;
      }
    }]);
  }();
  function cn(e) {
    var t = e.path;
    var n = e.method;
    var r = n === undefined ? "GET" : n;
    if (!t) {
      return Promise.reject("fetch path is required");
    }
    var i = new Qe({
      path: t
    });
    if (r === ze.GET) {
      return i.fetchGetAuth(e);
    } else if (r === ze.POST) {
      return i.fetchPostAuth(e);
    } else {
      return i.fetchAuth(e, r);
    }
  }
  (function (e) {
    e.GET = "GET";
    e.POST = "POST";
  })(ze || (ze = {}));
  on.getInstance();
  var an = u.default.gdBridge;
  var sn = u.default.env;
  function un(e) {
    var t = sn.isWxMpWm ? "/outer_packages/mactivity/pages/middlePage/index" : sn.isWxMpMtWm ? "/waimai/outer_packages/mactivity/pages/middlePage/index" : "";
    if (t) {
      var n = an.getBridge().wx;
      try {
        var r = encodeURIComponent(function (e, t) {
          var n = (e || []).map(function (e) {
            return {
              templateId: e || "",
              scene: t || ""
            };
          });
          try {
            return JSON.stringify(n);
          } catch (e) {
            console.error("### _getTemplateScene failed ###", e);
            return "";
          }
        }(e.templateIds, e.scene));
        var i = encodeURIComponent(e.alertTitle || "");
        var o = `${t}?channelId=gundam&templateScene=${r}&alertTitle=${i}`;
        console.log("### jump2MiddlePage ###", "url", o);
        n.miniProgram.navigateTo({
          url: o,
          fail: function (e) {
            console.error("### jump2MiddlePage ###", "navigateTo fail", e);
          }
        });
      } catch (e) {
        console.error("### jump2MiddlePage ###", e);
      }
    } else {
      Z("请前往外卖小程序参加活动~");
    }
  }
  var ln = u.default.env;
  var dn = u.default.gdBridge;
  var fn = u.default.gdUtil;
  var mn = u.default.gdMonitor;
  var hn = u.default.gdAjax;
  var pn = dn.getBridge().knb;
  var gn = "订阅失败！系统未获取通知权限";
  var bn = "订阅任务已过期";
  function vn() {
    var e = "";
    e = ln.isTest ? "//promotion.waimai.test.sankuai.com" : ln.isStage ? "//promotion.waimai.st.sankuai.com" : (ln.isProd, "//promotion.waimai.meituan.com");
    if (!(Mach.env.isH5 || ln.isLocal)) {
      e = `https:${e}`;
    }
    return e;
  }
  function yn() {
    return (yn = f(T().m(function e(t) {
      var n;
      var r;
      var i;
      var o;
      var c;
      return T().w(function (e) {
        for (;;) {
          switch (e.n) {
            case 0:
              n = vn();
              r = `${n}/subscription/wxMiniTask`;
              i = "";
              e.p = 1;
              e.n = 2;
              return u.default.gdUtil.getCtypeValue();
            case 2:
              i = e.v;
              e.n = 4;
              break;
            case 3:
              e.p = 3;
              c = e.v;
              console.log(c);
            case 4:
              o = {
                activityId: t || "",
                isMini: fn.qs().isMini || "",
                ctype: i
              };
              return e.a(2, hn.fetch({
                url: r,
                method: "GET",
                params: o
              }));
          }
        }
      }, e, null, [[1, 3]]);
    }))).apply(this, arguments);
  }
  function wn(e) {
    return new Promise(function (t, n) {
      pn.use("requestPermission", {
        type: "notification",
        readonly: !!e,
        forceJump: true,
        success: t,
        fail: n
      });
    });
  }
  function xn(e) {
    return new Promise(function () {
      var t = f(T().m(function t(n, r) {
        var i;
        return T().w(function (t) {
          for (;;) {
            switch (t.n) {
              case 0:
                t.p = 0;
                if (!dn.use) {
                  t.n = 2;
                  break;
                }
                t.n = 1;
                return dn.use("requestPermission", {
                  type: "notification",
                  readonly: !!e,
                  forceJump: true
                });
              case 1:
                n(true);
                t.n = 4;
                break;
              case 2:
                t.n = 3;
                return wn(e);
              case 3:
                n(true);
              case 4:
                t.n = 6;
                break;
              case 5:
                t.p = 5;
                i = t.v;
                console.log("### gdBridge.use(requestPermission) or KNB.requestPermission error", i);
                r(false);
              case 6:
                return t.a(2);
            }
          }
        }, t, null, [[0, 5]]);
      }));
      return function (e, n) {
        return t.apply(this, arguments);
      };
    }());
  }
  function kn(t) {
    var n;
    var i;
    var o;
    var c;
    var a = fn.getGlobalData();
    var s = j(e.useState(false), 2);
    var u = s[0];
    var l = s[1];
    var d = j(e.useState(true), 2);
    var m = d[0];
    var h = d[1];
    var p = j(e.useState(false), 2);
    var g = p[0];
    var b = p[1];
    var v = j(e.useState(false), 2);
    var y = v[0];
    var w = v[1];
    var x = j(e.useState([]), 2);
    var k = x[0];
    var S = x[1];
    var C = j(e.useState(false), 2);
    var A = C[0];
    var I = C[1];
    var M = e.useRef(false);
    var P = e.useMemo(function () {
      var e;
      var n;
      return !!t && !ln.isWxHm && !ln.isHarmonyMSI && ((e = a.env) !== null && e !== undefined && e.isWmApp || (n = a.env) !== null && n !== undefined && n.isMtApp ? Boolean(t.subscribeAPPConfig.subTaskId && t.subscribeAPPConfig.subAPPToggle) : !(!ln.isWxMpWm && !ln.isWxMpMtWm) && Boolean(t.subscribeMPConfig.subMPTaskId && t.subscribeMPConfig.subMPToggle));
    }, [(n = a.env) === null || n === undefined ? undefined : n.isMtApp, (i = a.env) === null || i === undefined ? undefined : i.isWmApp, t]);
    var N = e.useCallback(function (e) {
      if (!u) {
        l(true);
        var t = function (e) {
          var t = 0;
          var n = 0;
          if (ln.isIOS) {
            n = R.IOS;
          }
          if (ln.isAndroid) {
            n = R.Android;
          }
          if (e != null && e.isWmApp) {
            t = O.wm;
          }
          if (e != null && e.isMtApp) {
            t = O.mt;
          }
          return {
            appType: t,
            deviceType: n
          };
        }(a.env);
        var n = t.appType;
        var r = t.deviceType;
        cn({
          path: "/playcenter/common/v1/reminder",
          data: {
            subsId: e.subscribeAPPConfig.subTaskId,
            toStatus: !m,
            appType: n,
            deviceType: r
          },
          contentType: "json",
          method: "POST",
          handleLogin: function () {
            oe();
          }
        }).then(function (e) {
          l(false);
          var t = e.code;
          var n = e.subcode;
          if (t === 0) {
            Z(m ? "取消订阅提醒，可能错过抢券最佳时机哦!" : "预约成功，将在神券开抢前提醒您~");
            h(!m);
          } else if (t !== 1 || n !== 4 && n !== 7) {
            Z("网络异常，请稍后重试~");
          }
        }).catch(function (e) {
          l(false);
          Z("网络异常，请稍后重试~");
        });
      }
    }, [u, a.env, m]);
    var G = e.useCallback(function (e) {
      I(e);
    }, []);
    var z = e.useCallback(f(T().m(function e() {
      return T().w(function (e) {
        for (;;) {
          switch (e.n) {
            case 0:
              e.p = 0;
              if (!dn.use) {
                e.n = 2;
                break;
              }
              e.n = 1;
              return dn.use("openAppSetting");
            case 1:
              e.n = 3;
              break;
            case 2:
              pn.use("openAppSetting", {
                fail: function (e) {
                  console.log("openAppSetting fail:", e);
                }
              });
            case 3:
              e.n = 5;
              break;
            case 4:
              e.p = 4;
              Z(gn);
            case 5:
              return e.a(2);
          }
        }
      }, e, null, [[0, 4]]);
    })), []);
    var D = function () {
      var e = f(T().m(function e() {
        return T().w(function (e) {
          for (;;) {
            switch (e.n) {
              case 0:
                e.p = 0;
                if (!dn.use) {
                  e.n = 2;
                  break;
                }
                e.n = 1;
                return dn.use("openPage", {
                  url: "app-settings:"
                });
              case 1:
                e.n = 3;
                break;
              case 2:
                pn.use("openPage", {
                  url: "app-settings:",
                  fail: function (e) {
                    console.log("openPage-app-settings fail:", e);
                    z();
                  }
                });
              case 3:
                e.n = 5;
                break;
              case 4:
                e.p = 4;
                z();
              case 5:
                return e.a(2);
            }
          }
        }, e, null, [[0, 4]]);
      }));
      return function () {
        return e.apply(this, arguments);
      };
    }();
    var U = e.useRef(null);
    var E = function () {
      var e = f(T().m(function e(t) {
        var n;
        var r;
        return T().w(function (e) {
          for (;;) {
            switch (e.n) {
              case 0:
                Y();
                n = function () {
                  var e;
                  if (dn.use) {
                    dn.use("unsubscribe", {
                      action: "foreground",
                      subId: (e = U.current) === null || e === undefined ? undefined : e.subId,
                      success: function () {},
                      fail: function () {}
                    });
                  } else {
                    pn.use("unsubscribe", {
                      action: "foreground",
                      success: function () {},
                      fail: function () {}
                    });
                  }
                };
                if (!ln.isIOS) {
                  e.n = 6;
                  break;
                }
                r = function () {
                  xn(true).then(function () {
                    console.log("IOS 触发成功");
                    N(t);
                    n();
                  }, function () {
                    if (!M.current) {
                      Z(gn);
                    }
                    M.current = true;
                    console.log("IOS push设置失败");
                    mn.addLogan("[push设置失败]");
                    n();
                  });
                };
                e.p = 1;
                if (!dn.use) {
                  e.n = 3;
                  break;
                }
                e.n = 2;
                return dn.use("subscribe", {
                  action: "foreground",
                  handle: r
                });
              case 2:
                U.current = e.v;
                e.n = 4;
                break;
              case 3:
                pn.use("subscribe", {
                  action: "foreground",
                  handle: r
                });
              case 4:
                e.n = 6;
                break;
              case 5:
                e.p = 5;
                n();
              case 6:
                D();
                G(false);
              case 7:
                return e.a(2);
            }
          }
        }, e, null, [[1, 5]]);
      }));
      return function (t) {
        return e.apply(this, arguments);
      };
    }();
    var F = e.useCallback(function (e) {
      var t;
      var n;
      if (((t = a.env) !== null && t !== undefined && t.isWmApp || (n = a.env) !== null && n !== undefined && n.isMtApp) && e) {
        return cn({
          path: "/playcenter/common/v1/reminder",
          data: {
            subsId: e.subscribeAPPConfig.subTaskId
          },
          handleLogin: function () {
            b(false);
          }
        }).then(function (e) {
          var t;
          var n = e.code;
          var r = e.data;
          if (n === 0) {
            b(true);
            t = r.status;
          } else {
            t = false;
          }
          h(t);
          return t;
        }).catch(function (e) {
          console.log("error:", e);
          h(false);
          return false;
        });
      } else {
        return Promise.resolve(false);
      }
    }, [(o = a.env) === null || o === undefined ? undefined : o.isMtApp, (c = a.env) === null || c === undefined ? undefined : c.isWmApp]);
    function _(e) {
      (function (e) {
        return yn.apply(this, arguments);
      })(e.subscribeMPConfig.subMPTaskId).then(function (t) {
        var n = t.code;
        var r = t.data;
        if (n === 0) {
          var i = r || [];
          if (k.length > 0 && i.length === 0) {
            Z(bn);
            return;
          }
          if (i.length > 0) {
            S(i);
            (function (e, t) {
              un({
                templateIds: e || [],
                alertTitle: t.subscribeMPConfig.successToast || "",
                scene: String(t.subscribeMPConfig.subMPTaskId || "")
              });
            })(i, e);
          } else {
            Z(bn);
          }
        } else {
          Z("网络异常，请稍后重试~");
        }
      }).catch(function (e) {
        console.error("### fetchTaskStatus failed ###", e);
        Z("网络异常，请稍后重试~");
      });
    }
    r.useAsyncEffect(f(T().m(function e() {
      var n;
      var r;
      return T().w(function (e) {
        for (;;) {
          switch (e.n) {
            case 0:
              h(false);
              l(false);
              w(false);
              S([]);
              if (P) {
                e.n = 1;
                break;
              }
              return e.a(2);
            case 1:
              if (!((n = a.env) !== null && n !== undefined && n.isWmApp || (r = a.env) !== null && r !== undefined && r.isMtApp)) {
                e.n = 2;
                break;
              }
              e.n = 2;
              return F(t);
            case 2:
              return e.a(2);
          }
        }
      }, e);
    })), [t, P]);
    return {
      remindStatus: m,
      isShowSetting: A,
      handleRemind: function () {
        var e;
        var n;
        if (t && P) {
          if ((e = a.env) !== null && e !== undefined && e.isWmApp || (n = a.env) !== null && n !== undefined && n.isMtApp) {
            (function (e) {
              if (g) {
                if (m) {
                  N(e);
                } else {
                  xn(true).then(function () {
                    N(e);
                  }, function () {
                    G(true);
                  });
                }
              } else {
                oe();
              }
            })(t);
          } else if (ln.isWxMpWm || ln.isWxMpMtWm) {
            (function (e) {
              if (!y) {
                w(true);
                // TOLOOK
                setTimeout(function () {
                  w(false);
                  console.log("### handleJump2Sub ###", "fetchingFlag timeout false", y);
                }, 2000);
                _(e);
              }
            })(t);
          }
        }
      },
      handleSetting: E,
      handleCancel: function () {
        Y();
        G(false);
        Z(gn);
      },
      enableRemind: P
    };
  }
  M(`.bo7z1 {
  position: relative;
  width: 7.5rem;
  flex-direction: column;
  align-items: center;
  padding: 0 0.24rem;
}
.QilV5 {
  width: 7.02rem;
  flex-shrink: 0;
  min-height: 2.88rem;
  flex-direction: column;
  border-radius: 0.24rem;
  overflow: hidden;
}
.WNoBO {
  width: 7.02rem;
  min-height: 2.88rem;
  flex-direction: column;
  padding-right: 0.24rem;
  padding-bottom: 0.24rem;
  padding-left: 0.24rem;
}
.V7tAK {
  font-size: 0;
}
.YLsCy {
  width: 7.02rem;
}
._4ELS8 {
  position: absolute;
  right: 0.24rem;
  z-index: 1;
}
.-RJEV {
  width: 0.64rem;
  height: 0.28rem;
  opacity: 0.32;
  background: #000000;
  border-top-right-radius: 0.32rem;
  border-bottom-left-radius: 0.16rem;
}
.Jj3kc {
  width: 0.64rem;
  height: 0.28rem;
  position: absolute;
  top: 0;
  right: 0;
  font-weight: 400;
  font-family: PingFangSC-Regular;
  font-size: 0.2rem;
  color: #ffffff;
  line-height: 0.28rem;
  text-align: center;
  margin: 0;
}
.GHnBV {
  width: 6.54rem;
  flex-direction: column;
  flex-shrink: 0;
}
.VU8Pm {
  width: 6.54rem;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
}
`);
  var Tn = u.default.gdUtil;
  var Sn = u.default.env;
  var Cn = u.default.gdLog;
  var jn = t.GdcHOC(function (o) {
    var c = o.activityId;
    var s = o._styleConfig;
    var u = o.componentInfo;
    var l = o.logicGroup;
    var d = l === undefined ? {
      compScene: "custom",
      backgroundImage: "",
      couponTips: "",
      ruleTips: "",
      layerImgGroup: {
        isShow: false,
        imgUrl: "",
        hrefUrl: ""
      },
      componentStyle: {
        marginBtm: 0,
        marginTop: 0
      }
    } : l;
    var m = o.moduleClick;
    var h = j(e.useState(false), 2);
    var p = h[0];
    var g = h[1];
    var b = j(e.useState(G.WillBegin), 2);
    var v = b[0];
    var y = b[1];
    var w = e.useMemo(function () {
      return d.compScene === U.SqjNoema;
    }, [d.compScene]);
    var x = function (t, n, i) {
      var o = j(e.useState([]), 2);
      var c = o[0];
      var a = o[1];
      var s = j(e.useState({}), 2);
      var u = s[0];
      var l = s[1];
      var d = e.useRef(0);
      var m = j(e.useState(false), 2);
      var h = m[0];
      var p = m[1];
      var g = j(e.useState(""), 2);
      var b = g[0];
      var v = g[1];
      var y = j(e.useState(-1), 2);
      var w = y[0];
      var x = y[1];
      var S = j(e.useState(true), 2);
      var C = S[0];
      var A = S[1];
      var I = j(e.useState(false), 2);
      var M = I[0];
      var P = I[1];
      var O = j(e.useState(false), 2);
      var R = O[0];
      var N = O[1];
      var G = j(e.useState(false), 2);
      var z = G[0];
      var D = G[1];
      var U = e.useCallback(f(T().m(function e() {
        var r;
        var i;
        var o;
        var c;
        var s;
        var u = arguments;
        return T().w(function (e) {
          for (;;) {
            switch (e.n) {
              case 0:
                o = (i = u.length > 0 && u[0] !== undefined ? u[0] : {}) == null ? undefined : i.roundCode;
                c = (r = window) === null || r === undefined || (r = r.location) === null || r === undefined || (r = r.search) === null || r === undefined ? undefined : r.includes("isPreview=true");
                if (t || c) {
                  e.n = 1;
                  break;
                }
                return e.a(2);
              case 1:
                s = function () {
                  var e = f(T().m(function e() {
                    var r;
                    return T().w(function (e) {
                      for (;;) {
                        switch (e.n) {
                          case 0:
                            if (!t) {
                              e.n = 2;
                              break;
                            }
                            e.n = 1;
                            return te(t, n == null ? undefined : n.instanceID, true);
                          case 1:
                            r = e.v;
                            return e.a(2, Fe.fetch({
                              url: `${$("/api/rights/activity/secKill/info")}?${re(k(k({}, r), {}, {
                                roundCode: o
                              }))}`,
                              method: "GET",
                              withCredentials: true
                            }));
                          case 2:
                            return e.a(2, Fe.fetch({
                              url: "https://papi.sankuai.com/api/req/635e7ea5-dc1e-4a4a-83db-448596e8f5e3/gd-limited-time-grab-coupon",
                              method: "GET"
                            }));
                          case 3:
                            return e.a(2);
                        }
                      }
                    }, e);
                  }));
                  return function () {
                    return e.apply(this, arguments);
                  };
                }();
                s().then(function (e) {
                  var t;
                  var n = e.code;
                  var r = e.data;
                  if (n === 6) {
                    N(false);
                    P(true);
                    return;
                  }
                  if (n === 1 && r) {
                    var i = r.allGrabRounds;
                    var c = r.currentGrabCouponInfo;
                    var s = r.currentTime;
                    var u = r.nextGrabCouponInfo;
                    var f = r.activityStartTime;
                    var m = r.activityEndTime;
                    P(false);
                    if (!s) {
                      A(false);
                      return;
                    }
                    p(true);
                    if (f && m && s) {
                      var h = f <= (t = s) && t <= m;
                      if (!h) {
                        A(false);
                        return;
                      }
                      A(h);
                    }
                    if (!(i && (i == null ? undefined : i.length) > 0)) {
                      N(true);
                      return;
                    }
                    N(false);
                    if (i != null && i.length) {
                      var g = i.sort(function (e, t) {
                        return we(e.startTime || "") - we(t.startTime || "");
                      });
                      a(g);
                    }
                    d.current = pe(s);
                    var b = o || "";
                    var y = {};
                    if (c) {
                      b = (c == null ? undefined : c.roundCode) || "";
                      y = c;
                    } else if (u && !o) {
                      b = u == null ? undefined : u.roundCode;
                      y = u;
                    }
                    var w = i.findIndex(function (e) {
                      return e.roundCode === b;
                    });
                    var k = i[w];
                    var T = ge([k.startTime, k.endTime], d.current);
                    var S = he(d.current, T).status;
                    y.status = S;
                    x(w);
                    v(b);
                    l(y);
                  } else {
                    N(true);
                    P(false);
                    l({});
                    v(o || "");
                    W("getGrabRoundInfo", e, "场次预发失败", o);
                  }
                }).catch(function (e) {
                  N(true);
                  P(false);
                  l({});
                  v(o || "");
                  console.log("【神券节限时抢玩法】场次预发失败", e);
                  W("getGrabRoundInfo", e, "场次预发失败", i.roundCode, o);
                }).finally(function () {
                  D(true);
                });
              case 2:
                return e.a(2);
            }
          }
        }, e);
      })), [t, n == null ? undefined : n.instanceID]);
      e.useEffect(function () {
        U({});
      }, [t, U]);
      var E = e.useCallback(function () {
        U({
          roundCode: b
        });
      }, [b, U]);
      e.useEffect(function () {
        var e = Mach.on("pageWillAppear", E);
        return function () {
          if (typeof e == "function") {
            e();
          }
        };
      }, [E]);
      var F = e.useCallback(function () {
        var e = f(T().m(function e(r) {
          var o;
          var c;
          var a;
          var s;
          var f;
          return T().w(function (e) {
            for (;;) {
              switch (e.n) {
                case 0:
                  o = r.rightCode;
                  c = r.handleLogClick;
                  a = u.roundCode;
                  s = u.token;
                  if (i) {
                    _e.emit("wm-coupon-festival-tab-show", {
                      isStop: true,
                      isNotClick: true
                    });
                  }
                  e.n = 1;
                  return te(t, n == null ? undefined : n.instanceID);
                case 1:
                  f = e.v;
                  Fe.fetch({
                    url: `${$("/api/rights/activity/secKill/grab")}?${re({
                      cType: f.cType,
                      fpPlatform: f.fpPlatform,
                      wx_openid: f.wx_openid,
                      appVersion: f.appVersion,
                      latitude: f.latitude,
                      longitude: f.longitude,
                      actualLatitude: f.actualLatitude,
                      actualLongitude: f.actualLongitude
                    })}`,
                    data: {
                      activityId: t,
                      gdId: f.gdId,
                      pageId: f.pageId,
                      instanceId: f.instanceId,
                      rightCode: o,
                      roundCode: a,
                      grabToken: s
                    },
                    needSign: true,
                    method: "POST",
                    contentType: "json",
                    headers: {
                      "Content-Type": "application/json"
                    },
                    needFingerprint: true,
                    withCredentials: true
                  }).then(function (e) {
                    var t = e.code;
                    var n = e.data;
                    var i = n || {};
                    var s = i.currentTime;
                    var f = i.coupon;
                    if (f) {
                      var m = (u.coupon || []).map(function (e) {
                        if (e.rightCode === f.rightCode) {
                          return k(k({}, e), f);
                        } else if (n.subCode === 9017 && e.rightCode === o) {
                          return k(k({}, e), {}, {
                            status: 4
                          });
                        } else {
                          return e;
                        }
                      });
                      var h = k(k({}, u), {}, {
                        coupon: m
                      });
                      l(h);
                    }
                    if (s) {
                      d.current = pe(s);
                    }
                    if (t === 1 && n) {
                      if ((n == null ? undefined : n.subCode) === 1) {
                        c({
                          status: 1,
                          result: 0
                        });
                        _e.emit("refreshCouponAssets", 3);
                        K();
                      }
                      if ((n == null ? undefined : n.subCode) === 9017) {
                        c({
                          status: 0,
                          result: 1
                        });
                      }
                      if (f == null ? undefined : f.toastMsg) {
                        Z(f == null ? undefined : f.toastMsg);
                      }
                    } else {
                      c({
                        status: 0,
                        result: 3
                      });
                      Z((f == null ? undefined : f.toastMsg) || "当前抢券人数过多，请稍后再试~");
                      W("grab", e, "抢券失败", a, r == null ? undefined : r.rightCode);
                    }
                  }).catch(function (e) {
                    c({
                      status: 0,
                      result: 3
                    });
                    Z("当前抢券人数过多，请稍后再试~");
                    W("grab", e, "抢券失败", a, r == null ? undefined : r.rightCode);
                  }).finally(function () {
                    // TOLOOK
                    setTimeout(function () {
                      if (i) {
                        _e.emit("wm-coupon-festival-tab-show", {
                          isNotClick: false
                        });
                      }
                    }, 2000);
                  });
                case 2:
                  return e.a(2);
              }
            }
          }, e);
        }));
        return function (t) {
          return e.apply(this, arguments);
        };
      }(), [t, n == null ? undefined : n.instanceID, u, i]);
      var _ = e.useMemo(function () {
        return r.throttle(F, 1000);
      }, [F]);
      return {
        grabRounds: c,
        currentGrabCouponInfo: u,
        currentTime: d.current,
        activeRoundCode: b,
        isInTime: C,
        needLogin: M,
        isFetchServerTime: h,
        isError: R,
        isFetchOver: z,
        activeTabIndex: w,
        getData: U,
        handelGetAwardCoupon: _
      };
    }(c, u, w);
    var S = x.isFetchServerTime;
    var C = x.grabRounds;
    var A = x.currentGrabCouponInfo;
    var I = x.currentTime;
    var M = x.activeRoundCode;
    var P = x.activeTabIndex;
    var O = x.isInTime;
    var R = x.needLogin;
    var N = x.isError;
    var z = x.isFetchOver;
    var D = x.getData;
    var E = x.handelGetAwardCoupon;
    var L = e.useMemo(function () {
      return {
        gd_page_id: Tn.getGlobalData().gdId,
        instanceID: u.instanceID,
        activity_id: c,
        sub_instanceID: "",
        componentInfo: u
      };
    }, [c, u]);
    var V = e.useCallback(function (e, t) {
      m({
        valBid: e,
        valLab: k(k({}, L), t)
      });
    }, [L, m]);
    var B = e.useCallback(function (e, t) {
      Cn.view({
        valBid: e,
        valLab: k(k({}, L), t)
      });
    }, [L]);
    var q = e.useMemo(function () {
      return {
        subscribeAPPConfig: {
          subAPPToggle: A.openAppAlert,
          subTaskId: A.appAlertId || ""
        },
        subscribeMPConfig: {
          subMPToggle: A.openAppletAlert,
          subMPTaskId: A.appletAlertId || "",
          successToast: A.appletAlertMsg || ""
        }
      };
    }, [A]);
    var H = kn(q);
    var X = H.remindStatus;
    var J = H.isShowSetting;
    var Q = H.handleRemind;
    var ee = H.handleSetting;
    var ne = H.handleCancel;
    var ie = H.enableRemind;
    var oe = e.useCallback(function () {
      Y();
      var e = d.layerImgGroup.hrefUrl;
      if (e) {
        if (e.startsWith("meituanwaimai") && !Sn.isApp() && Tn.handleCallApp) {
          Tn.handleCallApp({
            schemes: [{
              url: e
            }],
            failUrl: window.location.href
          });
        } else {
          Tn.redirectTo(d.layerImgGroup.hrefUrl);
        }
      }
    }, [d.layerImgGroup.hrefUrl]);
    var ce = e.useMemo(function () {
      return C.length > 1;
    }, [C]);
    var ae = e.useMemo(function () {
      var e;
      var t = w ? 3 : 6;
      return (((e = A.coupon) === null || e === undefined ? undefined : e.slice(0, t)) || []).reduce(function (e, t, n) {
        if (n % 3 == 0) {
          e.push([t]);
        } else {
          e[e.length - 1].push(t);
        }
        return e;
      }, []);
    }, [A.coupon, w]);
    var se = e.useCallback(function (e) {
      Y();
      g(!e);
    }, []);
    var ue = e.useCallback(function (e, t) {
      if (e && t) {
        return 20;
      } else {
        return -2;
      }
    }, []);
    var le = e.useMemo(function () {
      var e = d.couponButtonText;
      if (!e) {
        return _;
      }
      var t = Object.entries(e).reduce(function (e, t) {
        var n = j(t, 2);
        var r = n[0];
        var i = n[1];
        if (i) {
          e[r] = i;
          if (r === "toUse") {
            e.inUse = i;
          }
        }
        return e;
      }, {});
      return k(k({}, _), t);
    }, [d.couponButtonText]);
    var de = e.useMemo(function () {
      return i.jsxs(i.Fragment, {
        children: [i.jsx(Me, {
          textColor: s.textColor,
          timeTextColor: s.timeTextColor,
          grabRounds: C,
          activeRoundCode: M,
          isSqjNoema: w,
          currentTime: I,
          getData: D,
          setActiveRoundTimeStatus: y,
          activeTabIndex: P,
          logClick: V,
          logView: B
        }), ae.length > 0 ? i.jsx(t.View, {
          className: a("coupons-line-wrapper", "1ybe2o"),
          style: {
            marginTop: ue(ce, ae.length === 1 && ae[0].length === 1)
          },
          children: ae.map(function (e, n) {
            return i.jsx(t.View, {
              className: a("coupon-lines", "1ybe2o"),
              children: e.map(function (t, r) {
                return i.jsx(Ce, {
                  couponItemData: t,
                  isMultipleTimeTabs: ce,
                  sqjTagIcon: s.sqjTagIcon,
                  oneLineCouponNum: e.length,
                  currentTime: I,
                  activeRoundTimeStatus: v,
                  currentGrabStatus: A.status,
                  handelGetAwardCoupon: E,
                  remindStatus: X,
                  handleRemind: Q,
                  enableRemind: ie,
                  index: `${n}-${r}`,
                  logClick: V,
                  logView: B,
                  activeTabIndex: P,
                  btnTextMap: le
                }, `${t.couponId}-${M}`);
              })
            }, n);
          })
        }) : i.jsx(Pe, {
          isMultipleTimeTabs: ce
        })]
      });
    }, [C, M, I, ie, ae, ce, w, s.sqjTagIcon, s.textColor, s.timeTextColor, v, X, P, A.status, E, Q, D, V, B, ue]);
    var fe = e.useMemo(function () {
      if (R) {
        return i.jsx(Ge, {
          abnormalType: F.NeedLogin,
          isSqjNoema: w,
          backgroundColor: s.backgroundColor,
          bgImg: d.backgroundImage
        });
      } else if (N) {
        return i.jsx(Ge, {
          abnormalType: F.OtherAnomalies,
          isSqjNoema: w,
          backgroundColor: s.backgroundColor,
          bgImg: d.backgroundImage
        });
      } else if (O) {
        if (C.length > 0) {
          return i.jsx(t.View, {
            className: a("content-wrapper-outer", "1ybe2o"),
            children: w ? i.jsx(t.ImageBackground, {
              className: a("content-wrapper", "1ybe2o"),
              resizeMode: "scaleToFill",
              src: d.backgroundImage,
              style: {
                height: "288rpx"
              },
              children: de
            }) : i.jsx(t.View, {
              className: a("content-wrapper", "1ybe2o"),
              style: {
                backgroundColor: s.backgroundColor
              },
              children: de
            })
          });
        } else if (C.length <= 0 && !z) {
          return i.jsx(t.View, {
            className: a("content-wrapper-outer", "1ybe2o"),
            children: i.jsx(t.View, {
              className: a("content-wrapper", "1ybe2o"),
              style: {
                height: "288rpx",
                backgroundColor: s.backgroundColor || "#FFCBDF",
                borderRadius: "24rpx"
              }
            })
          });
        } else {
          return undefined;
        }
      } else {
        return i.jsx(Ge, {
          abnormalType: F.NoInTime,
          isSqjNoema: w,
          backgroundColor: s.backgroundColor,
          bgImg: d.backgroundImage
        });
      }
    }, [s.backgroundColor, C.length, N, O, w, d.backgroundImage, R, de]);
    return i.jsxs(t.View, {
      className: a("component-wrapper", "1ybe2o"),
      style: {
        marginTop: w ? 0 : d.componentStyle.marginTop,
        marginBottom: w ? 0 : d.componentStyle.marginBtm,
        flexDirection: "column"
      },
      onClick: Y,
      children: [d.layerImgGroup.isShow && d.layerImgGroup.imgUrl && !w && i.jsx(t.View, {
        className: a("layer-img-container", "1ybe2o"),
        children: i.jsx(t.Image, {
          src: d.layerImgGroup.imgUrl,
          className: a("layer-img", "1ybe2o"),
          onClick: oe,
          resizeMode: "widthFix"
        })
      }), fe, !w && S && i.jsxs(t.View, {
        className: a("rule-btn", "1ybe2o"),
        onClick: function () {
          return se(false);
        },
        children: [i.jsx(t.View, {
          className: a("rule-btn-bg", "1ybe2o")
        }), i.jsx(t.Text, {
          className: a("rule-btn-text", "1ybe2o"),
          content: "规则"
        })]
      }), p && i.jsx(Ee, {
        onClose: function () {
          return se(true);
        },
        ruleText: d.ruleTips
      }), i.jsx(n.Modal, {
        open: J,
        title: "开启推送通知",
        okText: "去设置",
        cancelText: "不开启",
        onCancel: ne,
        onOk: function () {
          return ee(q);
        },
        children: i.jsx(t.Text, {
          content: "为了您能收到活动订阅提醒，请前往系统设置中开启消息通知权限",
          maxLines: 0
        })
      })]
    });
  });
  return jn;
});
window["@gdc/gd-limited-time-grab-coupon"] = window.__component_output__;
//# sourceMappingURL=bundle.js.map