! function (e, t) {
  "object" == typeof exports && "undefined" != typeof module ? module.exports = t(require("react"), require("@gundam/gundam-core"), require("@gundam/gundam-ui"), require("@gundam/gundam-utils"), require("react/jsx-runtime"), require("@gundam/gundam-base-components")) : "function" == typeof define && define.amd ? define(["react", "@gundam/gundam-core", "@gundam/gundam-ui", "@gundam/gundam-utils", "react/jsx-runtime", "@gundam/gundam-base-components"], t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).__component_output__ = t(e.<PERSON>, e.<PERSON>dam<PERSON>ore, e.GundamUI, e.GundamUtils, e.<PERSON>, e.GundamBaseComponents)
}(this, function (e, t, n, r, i, o) {
  "use strict";
  const c = {
    "1ybe2o": {
      "component-wrapper": "bo7z1",
      "content-wrapper-outer": "QilV5",
      "content-wrapper": "WNoBO",
      "layer-img-container": "V7tAK",
      "layer-img": "YLsCy",
      "rule-btn": "_4ELS8",
      "rule-btn-bg": "-RJEV",
      "rule-btn-text": "Jj3kc",
      "coupons-line-wrapper": "GHnBV",
      "coupon-lines": "VU8Pm"
    },
    "1dezzt": {
      "coupon-wrapper": "cdzYm",
      "grab-style": "ke98Q",
      "sqj-icon-wrapper": "_4VJuw",
      "sqj-icon": "esb-l",
      "price-wrapper": "c1hmE",
      "price-wrapper-1": "wl-tA",
      "price-wrapper-1-isAndroid": "xxxyB",
      "price-wrapper-1-multi": "WA7tP",
      "price-wrapper-1-multi-isAndroid": "DZ7ZA",
      "price-wrapper-2": "O1iuo",
      "price-wrapper-2-multi": "lfLRy",
      "price-wrapper-2-multi-isAndroid": "GZXGc",
      "price-wrapper-3": "MPav9",
      "price-wrapper-3-multi": "nsd5g",
      "price-wrapper-3-multi-H5": "DuHRD",
      "price-wrapper-3-multi-isAndroid": "stHKf",
      "price-content": "_6E2Dr",
      "price-unit": "FE0QO",
      "price-unit-1": "_5quBR",
      "price-unit-2": "SFzwU",
      "price-unit-3": "mxQPn",
      "price-number": "k3LMP",
      "price-number-1": "iiL-i",
      "price-number-2": "MXFbS",
      "price-number-3": "AI9RG",
      "price-limit": "j1k9V",
      "price-limit-1": "L9f9f",
      "price-limit-1-isAndroid": "_5z0ft",
      "price-limit-2": "fCYZK",
      "price-limit-2-multi": "brv6d",
      "price-limit-3": "-PkfV",
      "price-limit-3-multi": "W4t1n",
      "coupon-info": "LjACW",
      "coupon-info-1": "Agpbs",
      "coupon-info-1-multi": "z-vdD",
      "coupon-info-2": "_1cggL",
      "coupon-info-2-multi": "_4bowN",
      "coupon-info-3": "KE6Lm",
      "coupon-info-3-multi": "w9kaE",
      "coupon-icon-1": "tEkrM",
      "coupon-icon-2": "QEhXx",
      "coupon-icon-3": "Cx0k2",
      "coupon-name-3": "_0xR7h",
      "coupon-title": "z5SQU",
      "coupon-title-1": "a-Lrg",
      "coupon-title-2": "_7Fpi9",
      "coupon-title-3": "X7Hbr",
      "coupon-desc-text": "Y5yCO",
      "coupon-desc-text-red": "WuXUV",
      "coupon-desc-text-1": "houwq",
      "coupon-desc-text-2": "CNnOZ",
      "coupon-desc-text-3": "vUwPR",
      "coupon-desc-1": "_1vjeS",
      "coupon-desc-1-multi": "w7JrD",
      "coupon-desc-2": "_9pwOE",
      "coupon-desc-2-multi": "txF1V",
      "coupon-desc-3": "eh43s",
      "coupon-desc-3-H5": "_4JfNV",
      "coupon-desc-3-multi": "HLFQ8",
      "coupon-desc-3-multi-H5": "QonJJ",
      "progress-bar": "HX-vX",
      "progress-bar-1": "pniD1",
      "progress-bar-2": "_1scuO",
      "progress-bar-outer": "M8Hig",
      "progress-bar-outer-gray": "jGMmD",
      "progress-bar-inner": "YJoMG",
      "progress-bar-text": "uThXg",
      "progress-bar-text-gray": "fqsA-",
      btn: "fNthf",
      "btn-disable": "Ad9mC",
      "btn-remindme": "AT6eZ",
      "btn-remindme-done": "-ZdLj",
      "btn-bg": "xotAf",
      "btn-text": "Si6Wc",
      "btn-text-disable": "SO0Lm",
      "btn-text-1": "hg9CX",
      "btn-text-2": "VUi0e",
      "btn-text-3": "rIX7i",
      "eighteen-tag": "mEiVc",
      "eighteen-tag-multiple-time": "_3Ep9L"
    },
    "11oojf": {
      "list-1": "ezvKs",
      "list-1-time": "IIgVT",
      "list-1-time-isAndroid": "LHM5y",
      "list-1-time-desc": "VtXxq",
      "list-1-time-desc-isAndroid": "_9--xS",
      "list-1-count-down-txt": "HsC7X",
      "list-1-count-down-content": "D9q7s",
      "list-1-hms": "nsvqX",
      "list-1-colon": "GDFLV",
      "list-n": "uos5M",
      "list-n-scroll": "XcZDm",
      "list-n-item": "_0-T-U",
      "list-n-item-time": "EvSyQ",
      "list-n-item-status-txt": "lE3Qo",
      "list-n-item-status-txt-doing": "lRIid",
      "list-n-item-count-down": "bk-2y",
      "list-n-item-count-down-hms": "xUqe6",
      "list-n-item-count-down-colon": "t3TKG"
    },
    "26x95f": {
      "empty-card-wrapper": "WzSQm",
      "empty-img": "ON1jm",
      "text-wrapper": "SBXcy",
      "empty-text-tip": "c17v8"
    },
    "12kyc3": {
      "login-bg": "ma38v",
      "login-text-tip": "hRb-I",
      "login-btn": "-V8F1",
      "net-error-img": "KTqrZ"
    },
    "1r4gew": {
      rule: "-jSHi",
      "rule-wrap": "bkR7s",
      "rule-area": "ZHmJS",
      "rule-title": "DL-Wu",
      "rule-close-btn": "Z9pYA",
      "rule-text": "RWKHk",
      "rule-text-scroller": "JBZy1"
    }
  };

  function a() {
    try {
      const e = Array.prototype.slice.call(arguments),
        t = c;
      if (!e.length)
        return "";
      const n = e.join("__");
      if (globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n])
        return globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n];
      const r = e.shift(),
        i = r.split(" ").map(e => e.trim()).filter(e => !!e);
      if (!i.length)
        return "";
      const o = i.map(n => {
        const r = n;
        let i = "";
        return e.some(e => {
          const n = t[e] || {};
          if (n[r])
            return i = n[r],
              !0
        }),
          i
      }).filter(e => !!e).join(" ");
      return globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n] = r + " " + o,
        globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache[n]
    } catch (e) {
      console.log(e.message)
    }
    return ""
  }

  function s(e) {
    return e && "object" == typeof e && "default" in e ? e : {
      default: e
    }
  }
  globalThis.gdcgdlimitedtimegrabcoupon0020 = {},
    globalThis.gdcgdlimitedtimegrabcoupon0020.classNameCache = {};
  var u = s(t);

  function l(e, t) {
    (null == t || t > e.length) && (t = e.length);
    for (var n = 0, r = Array(t); n < t; n++)
      r[n] = e[n];
    return r
  }

  function d(e, t, n, r, i, o, c) {
    try {
      var a = e[o](c),
        s = a.value
    } catch (e) {
      return void n(e)
    }
    a.done ? t(s) : Promise.resolve(s).then(r, i)
  }

  function f(e) {
    return function () {
      var t = this,
        n = arguments;
      return new Promise(function (r, i) {
        var o = e.apply(t, n);

        function c(e) {
          d(o, r, i, c, a, "next", e)
        }

        function a(e) {
          d(o, r, i, c, a, "throw", e)
        }
        c(void 0)
      })
    }
  }

  function m(e, t, n) {
    return t = v(t),
      function (e, t) {
        if (t && ("object" == typeof t || "function" == typeof t))
          return t;
        if (void 0 !== t)
          throw new TypeError("Derived constructors may only return object or undefined");
        return function (e) {
          if (void 0 === e)
            throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
          return e
        }(e)
      }(e, w() ? Reflect.construct(t, n || [], v(e).constructor) : t.apply(e, n))
  }

  function h(e, t) {
    if (!(e instanceof t))
      throw new TypeError("Cannot call a class as a function")
  }

  function p(e, t) {
    for (var n = 0; n < t.length; n++) {
      var r = t[n];
      r.enumerable = r.enumerable || !1,
        r.configurable = !0,
      "value" in r && (r.writable = !0),
        Object.defineProperty(e, A(r.key), r)
    }
  }

  function g(e, t, n) {
    return t && p(e.prototype, t),
    n && p(e, n),
      Object.defineProperty(e, "prototype", {
        writable: !1
      }),
      e
  }

  function b(e, t, n) {
    return (t = A(t)) in e ? Object.defineProperty(e, t, {
      value: n,
      enumerable: !0,
      configurable: !0,
      writable: !0
    }) : e[t] = n,
      e
  }

  function v(e) {
    return v = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (e) {
      return e.__proto__ || Object.getPrototypeOf(e)
    },
      v(e)
  }

  function y(e, t) {
    if ("function" != typeof t && null !== t)
      throw new TypeError("Super expression must either be null or a function");
    e.prototype = Object.create(t && t.prototype, {
      constructor: {
        value: e,
        writable: !0,
        configurable: !0
      }
    }),
      Object.defineProperty(e, "prototype", {
        writable: !1
      }),
    t && C(e, t)
  }

  function w() {
    try {
      var e = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}))
    } catch (e) {}
    return (w = function () {
      return !!e
    })()
  }

  function x(e, t) {
    var n = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
      var r = Object.getOwnPropertySymbols(e);
      t && (r = r.filter(function (t) {
        return Object.getOwnPropertyDescriptor(e, t).enumerable
      })),
        n.push.apply(n, r)
    }
    return n
  }

  function k(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = null != arguments[t] ? arguments[t] : {};
      t % 2 ? x(Object(n), !0).forEach(function (t) {
        b(e, t, n[t])
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : x(Object(n)).forEach(function (t) {
        Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t))
      })
    }
    return e
  }

  function T() {

    var e, t, n = "function" == typeof Symbol ? Symbol : {},
      r = n.iterator || "@@iterator",
      i = n.toStringTag || "@@toStringTag";

    function o(n, r, i, o) {
      var s = r && r.prototype instanceof a ? r : a,
        u = Object.create(s.prototype);
      return S(u, "_invoke", function (n, r, i) {
        var o, a, s, u = 0,
          l = i || [],
          d = !1,
          f = {
            p: 0,
            n: 0,
            v: e,
            a: m,
            f: m.bind(e, 4),
            d: function (t, n) {
              return o = t,
                a = 0,
                s = e,
                f.n = n,
                c
            }
          };

        function m(n, r) {
          for (a = n,
                 s = r,
                 t = 0; !d && u && !i && t < l.length; t++) {
            var i, o = l[t],
              m = f.p,
              h = o[2];
            n > 3 ? (i = h === r) && (s = o[(a = o[4]) ? 5 : (a = 3,
              3)],
              o[4] = o[5] = e) : o[0] <= m && ((i = n < 2 && m < o[1]) ? (a = 0,
              f.v = r,
              f.n = o[1]) : m < h && (i = n < 3 || o[0] > r || r > h) && (o[4] = n,
              o[5] = r,
              f.n = h,
              a = 0))
          }
          if (i || n > 1)
            return c;
          throw d = !0,
            r
        }
        return function (i, l, h) {
          if (u > 1)
            throw TypeError("Generator is already running");
          for (d && 1 === l && m(l, h),
                 a = l,
                 s = h;
               (t = a < 2 ? e : s) || !d;) {
            o || (a ? a < 3 ? (a > 1 && (f.n = -1),
              m(a, s)) : f.n = s : f.v = s);
            try {
              if (u = 2,
                o) {
                if (a || (i = "next"),
                  t = o[i]) {
                  if (!(t = t.call(o, s)))
                    throw TypeError("iterator result is not an object");
                  if (!t.done)
                    return t;
                  s = t.value,
                  a < 2 && (a = 0)
                } else
                  1 === a && (t = o.return) && t.call(o),
                  a < 2 && (s = TypeError("The iterator does not provide a '" + i + "' method"),
                    a = 1);
                o = e
              } else if ((t = (d = f.n < 0) ? s : n.call(r, f)) !== c)
                break
            } catch (t) {
              o = e,
                a = 1,
                s = t
            } finally {
              u = 1
            }
          }
          return {
            value: t,
            done: d
          }
        }
      }(n, i, o), !0),
        u
    }
    var c = {};

    function a() {}

    function s() {}

    function u() {}
    t = Object.getPrototypeOf;
    var l = [][r] ? t(t([][r]())) : (S(t = {}, r, function () {
        return this
      }),
        t),
      d = u.prototype = a.prototype = Object.create(l);

    function f(e) {
      return Object.setPrototypeOf ? Object.setPrototypeOf(e, u) : (e.__proto__ = u,
        S(e, i, "GeneratorFunction")),
        e.prototype = Object.create(d),
        e
    }
    return s.prototype = u,
      S(d, "constructor", u),
      S(u, "constructor", s),
      s.displayName = "GeneratorFunction",
      S(u, i, "GeneratorFunction"),
      S(d),
      S(d, i, "Generator"),
      S(d, r, function () {
        return this
      }),
      S(d, "toString", function () {
        return "[object Generator]"
      }),
      (T = function () {
        return {
          w: o,
          m: f
        }
      })()
  }

  function S(e, t, n, r) {
    var i = Object.defineProperty;
    try {
      i({}, "", {})
    } catch (e) {
      i = 0
    }
    S = function (e, t, n, r) {
      if (t)
        i ? i(e, t, {
          value: n,
          enumerable: !r,
          configurable: !r,
          writable: !r
        }) : e[t] = n;
      else {
        function o(t, n) {
          S(e, t, function (e) {
            return this._invoke(t, n, e)
          })
        }
        o("next", 0),
          o("throw", 1),
          o("return", 2)
      }
    },
      S(e, t, n, r)
  }

  function C(e, t) {
    return C = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (e, t) {
      return e.__proto__ = t,
        e
    },
      C(e, t)
  }

  function j(e, t) {
    return function (e) {
      if (Array.isArray(e))
        return e
    }(e) || function (e, t) {
      var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"];
      if (null != n) {
        var r, i, o, c, a = [],
          s = !0,
          u = !1;
        try {
          if (o = (n = n.call(e)).next,
          0 === t) {
            if (Object(n) !== n)
              return;
            s = !1
          } else
            for (; !(s = (r = o.call(n)).done) && (a.push(r.value),
            a.length !== t); s = !0)
              ;
        } catch (e) {
          u = !0,
            i = e
        } finally {
          try {
            if (!s && null != n.return && (c = n.return(),
            Object(c) !== c))
              return
          } finally {
            if (u)
              throw i
          }
        }
        return a
      }
    }(e, t) || function (e, t) {
      if (e) {
        if ("string" == typeof e)
          return l(e, t);
        var n = {}.toString.call(e).slice(8, -1);
        return "Object" === n && e.constructor && (n = e.constructor.name),
          "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? l(e, t) : void 0
      }
    }(e, t) || function () {
      throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")
    }()
  }

  function A(e) {
    var t = function (e, t) {
      if ("object" != typeof e || !e)
        return e;
      var n = e[Symbol.toPrimitive];
      if (void 0 !== n) {
        var r = n.call(e, t || "default");
        if ("object" != typeof r)
          return r;
        throw new TypeError("@@toPrimitive must return a primitive value.")
      }
      return ("string" === t ? String : Number)(e)
    }(e, "string");
    return "symbol" == typeof t ? t : t + ""
  }

  function I(e) {
    return I = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) {
        return typeof e
      } :
      function (e) {
        return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e
      },
      I(e)
  }

  function M(e, t) {
    void 0 === t && (t = {});
    var n = t.insertAt;
    if (e && "undefined" != typeof document) {
      var r = document.head || document.getElementsByTagName("head")[0],
        i = document.createElement("style");
      i.type = "text/css",
        "top" === n && r.firstChild ? r.insertBefore(i, r.firstChild) : r.appendChild(i),
        i.styleSheet ? i.styleSheet.cssText = e : i.appendChild(document.createTextNode(e))
    }
  }
  var P, O, R, N, G, z;
  M(".cdzYm {\n  position: relative;\n  margin-top: 0.1rem;\n}\n.ke98Q {\n  opacity: 0.4;\n}\n._4VJuw {\n  width: 0.59rem;\n  height: 0.31rem;\n  border-top-left-radius: 0.15rem;\n  border-bottom-right-radius: 0.16rem;\n  overflow: hidden;\n  position: absolute;\n  top: 0.01rem;\n  left: 0.01rem;\n}\n.esb-l {\n  width: 0.59rem;\n  height: 0.31rem;\n}\n.c1hmE {\n  flex-direction: column;\n  align-items: center;\n}\n.wl-tA {\n  width: 1.7rem;\n  margin-top: 0.46rem;\n  padding-left: 0.08rem;\n}\n.xxxyB {\n  margin-top: 0.5rem;\n}\n.WA7tP {\n  margin-top: 0.24rem;\n  padding-left: 0.1rem;\n}\n.DZ7ZA {\n  margin-top: 0.27rem;\n}\n.O1iuo {\n  width: 1.26rem;\n  margin-top: 0.42rem;\n  padding-left: 0.05rem;\n}\n.lfLRy {\n  margin-top: 0.3rem;\n}\n.GZXGc {\n  margin-top: 0.32rem;\n}\n.MPav9 {\n  width: 2.13rem;\n  margin-top: 0.12rem;\n}\n.nsd5g {\n  margin-top: 0.07rem;\n}\n.DuHRD {\n  margin-top: 0.1rem;\n}\n.stHKf {\n  margin-top: 0.1rem;\n}\n._6E2Dr {\n  align-items: baseline;\n}\n.FE0QO {\n  font-family: AvenirLTPro-Heavy;\n  color: #FF2D19;\n}\n._5quBR {\n  font-size: 0.28rem;\n  line-height: 0.3rem;\n  font-weight: 700;\n}\n.SFzwU {\n  font-size: 0.24rem;\n  line-height: 0.26rem;\n  font-weight: 800;\n}\n.mxQPn {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  font-weight: 800;\n}\n.k3LMP {\n  font-family: AvenirLTPro-Heavy;\n  color: #FF2D19;\n}\n.iiL-i {\n  font-size: 0.6rem;\n  line-height: 0.62rem;\n  font-weight: 700;\n}\n.MXFbS {\n  font-size: 0.44rem;\n  line-height: 0.46rem;\n  font-weight: 800;\n}\n.AI9RG {\n  font-size: 0.36rem;\n  line-height: 0.38rem;\n  font-weight: 700;\n}\n.j1k9V {\n  font-family: PingFangSC-Regular;\n  font-size: 22px;\n  color: #FF2D19;\n  letter-spacing: 0;\n  text-align: center;\n  line-height: 24px;\n  font-weight: 400;\n}\n.L9f9f {\n  font-size: 0.24rem;\n  line-height: 0.26rem;\n}\n._5z0ft {\n  margin-top: -0.02rem;\n}\n.fCYZK {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n}\n.brv6d {\n  margin-top: -0.03rem;\n}\n.-PkfV {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  margin-top: -0.04rem;\n}\n.W4t1n {\n  font-size: 0.18rem;\n  line-height: 0.2rem;\n  margin-top: -0.05rem;\n}\n.LjACW {\n  flex-direction: column;\n}\n.Agpbs {\n  margin-top: 0.58rem;\n}\n.z-vdD {\n  margin-top: 0.36rem;\n}\n._1cggL {\n  margin-top: 0.5rem;\n}\n._4bowN {\n  margin-top: 0.41rem;\n}\n.KE6Lm {\n  margin-top: 0.1rem;\n}\n.w9kaE {\n  margin-top: 0.07rem;\n}\n.tEkrM {\n  width: 0.36rem;\n  height: 0.36rem;\n  margin-right: 0.04rem;\n  flex-shrink: 0;\n}\n.QEhXx {\n  width: 0.26rem;\n  height: 0.26rem;\n  margin-right: 0.02rem;\n  flex-shrink: 0;\n}\n.Cx0k2 {\n  width: 0.22rem;\n  height: 0.22rem;\n  margin-right: 0.02rem;\n  flex-shrink: 0;\n}\n._0xR7h {\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n}\n.z5SQU {\n  font-family: PingFangSC-Medium;\n  color: #222426;\n  font-weight: 500;\n}\n.a-Lrg {\n  font-size: 0.34rem;\n  line-height: 0.37rem;\n  width: 2.42rem;\n  margin-top: 0.01rem;\n}\n._7Fpi9 {\n  font-size: 0.26rem;\n  line-height: 0.28rem;\n  width: 1.56rem;\n}\n.X7Hbr {\n  font-size: 0.22rem;\n  line-height: 0.24rem;\n  max-width: 1.54rem;\n}\n.Y5yCO {\n  font-family: PingFangSC-Regular;\n  font-size: 0.24rem;\n  color: #858687;\n  font-weight: 400;\n}\n.WuXUV {\n  color: #FF2D19;\n}\n.houwq {\n  max-width: 3rem;\n}\n.CNnOZ {\n  width: 1.9rem;\n  font-size: 0.22rem;\n}\n.vUwPR {\n  font-size: 0.16rem;\n  max-width: 1.9rem;\n}\n._1vjeS {\n  margin-top: 0.05rem;\n  margin-left: 0.04rem;\n}\n.w7JrD {\n  margin-top: 0.05rem;\n}\n._9pwOE {\n  margin-top: 0.04rem;\n}\n.txF1V {\n  margin-top: 0;\n}\n.eh43s {\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n  margin-top: 0.02rem;\n}\n._4JfNV {\n  margin-top: 0.06rem;\n}\n.HLFQ8 {\n  margin-top: -0.04rem;\n}\n.QonJJ {\n  margin-top: -0.01rem;\n}\n.HX-vX {\n  height: 0.24rem;\n  align-items: center;\n}\n.pniD1 {\n  margin-top: 0.06rem;\n}\n._1scuO {\n  margin-top: 0.04rem;\n}\n.M8Hig {\n  width: 1.14rem;\n  height: 0.1rem;\n  background-color: #FF4A2633;\n  border-radius: 0.08rem;\n  margin-right: 0.08rem;\n}\n.jGMmD {\n  opacity: 0.5;\n  background: #D3D3D3;\n}\n.YJoMG {\n  height: 100%;\n  background-color: #FF2D19;\n  border-radius: 0.08rem;\n}\n.uThXg {\n  font-family: PingFangSC-Regular;\n  font-size: 0.18rem;\n  color: #FF2D19;\n  text-align: center;\n  line-height: 0.2rem;\n  font-weight: 400;\n}\n.fqsA- {\n  color: #858687;\n}\n.fNthf {\n  position: absolute;\n  border-radius: 0.32rem;\n}\n.Ad9mC {\n  background: #D3D3D37F;\n}\n.AT6eZ {\n  background-image: linear-gradient(270deg, #53C03C 0%, #67CC52 100%);\n}\n.-ZdLj {\n  opacity: 0.6;\n}\n.xotAf {\n  width: 100%;\n  height: 100%;\n}\n.Si6Wc {\n  font-family: PingFangSC-Semibold;\n  font-size: 0.28rem;\n  width: 100%;\n  color: #FFFFFF;\n  letter-spacing: 0;\n  text-align: center;\n  line-height: 0.31rem;\n  font-weight: 600;\n}\n.SO0Lm {\n  color: #858687;\n  opacity: 0.7;\n}\n.hg9CX {\n  position: absolute;\n  top: 0.18rem;\n}\n.VUi0e,\n.rIX7i {\n  font-family: PingFangSC-Medium;\n  font-size: 0.24rem;\n  line-height: 0.28rem;\n  font-weight: 500;\n}\n.mEiVc {\n  width: 1.1rem;\n  height: 0.82rem;\n  position: absolute;\n  z-index: 2;\n  top: -0.07rem;\n  right: -0.18rem;\n}\n._3Ep9L {\n  top: -0.22rem;\n}\n"),
    function (e) {
      e[e.WaiMai = 1] = "WaiMai",
        e[e.ToStore = 2] = "ToStore"
    }(P || (P = {})),
    function (e) {
      e[e.mt = 1] = "mt",
        e[e.wm = 3] = "wm"
    }(O || (O = {})),
    function (e) {
      e[e.Android = 1] = "Android",
        e[e.IOS = 2] = "IOS"
    }(R || (R = {})),
    function (e) {
      e[e.Fail = 0] = "Fail",
        e[e.NotJoin = 2] = "NotJoin",
        e[e.Success = 3] = "Success",
        e[e.GrabOver = 4] = "GrabOver",
        e[e.CantGrabRep = 5] = "CantGrabRep",
        e[e.ReachedLimit = 8] = "ReachedLimit"
    }(N || (N = {})),
    function (e) {
      e.WillBegin = "willBegin",
        e.IsDoing = "isDoing",
        e.Miss = "miss"
    }(G || (G = {})),
    function (e) {
      e.WillBegin = "willBegin",
        e.CanCatch = "canCatch",
        e.ToUse = "toUse",
        e.InUse = "inUse",
        e.Used = "used",
        e.OverTime = "overTime",
        e.GrabOver = "grabOver",
        e.Miss = "miss",
        e.RemindMe = "remindMe",
        e.Reminded = "reminded",
        e.CantCatch = "cantCatch",
        e.ReceivedAlready = "receivedAlready"
    }(z || (z = {}));
  var D, U, E, F, _ = {
    willBegin: "即将开始",
    canCatch: "立即抢券",
    toUse: "去使用",
    inUse: "去使用",
    used: "已使用",
    overTime: "已过期",
    grabOver: "已抢完",
    miss: "已结束",
    remindMe: "提醒我",
    reminded: "已提醒",
    cantCatch: "不可领取",
    receivedAlready: "已领取"
  };
  ! function (e) {
    e[e.NotUse = 0] = "NotUse",
      e[e.Used = 1] = "Used",
      e[e.OverTime = 2] = "OverTime"
  }(D || (D = {})),
    function (e) {
      e.Custom = "custom",
        e.SqjNoema = "sqjNoema"
    }(U || (U = {})),
    function (e) {
      e.OneLine1 = "1",
        e.OneLine2 = "2",
        e.OneLine3 = "3"
    }(E || (E = {})),
    function (e) {
      e[e.NeedLogin = 0] = "NeedLogin",
        e[e.NoInTime = 1] = "NoInTime",
        e[e.OtherAnomalies = 2] = "OtherAnomalies"
    }(F || (F = {}));
  var L = u.default.gdMonitor,
    V = u.default.gdUtil,
    W = function (e, t, n) {
      var r, i, o, c = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : "",
        a = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : "",
        s = V.getGlobalData();
      L.addError({
        error: {
          name: "[gd-limited-time-grab-coupon] > [".concat(e, "-err]:code-").concat(null == t ? void 0 : t.code, ",subcode-").concat(null == t || null === (r = t.data) || void 0 === r ? void 0 : r.subCode, ",status-").concat(null == t || null === (i = t.data) || void 0 === i ? void 0 : i.status, ",roundCode-").concat(c, ",rightCode-").concat(a),
          msg: "神券节限时抢玩法-".concat(n, ",gdId-").concat(s.gdId, ",pageId-").concat(s.pageId)
        },
        opts: {
          category: null === globalThis || void 0 === globalThis || null === (o = globalThis.Owl) || void 0 === o || null === (o = o.errorModel) || void 0 === o || null === (o = o.CATEGORY) || void 0 === o ? void 0 : o.AJAX,
          level: "warn"
        }
      })
    },
    B = u.default.env,
    q = u.default.gdUtil,
    H = u.default.gdFeature,
    X = "wxde8ac0a21135c07d",
    J = "wx2c348cf579062e56",
    Q = function () {
      var e, t = String((null === (e = window) || void 0 === e || null === (e = e.navigator) || void 0 === e ? void 0 : e.userAgent) || "").toLowerCase();
      return B.isWxMpWm || t.includes(J) ? J : B.isWxMpMtWm || B.isWxMpMtSg || B.isWxMpMtThh || t.includes(X) ? X : ""
    },
    Z = function (e) {
      n.Toast.show({
        message: e
      })
    },
    K = function () {
      try {
        var e, t = q.getGlobalData(),
          n = null == t ? void 0 : t.pageId,
          r = null == t || null === (e = t.config) || void 0 === e ? void 0 : e.limitedSaleWithRefreshConfig,
          i = r.delayTime,
          o = r.limitedSaleWithRefreshDataList;
        o && o.length > 0 && o.includes(String(n)) && setTimeout(function () {
          u.default.gdEvent.emit("refreshSupply", {})
        }, i)
      } catch (e) {
        console.log("[gd-limited-time-grab-coupon] - handleEmitGrabSuccess - error", e),
          W("handleEmitGrabSuccess", e, "限时抢组件通知供给事件失败")
      }
    },
    Y = function () {
      console.log("report click of gd-limited-time-grab-coupon"),
        u.default.gdEvent.emit("wm-coupon-festival-tab-click", {})
    };

  function $(e, t) {
    var n = t ? {
        local: "//promotion.waimai.test.sankuai.com",
        test: "//promotion.waimai.test.sankuai.com",
        stage: "//promotion.waimai.st.sankuai.com",
        prod: "//promotion.waimai.meituan.com"
      } : {
        local: "//rights.tsp.test.sankuai.com",
        test: "//rights.tsp.test.sankuai.com",
        stage: "//rights-apigw.tsp.st.sankuai.com",
        prod: "//rights-apigw.meituan.com"
      },
      r = "";
    return r = B.isLocal && Mach.env.isH5 ? n.local + e : B.isTest ? n.test + e : B.isStage ? n.stage + e : (B.isProd,
    n.prod + e),
    Mach.env.isH5 || (r = "https:".concat(r)),
      r
  }
  var ee = function (e) {
    return e && e < 1e3 ? parseInt(String(1e6 * e), 10) : parseInt(e, 10)
  };

  function te(e) {
    return ne.apply(this, arguments)
  }

  function ne() {
    return ne = f(T().m(function e(t) {
      var n, r, i, o, c, a, s, l, d, f, m, h, p, g, b, v, y, w, x, S, C, j, A, I, M, P, O, R = arguments;
      return T().w(function (e) {
        for (;;)
          switch (e.n) {
            case 0:
              return n = R.length > 1 && void 0 !== R[1] ? R[1] : "",
                r = R.length > 2 ? R[2] : void 0,
                i = "",
                o = {
                  fpPlatform: "",
                  wxOpenId: "",
                  appVersion: ""
                },
                e.p = 1,
                e.n = 2,
                u.default.gdUtil.getCtypeValue();
            case 2:
              return i = e.v,
                e.n = 3,
                u.default.gdUtil.getRiskParams();
            case 3:
              o = e.v,
                e.n = 5;
              break;
            case 4:
              e.p = 4,
                O = e.v,
                console.log(O);
            case 5:
              return c = r ? {
                appVersion: o.appVersion
              } : {
                fpPlatform: o.fpPlatform,
                wx_openid: o.wxOpenId,
                appVersion: o.appVersion
              },
                a = q.qs(),
                s = a.wm_longitude,
                l = void 0 === s ? 0 : s,
                d = a.wm_latitude,
                f = void 0 === d ? 0 : d,
                m = a.wm_actual_latitude,
                h = void 0 === m ? 0 : m,
                p = a.wm_actual_longitude,
                g = void 0 === p ? 0 : p,
                b = {
                  lat: 0,
                  lng: 0
                },
                e.p = 6,
                e.n = 7,
                H.location();
            case 7:
              b = e.v,
                e.n = 9;
              break;
            case 8:
              e.p = 8,
                console.log("【神券节限时抢玩法组件】 gdFeature.location获取定位信息出错");
            case 9:
              return y = (v = b).lat,
                w = void 0 === y ? 0 : y,
                x = v.lng,
                S = void 0 === x ? 0 : x,
                C = ee(Number(f) || w),
                j = ee(Number(l) || S),
                A = ee(Number(h) || w),
                I = ee(Number(g) || S),
                M = q.getGlobalData(),
                P = k(k({
                  gdId: M.gdId,
                  pageId: M.pageId,
                  instanceId: n,
                  cType: i
                }, c), {}, {
                  latitude: C,
                  longitude: j,
                  actualLatitude: A,
                  actualLongitude: I,
                  appid: Q()
                }),
                e.a(2, k({
                  activityId: t
                }, P))
          }
      }, e, null, [
        [6, 8],
        [1, 4]
      ])
    })),
      ne.apply(this, arguments)
  }
  var re = function (e) {
    return Object.keys(e).map(function (t) {
      return "".concat(encodeURIComponent(t), "=").concat(encodeURIComponent(e[t]))
    }).join("&")
  };

  function ie(e) {
    if ("object" !== I(e) || null === e)
      return e;
    var t = Array.isArray(e) ? [] : {};
    return Object.keys(e).forEach(function (n) {
      "dom" !== n && e.hasOwnProperty(n) && (t[n] = ie(e[n]))
    }),
      t
  }
  var oe = function () {
      H.login().then(function () {
        q.reload()
      }).catch(function (e) {
        console.log("login err", e)
      })
    },
    ce = u.default.env,
    ae = u.default.gdBridge,
    se = u.default.gdUtil,
    ue = b(b(b({}, E.OneLine1, {
      bgSrc: "https://p1.meituan.net/dptakeaway/803636e963880a3e20d8ff6c2386cded15943.png",
      btnSrc: "https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png",
      greyBtnSrc: "https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png",
      bgStyle: {
        height: "192rpx",
        width: "654rpx"
      },
      btnStyle: {
        height: "64rpx",
        width: "160rpx",
        top: "64rpx",
        right: "24rpx"
      }
    }), E.OneLine2, {
      bgSrc: "https://p0.meituan.net/dptakeaway/cc358d46923a3e7cc070a7ff6d2455b81802.png",
      btnSrc: "https://p0.meituan.net/dptakeaway/b6965689f2b53d213f0d07a00f8b224712407.png",
      greyBtnSrc: "https://p1.meituan.net/dptakeaway/93c96ff73c053ce60722fa1239f62c417258.png",
      bgStyle: {
        height: "192rpx",
        width: "323rpx"
      },
      btnStyle: {
        height: "44rpx",
        width: "299rpx",
        left: "12rpx",
        bottom: "12rpx"
      }
    }), E.OneLine3, {
      bgSrc: "https://p1.meituan.net/dptakeaway/89fb4165026bc44898aedffcfd4225ba1624.png",
      btnSrc: "https://p0.meituan.net/dptakeaway/4578a9f7619239a937135db75a7621e78377.png",
      greyBtnSrc: "https://p0.meituan.net/dptakeaway/b35268acc04cd46e3dee466be21e206d5110.png",
      bgStyle: {
        height: "192rpx",
        width: "213rpx"
      },
      btnStyle: {
        height: "40rpx",
        width: "189rpx",
        left: "12rpx",
        bottom: "12rpx"
      }
    }),
    le = b(b(b({}, E.OneLine1, {
      bgSrc: "https://p0.meituan.net/dptakeaway/7560b5e98cb7516f17bd72e1a9f09b2812143.png",
      btnSrc: "https://p1.meituan.net/dptakeaway/bc506ee9341bef6519277b645bb0e2d44361.png",
      greyBtnSrc: "https://p1.meituan.net/dptakeaway/cc697981896053ce0b60bb778ad55c553666.png",
      bgStyle: {
        height: "140rpx",
        width: "654rpx"
      },
      btnStyle: {
        height: "64rpx",
        width: "160rpx",
        top: "38rpx",
        right: "24rpx"
      }
    }), E.OneLine2, {
      bgSrc: "https://p0.meituan.net/dptakeaway/4eec8a9e40a93db7210b2764c2d98bbc1629.png",
      btnSrc: "https://p0.meituan.net/dptakeaway/9ba2b4f20164ce3c8db75101438ad79f12264.png",
      greyBtnSrc: "https://p0.meituan.net/dptakeaway/d8daab6b2047692a41b631c1948deb1c7333.png",
      bgStyle: {
        height: "162rpx",
        width: "323rpx"
      },
      btnStyle: {
        height: "44rpx",
        width: "307rpx",
        left: "8rpx",
        bottom: "8rpx"
      }
    }), E.OneLine3, {
      bgSrc: "https://p1.meituan.net/dptakeaway/15819d000a62221efe319bf03648e4c11498.png",
      btnSrc: "https://p1.meituan.net/dptakeaway/9c0bbb0cb1df52871e469811f150e9a18526.png",
      greyBtnSrc: "https://p0.meituan.net/dptakeaway/471f4ef0f3c07ddd37a6d6083e63f6635196.png",
      bgStyle: {
        height: "162rpx",
        width: "213rpx"
      },
      btnStyle: {
        height: "40rpx",
        width: "197rpx",
        left: "8rpx",
        bottom: "8rpx"
      }
    });
  var de = function (e) {
      var t = se.getGlobalData(),
        n = e.split("?")[0],
        i = r.qs(e),
        o = r.qs(),
        c = {
          utm_term: t.gdId || ""
        },
        a = {
          utm_source: ""
        };
      return Object.keys(o).forEach(function (e) {
        "utm_source" === e && (a.utm_source = o[e])
      }),
        i = k(k(k({}, i), c), a),
        Object.keys(i).forEach(function (e) {
          n += "".concat((n.split("?")[1] ? "&" : "?") + e, "=").concat(i[e])
        }),
        n
    },
    fe = function (e) {
      e === P.ToStore && ((u.default.env.isWxMpWm || u.default.env.isWmApp) && Z("请打开美团app-我的-红包卡券中查看"),
      (u.default.env.isMtApp || u.default.env.isWxMpMtWm) && Z("请在我的-红包卡券中查看"),
      u.default.env.isDpApp && Z("请在我的-卡券中查看")),
      e === P.WaiMai && Z("请前往我的-红包卡券中查看并使用")
    };

  function me() {
    return (me = f(T().m(function e(t, n) {
      var r;
      return T().w(function (e) {
        for (;;)
          switch (e.n) {
            case 0:
              if (t) {
                e.n = 1;
                break
              }
              return fe(n),
                e.a(2);
            case 1:
              if (!(ce.isKSWm || t.startsWith("https://") || t.startsWith("http://") || t.startsWith("meituanwaimai://") || t.startsWith("imeituan://") || t.startsWith("dianping://") || t.startsWith("meituanshangou://"))) {
                e.n = 2;
                break
              }
              return se.redirectTo(t),
                e.a(2);
            case 2:
              return e.n = 3,
                ce.isWxMp();
            case 3:
              if (e.v)
                if (r = ae.getBridge().wx,
                "/pages/index/index" === t) {
                  t = de(t);
                  try {
                    r.miniProgram.switchTab({
                      url: t,
                      success: function (e) {
                        console.log("跳转小程序成功:".concat(t))
                      },
                      fail: function (e) {
                        fe(n),
                          console.log("跳转小程序失败:".concat(t, ",").concat(e))
                      }
                    })
                  } catch (e) {
                    console.log("跳转小程序失败:".concat(t, ",").concat(e))
                  }
                } else
                  try {
                    r.miniProgram.navigateTo({
                      url: t,
                      success: function (e) {
                        console.log("跳转小程序成功:".concat(t))
                      },
                      fail: function (e) {
                        fe(n),
                          console.log("跳转小程序失败:".concat(t, ",").concat(e))
                      }
                    })
                  } catch (e) {
                    console.log("跳转小程序失败:".concat(t, ",").concat(e))
                  }
              else
                se.redirectTo(t);
            case 4:
              return e.a(2)
          }
      }, e)
    }))).apply(this, arguments)
  }
  var he = function (e, t) {
    var n = {
      status: "",
      statusText: ""
    };
    return t && t.length >= 2 && (n.status = G.WillBegin,
      n.statusText = "距开始",
    e >= t[0] && (n.status = G.IsDoing,
      n.statusText = "抢券中"),
    e >= t[1] && (n.status = G.Miss,
      n.statusText = "已结束")),
      n
  };

  function pe(e) {
    return e.toString().length < 13 ? 1e3 * e : e
  }

  function ge(e, t) {
    var n = new Date(t),
      r = n.getFullYear(),
      i = String(n.getMonth() + 1).padStart(2, "0"),
      o = String(n.getDate()).padStart(2, "0"),
      c = Mach.env.isH5,
      a = c ? "".concat(r, "/").concat(i, "/").concat(o) : "".concat(r, "-").concat(i, "-").concat(o);
    return e.map(function (e) {
      return new Date(a + (c ? " " : "T") + e).getTime()
    })
  }

  function be(e) {
    return e.toString().padStart(2, "0")
  }

  function ve(e) {
    if (e <= 0)
      return {
        h: "00",
        m: "00",
        s: "00"
      };
    var t = Math.ceil(e),
      n = Math.floor(t / 3600),
      r = Math.floor(t % 3600 / 60),
      i = t % 60;
    return {
      h: be(n),
      m: be(r),
      s: be(i)
    }
  }

  function ye(e) {
    if (!e)
      return "";
    var t = new Date(pe(e));
    t.getFullYear();
    var n = String(t.getMonth() + 1).padStart(2, "0"),
      r = String(t.getDate()).padStart(2, "0");
    return String(t.getHours()).padStart(2, "0"),
      String(t.getMinutes()).padStart(2, "0"),
      String(t.getSeconds()).padStart(2, "0"),
      "".concat(n, "月").concat(r, "日")
  }
  var we = function (e) {
      var t = j(e.split(":").map(Number), 3);
      return 3600 * t[0] + 60 * t[1] + t[2]
    },
    xe = function (t, n, r, i, o) {
      var c = j(e.useState(n), 2),
        a = c[0],
        s = c[1],
        u = e.useRef(0),
        l = e.useRef(0),
        d = e.useRef(null),
        f = e.useCallback(function (e) {
          var t, n, c, a, d, f = ie(e),
            m = !1;
          if (u.current > 0) {
            var h = Date.now();
            u.current += h - l.current;
            var p = u.current;
            l.current = h,
              f.couponStartTime = pe(f.couponStartTime),
              f.couponEndTime = pe(f.couponEndTime),
              f.couponUseTime = pe(f.couponUseTime);
            var g = f.couponEndTime - p;
            if (f.showStartTime = ye(f.couponStartTime),
              f.showEndTime = ye(f.couponEndTime),
              f.showUseTime = ye(f.couponUseTime),
            p >= f.couponStartTime && p <= f.couponEndTime) {
              m = !0;
              var b = ve(g / 1e3),
                v = b.h,
                y = b.m,
                w = b.s;
              f.countDownDate = "".concat(v, ":").concat(y, ":").concat(w),
                f.isOneDay = g <= 864e5
            }
            p > f.couponEndTime && (m = !1,
              f.couponStatus = D.OverTime),
              f.showStatus = function (e, t, n, r, i) {
                if (e.status === N.Success) {
                  if (e.couponStatus === D.NotUse)
                    return n ? z.InUse : z.ToUse;
                  if (e.couponStatus === D.Used)
                    return z.Used;
                  if (e.couponStatus === D.OverTime)
                    return z.OverTime
                }
                return e.status === N.ReachedLimit ? z.ReceivedAlready : t === G.WillBegin ? e.status === N.CantGrabRep ? z.CantCatch : i ? r ? z.Reminded : z.RemindMe : z.WillBegin : t === G.IsDoing ? e.status === N.CantGrabRep ? z.CantCatch : e.status === N.GrabOver ? z.GrabOver : e.status === N.NotJoin ? z.CanCatch : z.GrabOver : t === G.Miss ? z.Miss : void 0
              }(f, r, m, i, o),
              f.progressPercent = r === G.Miss || z.GrabOver === f.showStatus ? 100 : Math.round((e.totalStock - e.residueStock) / e.totalStock * 100),
            f.progressPercent > 100 && (f.progressPercent = 100),
            (f.progressPercent < 0 || !f.progressPercent) && (f.progressPercent = 0),
              f.processWidth = [G.WillBegin, G.Miss].includes(r) || z.GrabOver === f.showStatus ? "0%" : r !== G.WillBegin && f.progressPercent < 10 ? "10%" : "".concat(f.progressPercent, "%"),
              f.grabProcessText = r === G.WillBegin ? "未开始" : f.progressPercent < 28 ? "疯抢中" : f.progressPercent >= 28 && f.progressPercent < 85 ? "".concat(f.progressPercent, "%") : f.progressPercent >= 85 && f.progressPercent < 100 ? "即将抢完" : r === G.Miss ? "已结束" : "已抢完",
              f.grabProcessIsGrayStyle = [z.GrabOver, z.Miss].includes(f.showStatus),
              f.isShowGrabProcess = r === G.Miss || [z.CanCatch, z.GrabOver, z.Miss].includes(f.showStatus),
              f.is18Coupon = (t = f.couponStartTime,
                n = f.couponEndTime,
                c = new Date(t),
                a = new Date(n),
              !!(d = 18 === c.getDate() && 18 === a.getDate()) && c.getFullYear() === a.getFullYear() && c.getMonth() === a.getMonth() && d)
          }
          s(f)
        }, [r, o, i]);
      return e.useEffect(function () {
        return u.current = t,
          l.current = Date.now(),
          f(n),
          d.current = setInterval(function () {
            f(n)
          }, 1e3),
          function () {
            clearInterval(d.current)
          }
      }, [n, r, t, f]), {
        couponItemInfo: a
      }
    },
    ke = {
      isDoing: 0,
      willBegin: 1,
      miss: 2
    },
    Te = Mach.env.isH5,
    Se = Mach.env.isAndroid,
    Ce = function (n) {
      var o = n.couponItemData,
        c = n.isMultipleTimeTabs,
        s = n.sqjTagIcon,
        u = n.oneLineCouponNum,
        l = n.currentTime,
        d = n.currentGrabStatus,
        f = n.activeRoundTimeStatus,
        m = n.handelGetAwardCoupon,
        h = n.remindStatus,
        p = n.handleRemind,
        g = n.enableRemind,
        v = n.index,
        y = n.btnTextMap,
        w = n.logView,
        x = n.activeTabIndex,
        T = n.logClick,
        S = xe(l, o, f, h, g).couponItemInfo,
        C = e.useMemo(function () {
          return S.showStatus
        }, [S]),
        j = e.useCallback(function (e) {
          var t = e.status,
            n = e.result;
          T("b_waimai_u6bmx6y1_mc", {
            tab_id: x,
            tab_index: x,
            coupon_id: S.couponId,
            right_code: S.rightCode,
            vp_seckill_type: ke[f],
            vp_stock_type: +(S.status !== N.GrabOver),
            status: t,
            index: v,
            has_result: n
          })
        }, [f, x, S.couponId, S.rightCode, S.status, v, T]),
        A = e.useCallback(function () {
          w("b_waimai_u6bmx6y1_mv", {
            tab_id: x,
            tab_index: x,
            coupon_id: S.couponId,
            vp_seckill_type: ke[d],
            vp_stock_type: +(S.status !== N.GrabOver),
            index: v,
            status: +(S.status === N.Success),
            has_result: S.status === N.Success ? 0 : 1,
            right_code: S.rightCode
          })
        }, [x, S.couponId, S.rightCode, S.status, d, v, w]),
        I = e.useCallback(function () {
          if (Y(),
            [z.CantCatch, z.ReceivedAlready].includes(C))
            S.toastMsg && Z(S.toastMsg);
          else if (C !== z.RemindMe && C !== z.Reminded)
            if (C !== z.CanCatch)
              if (C !== z.ToUse) {
                if (C === z.InUse) {
                  var e = +(S.status === N.Success);
                  j({
                    status: e,
                    result: 0
                  }),
                    function (e, t) {
                      me.apply(this, arguments)
                    }(S.couponDirectLink, S.couponAssetType)
                }
              } else
                Z("还未到可用时间~");
            else
              m({
                rightCode: S.rightCode,
                handleLogClick: j
              });
          else
            p()
        }, [S.couponAssetType, S.couponDirectLink, S.rightCode, S.toastMsg, C, S.status, m, j, p]),
        M = e.useMemo(function () {
          return function (e, t) {
            return e ? le["".concat(t)] : ue["".concat(t)]
          }(c, u) || {}
        }, [c, u]),
        P = e.useMemo(function () {
          return [z.CanCatch, z.InUse].includes(C) ? null == M ? void 0 : M.btnSrc : [z.WillBegin, z.ToUse].includes(C) ? null == M ? void 0 : M.greyBtnSrc : ""
        }, [C, null == M ? void 0 : M.btnSrc, null == M ? void 0 : M.greyBtnSrc]),
        O = e.useMemo(function () {
          return [z.CanCatch, z.InUse, z.WillBegin, z.ToUse].includes(C) && 1 === u ? k(k({}, M.btnStyle), {}, {
            height: "100rpx",
            width: "160rpx"
          }) : M.btnStyle
        }, [C, u, M.btnStyle]),
        R = e.useMemo(function () {
          return [z.GrabOver, z.Miss, z.Used, z.OverTime, z.CantCatch, z.ReceivedAlready].includes(C)
        }, [C]),
        G = e.useMemo(function () {
          return [z.GrabOver, z.Used, z.OverTime, z.CantCatch, z.ReceivedAlready].includes(C)
        }, [C]),
        D = e.useMemo(function () {
          return i.jsxs(t.View, {
            className: a(r.classNames("progress-bar", "progress-bar-".concat(u)), "1dezzt"),
            children: [i.jsx(t.View, {
              className: a(r.classNames("progress-bar-outer", {
                "progress-bar-outer-gray": S.grabProcessIsGrayStyle
              }), "1dezzt"),
              children: i.jsx(t.View, {
                className: a("progress-bar-inner ", "1dezzt"),
                style: {
                  width: "".concat(S.processWidth)
                }
              })
            }), i.jsx(t.Text, {
              content: S.grabProcessText,
              className: a(r.classNames("progress-bar-text", {
                "progress-bar-text-gray": S.grabProcessIsGrayStyle
              }), "1dezzt")
            })]
          })
        }, [u, S.grabProcessIsGrayStyle, S.processWidth, S.grabProcessText]),
        U = e.useMemo(function () {
          return C === z.Used ? i.jsx(t.Text, {
            content: "".concat(S.showUseTime, "已使用"),
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : C === z.OverTime ? i.jsx(t.Text, {
            content: "".concat(S.showEndTime, "到期"),
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : C === z.ToUse ? i.jsx(t.Text, {
            content: "".concat(S.showStartTime, "起可用"),
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : C === z.InUse ? S.isOneDay ? i.jsx(t.Text, {
            content: "".concat(S.countDownDate, "后到期"),
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : i.jsx(t.Text, {
            content: "".concat(S.showStartTime, "起可用"),
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : [z.CantCatch, z.ReceivedAlready].includes(C) ? S.coverMsg && i.jsx(t.Text, {
            content: S.coverMsg,
            className: a(r.classNames("coupon-desc-text", "coupon-desc-text-red", "coupon-desc-text-".concat(u)), "1dezzt")
          }) : D
        }, [C, D, S.showUseTime, S.showEndTime, S.showStartTime, S.isOneDay, S.countDownDate, S.coverMsg, u]),
        E = e.useMemo(function () {
          return S.is18Coupon && C === z.ToUse && 1 === u
        }, [S.is18Coupon, C, u]);
      return i.jsxs(t.ImageBackground, {
        resizeMode: "scaleToFill",
        src: M.bgSrc,
        style: k(k({}, M.bgStyle), {}, {
          flexDirection: u > 2 ? "column" : "row",
          flexWrap: u > 2 ? "nowrap" : "wrap"
        }),
        className: a("coupon-wrapper", "1dezzt"),
        onClick: I,
        onView: A,
        children: [i.jsx(t.View, {
          className: a("sqj-icon-wrapper", "1dezzt"),
          children: i.jsx(t.Image, {
            resizeMode: "scaleToFill",
            src: s,
            className: a("sqj-icon", "1dezzt")
          })
        }), i.jsxs(t.View, {
          className: a(r.classNames("price-wrapper", "price-wrapper-".concat(u), b(b(b(b(b({}, "price-wrapper-".concat(u, "-multi"), c), "price-wrapper-".concat(u, "-isAndroid"), Se), "price-wrapper-".concat(u, "-multi-H5"), c && Te), "price-wrapper-".concat(u, "-multi-isAndroid"), c && Se), "grab-style", G)), "1dezzt"),
          children: [i.jsxs(t.View, {
            className: a("price-content", "1dezzt"),
            children: [i.jsx(t.Text, {
              content: "¥",
              className: a("price-unit price-unit-".concat(u), "1dezzt")
            }), i.jsx(t.Text, {
              content: "".concat(S.couponAmount),
              className: a("price-number price-number-".concat(u), "1dezzt")
            })]
          }), i.jsx(t.Text, {
            className: a(r.classNames("price-limit", "price-limit-".concat(u), b(b({}, "price-limit-".concat(u, "-isAndroid"), Se), "price-limit-".concat(u, "-multi"), c)), "1dezzt"),
            content: S.couponAmountLimit > 0 ? "满".concat(S.couponAmountLimit, "可用") : "无门槛"
          })]
        }), i.jsxs(t.View, {
          className: a(r.classNames("coupon-info", "coupon-info-".concat(u), b({}, "coupon-info-".concat(u, "-multi"), c)), "1dezzt"),
          children: [i.jsxs(t.View, {
            className: a(r.classNames("coupon-name-".concat(u), {
              "grab-style": G
            }), "1dezzt"),
            children: [i.jsx(t.Image, {
              resizeMode: "scaleToFill",
              src: S.couponIconUrl,
              className: a("coupon-icon-".concat(u), "1dezzt")
            }), i.jsx(t.Text, {
              className: a(r.classNames("coupon-title", "coupon-title-".concat(u)), "1dezzt"),
              content: S.couponName
            })]
          }), i.jsx(t.View, {
            className: a(r.classNames("coupon-desc-".concat(u), b({}, "coupon-desc-".concat(u, "-H5"), Te), b({}, "coupon-desc-".concat(u, "-isAndroid"), Se), b({}, "coupon-desc-".concat(u, "-multi"), c), b({}, "coupon-desc-".concat(u, "-multi-H5"), c && Te)), "1dezzt"),
            children: U
          })]
        }), i.jsx(t.View, {
          className: a(r.classNames("btn", "btn-".concat(u), {
            "btn-disable": R,
            "btn-remindme": [z.RemindMe, z.Reminded].includes(C),
            "btn-remindme-done": [z.Reminded].includes(C)
          }), "1dezzt"),
          style: k(k({}, O), {}, {
            flexShrink: 0
          }),
          children: P ? i.jsx(t.ImageBackground, {
            src: P,
            className: a("btn-bg", "1dezzt"),
            resizeMode: "scaleToFill",
            children: i.jsx(t.Text, {
              onClick: I,
              content: y[C],
              className: a(r.classNames("btn-text", "btn-text-".concat(u), {
                "btn-text-disable": R
              }), "1dezzt")
            })
          }) : i.jsx(t.View, {
            className: a("btn-bg", "1dezzt"),
            children: i.jsx(t.Text, {
              onClick: I,
              content: y[C],
              className: a(r.classNames("btn-text", "btn-text-".concat(u), {
                "btn-text-disable": R
              }), "1dezzt")
            })
          })
        }), E && i.jsx(t.Image, {
          resizeMode: "aspectFill",
          src: "https://p0.meituan.net/dptakeaway/0c251c605452992573bd88180df8ab769791.png",
          className: a(r.classNames("eighteen-tag", {
            "eighteen-tag-multiple-time": c
          }), "1dezzt")
        })]
      })
    };
  M(".ezvKs {\n  width: 7.02rem;\n  height: 0.65rem;\n  justify-content: space-between;\n}\n.IIgVT {\n  font-family: AvenirLTPro-Heavy;\n  font-size: 0.32rem;\n  color: #222426;\n  text-align: center;\n  font-weight: 700;\n  margin-top: 0.3rem;\n}\n.LHM5y {\n  margin-top: 0.25rem;\n}\n.VtXxq {\n  font-family: PingFangSC-Medium;\n  font-size: 0.24rem;\n  color: #222426;\n  text-align: center;\n  line-height: 0.26rem;\n  font-weight: 500;\n  margin-top: 0.27rem;\n  margin-left: 0.12rem;\n}\n._9--xS {\n  margin-top: 0.26rem;\n  margin-left: 0.16rem;\n}\n.HsC7X {\n  font-family: PingFangSC-Medium;\n  font-size: 0.24rem;\n  color: #222426;\n  text-align: center;\n  line-height: 0.26rem;\n  font-weight: 500;\n  margin-right: 0.12rem;\n  margin-top: 0.25rem;\n}\n.D9q7s {\n  height: 0.36rem;\n  margin-top: 0.25rem;\n}\n.nsvqX {\n  color: #ffffff;\n  background: #222426;\n  border-radius: 0.08rem;\n  font-weight: 600;\n  font-family: PingFangSC-Semibold;\n  font-size: 0.22rem;\n  width: 0.36rem;\n  height: 0.36rem;\n  line-height: 0.36rem;\n  text-align: center;\n}\n.GDFLV {\n  color: #222426;\n  width: 0.12rem;\n  font-weight: 600;\n  font-family: PingFangSC-Semibold;\n  font-size: 0.24rem;\n  text-align: center;\n}\n.uos5M {\n  height: 0.94rem;\n  padding-top: 0.22rem;\n}\n.XcZDm {\n  width: 6.54rem;\n}\n._0-T-U {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-sizing: border-box;\n  flex-shrink: 0;\n}\n.EvSyQ {\n  font-family: PingFangSC-Medium;\n  font-size: 0.32rem;\n  color: #1E1818;\n  text-align: center;\n  line-height: 0.34rem;\n  font-weight: 500;\n  margin-top: 0.04rem;\n}\n.lE3Qo {\n  font-family: PingFangSC-Regular;\n  font-size: 0.24rem;\n  color: #1E1818;\n  text-align: center;\n  font-weight: 400;\n  line-height: 0.26rem;\n  margin-top: 0.08rem;\n}\n.lRIid {\n  font-weight: 500;\n}\n.bk-2y {\n  height: 0.36rem;\n  flex-shrink: 0;\n}\n.xUqe6 {\n  background: #222426;\n  border-radius: 0.08rem;\n  font-weight: 600;\n  font-family: PingFangSC-Semibold;\n  font-size: 0.22rem;\n  width: 0.36rem;\n  height: 0.36rem;\n  line-height: 0.36rem;\n  display: inline-block;\n  letter-spacing: 0;\n  text-align: center;\n}\n.t3TKG {\n  color: #222426;\n  width: 0.12rem;\n  font-weight: 600;\n  font-family: PingFangSC-Semibold;\n  font-size: 0.24rem;\n  letter-spacing: 0;\n  text-align: center;\n}\n");
  var je = function (t) {
      var n = t.grabRounds,
        i = t.getData,
        o = t.activeRoundCode,
        c = t.currentTime,
        a = t.isSqjNoema,
        s = t.autoScrollTab,
        l = t.logClick,
        d = e.useRef(null),
        f = e.useRef(0),
        m = e.useRef(0),
        h = e.useRef(null),
        p = e.useRef(-1),
        g = j(e.useState([]), 2),
        b = g[0],
        v = g[1],
        y = e.useCallback(function (e) {
          var t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
          Y(),
          a && t && u.default.gdEvent.emit("wm-coupon-festival-tab-show", {
            isStop: !0
          });
          var n = b[e];
          l("b_waimai_gouv1y6s_mc", {
            activity_time: [n.startTime, n.endTime]
          }),
            i({
              roundCode: n.roundCode
            })
        }, [a, b, l, i]),
        w = e.useMemo(function () {
          return r.throttle(y, 1e3)
        }, [y]),
        x = e.useCallback(function () {
          var e, t = (new Date).getTime();
          f.current += t - m.current,
            m.current = t,
            v(function (e) {
              p.current = e.findIndex(function (e) {
                return e.status === G.WillBegin
              });
              var t = n.map(function (t, r) {
                var o, c, a, s, u, l, d = ge([t.startTime, t.endTime], f.current),
                  m = he(f.current, d),
                  h = m.status,
                  g = m.statusText,
                  b = 0,
                  v = e[r];
                if (h === G.Miss) {
                  if ((null == v ? void 0 : v.status) === G.IsDoing && r < n.length - 1) {
                    var y, w = null === (y = n[r + 1]) || void 0 === y ? void 0 : y.roundCode;
                    i({
                      roundCode: w
                    })
                  }
                  p.current = r < n.length - 1 ? r + 1 : r,
                    b = 0
                }
                if (h === G.IsDoing) {
                  if (p.current = r,
                  (null == v ? void 0 : v.status) === G.WillBegin) {
                    var x, T = null === (x = n[r]) || void 0 === x ? void 0 : x.roundCode;
                    i({
                      roundCode: T
                    })
                  }
                  b = (d[1] - f.current) / 1e3
                }
                return h === G.WillBegin && (b = (d[0] - f.current) / 1e3),
                  k(k({}, t), {}, {
                    status: h,
                    statusText: g,
                    timeTxt: (o = t.startTime,
                      c = j(o.split(":"), 2),
                      a = c[0],
                      s = void 0 === a ? "" : a,
                      u = c[1],
                      l = void 0 === u ? "" : u,
                      "".concat(s, ":").concat(l)),
                    countDown: ve(b)
                  })
              });
              return t
            }),
          (e = f.current) >= ge(["23:59:59"], e)[0] && setTimeout(function () {
            i({})
          }, 900)
        }, [n, i]),
        T = e.useMemo(function () {
          var e = b.findIndex(function (e) {
            return e.roundCode === o
          });
          return e < 0 && (e = b.length - 1),
            e
        }, [o, b]);
      return e.useEffect(function () {
        b.length >= 5 && setTimeout(function () {
          s(T)
        }, 0)
      }, [T, s, b.length]),
        e.useEffect(function () {
          return f.current = c,
            m.current = (new Date).getTime(),
            x(),
            h.current = setInterval(function () {
              x()
            }, 1e3),
            function () {
              clearInterval(h.current)
            }
        }, [c, x]), {
        handleTabClick: w,
        tabRef: d,
        timeTabData: b,
        canUseIndex: p.current
      }
    },
    Ae = {
      willBegin: "未开始",
      isDoing: "进行中",
      miss: "已结束"
    },
    Ie = Mach.env.isAndroid,
    Me = function (n) {
      var o, c = n.grabRounds,
        s = n.textColor,
        u = n.timeTextColor,
        l = n.isSqjNoema,
        d = n.activeRoundCode,
        m = n.currentTime,
        h = n.activeTabIndex,
        p = n.getData,
        g = n.setActiveRoundTimeStatus,
        b = n.logView,
        v = n.logClick,
        y = j(e.useState(0), 2),
        w = y[0],
        x = y[1],
        k = e.useRef(null),
        S = e.useRef([]),
        C = e.useCallback(function () {
          var e = f(T().m(function e(t) {
            var n, i, o, c, a, s, u, l;
            return T().w(function (e) {
              for (;;)
                switch (e.n) {
                  case 0:
                    if (n = S.current[t],
                      !Mach.env.isH5) {
                      e.n = 1;
                      break
                    }
                    i = n.getBoundingClientRect().left,
                      o = n.offsetWidth,
                    (i > .5 * w || i < .2 * o) && (k.current.scrollLeft = k.current.scrollLeft + i - .4 * o),
                      e.n = 4;
                    break;
                  case 1:
                    return e.n = 2,
                      r.measureInWindowAsync(n);
                  case 2:
                    if (c = e.v,
                      a = c.x,
                      s = c.width,
                      !(a > .5 * w || a < .2 * s)) {
                      e.n = 4;
                      break
                    }
                    return e.n = 3,
                      r.contentOffsetAsync(k.current);
                  case 3:
                    u = e.v,
                      l = u.scrollLeft,
                      k.current.scrollToOffset({
                        x: l + a - .4 * s,
                        y: 0
                      }, !1);
                  case 4:
                    return e.a(2)
                }
            }, e)
          }));
          return function (t) {
            return e.apply(this, arguments)
          }
        }(), [w]),
        A = je({
          grabRounds: c,
          getData: p,
          activeRoundCode: d,
          currentTime: m,
          isSqjNoema: l,
          autoScrollTab: C,
          logClick: v
        }),
        I = A.handleTabClick,
        M = A.timeTabData,
        P = A.canUseIndex;
      e.useEffect(function () {
        var e, t = M.findIndex(function (e) {
          return e.roundCode === d
        });
        g(null === (e = M[t]) || void 0 === e ? void 0 : e.status)
      }, [d, g, M]);
      var O = e.useCallback(function (e) {
          switch (M.length) {
            case 4:
              return e ? 136 : 123;
            case 3:
              return 218;
            case 2:
              return 327;
            default:
              return e ? 136 : 86
          }
        }, [M]),
        R = e.useMemo(function () {
          switch (M.length) {
            case 5:
            case 4:
              return 50;
            case 3:
            case 2:
            case 1:
              return 0;
            default:
              return 36
          }
        }, [M.length]),
        N = e.useCallback(function (e, t) {
          return e ? 1 : t ? .4 : .6
        }, []);
      e.useEffect(function () {
        x(Mach.env.screenWidth)
      }, []);
      var z = e.useCallback(function (e) {
        b("b_waimai_gouv1y6s_mv", {
          activity_time: e
        })
      }, [b]);
      return i.jsxs(t.View, {
        className: a("time-tab-wrapper", "11oojf"),
        children: [1 === M.length && i.jsxs(t.View, {
          className: a("list-1", "11oojf"),
          children: [(null === (o = M[0]) || void 0 === o ? void 0 : o.status) === G.Miss && i.jsxs(t.View, {
            children: [i.jsx(t.Text, {
              className: a(r.classNames("list-1-time", {
                "list-1-time-isAndroid": Ie
              }), "11oojf"),
              content: M[0].timeTxt,
              style: {
                color: s
              }
            }), i.jsx(t.Text, {
              className: a(r.classNames("list-1-time-desc", {
                "list-1-time-desc-isAndroid": Ie
              }), "11oojf"),
              style: {
                color: s
              },
              content: "场".concat(Ae[M[0].status])
            })]
          }), i.jsxs(t.View, {
            className: a("list-1-count-down-wrapper", "11oojf"),
            children: [i.jsx(t.Text, {
              className: a("list-1-count-down-txt", "11oojf"),
              style: {
                color: s
              },
              content: M[0] && M[0].status !== G.Miss ? M[0].statusText : ""
            }), M[0] && M[0].status !== G.Miss && M[0].countDown.h && i.jsxs(t.View, {
              className: a("list-1-count-down-content", "11oojf"),
              children: [i.jsx(t.Text, {
                className: a("list-1-hms", "11oojf"),
                style: {
                  background: s,
                  color: u
                },
                content: M[0].countDown.h
              }), i.jsx(t.Text, {
                className: a("list-1-colon", "11oojf"),
                style: {
                  color: s
                },
                content: ":"
              }), i.jsx(t.Text, {
                className: a("list-1-hms", "11oojf"),
                style: {
                  background: s,
                  color: u
                },
                content: M[0].countDown.m
              }), i.jsx(t.Text, {
                className: a("list-1-colon", "11oojf"),
                style: {
                  color: s
                },
                content: ":"
              }), i.jsx(t.Text, {
                className: a("list-1-hms", "11oojf"),
                style: {
                  background: s,
                  color: u
                },
                content: M[0].countDown.s
              })]
            })]
          })]
        }), M.length > 1 && i.jsx(t.View, {
          className: a("list-n", "11oojf"),
          children: i.jsx(t.Scroller, {
            className: a("list-n-scroll  ".concat(M.length > 5 ? "list-n-scroll-can" : ""), "11oojf"),
            ref: function (e) {
              return k.current = e
            },
            showScrollIndicator: !1,
            children: i.jsx("content", {
              className: a("list-n-scroll-content", "11oojf"),
              children: M.map(function (e, n) {
                return i.jsxs(t.View, {
                  ref: function (e) {
                    return S.current[n] = e
                  },
                  className: a(r.classNames("list-n-item", {
                    "list-n-item-selected": n === h
                  }), "11oojf"),
                  style: {
                    color: s,
                    marginRight: R,
                    width: "".concat(O(e.status === G.IsDoing || P === n && e.status === G.WillBegin), "rpx"),
                    opacity: N(n === h, e.status === G.Miss)
                  },
                  onClick: function () {
                    return I(n, !0)
                  },
                  onView: function () {
                    z([e.startTime, e.endTime])
                  },
                  children: [P === n ? i.jsxs(t.View, {
                    className: a("list-n-item-count-down", "11oojf"),
                    children: [i.jsx(t.Text, {
                      className: a("list-n-item-count-down-hms", "11oojf"),
                      content: e.countDown.h,
                      style: {
                        background: s,
                        color: u
                      }
                    }), i.jsx(t.Text, {
                      className: a("list-n-item-count-down-colon", "11oojf"),
                      style: {
                        color: s
                      },
                      content: ":"
                    }), i.jsx(t.Text, {
                      className: a("list-n-item-count-down-hms", "11oojf"),
                      style: {
                        background: s,
                        color: u
                      },
                      content: e.countDown.m
                    }), i.jsx(t.Text, {
                      className: a("list-n-item-count-down-colon", "11oojf"),
                      style: {
                        color: s
                      },
                      content: ":"
                    }), i.jsx(t.Text, {
                      className: a("list-n-item-count-down-hms", "11oojf"),
                      style: {
                        background: s,
                        color: u
                      },
                      content: e.countDown.s
                    })]
                  }) : i.jsx(t.Text, {
                    className: a("list-n-item-time", "11oojf"),
                    maxLines: 0,
                    style: {
                      color: s
                    },
                    content: e.timeTxt
                  }), i.jsx(t.Text, {
                    className: a(r.classNames("list-n-item-status-txt", {
                      "list-n-item-status-txt-doing": P === n
                    }), "11oojf"),
                    style: {
                      color: s
                    },
                    content: P === n && e.status === G.WillBegin ? "距开始" : Ae[e.status]
                  })]
                }, e.roundCode)
              })
            })
          })
        })]
      })
    };
  M(".WzSQm {\n  width: 6.54rem;\n  background: #FFFFFF;\n  border-radius: 0.16rem;\n  align-items: center;\n  justify-content: center;\n}\n.ON1jm {\n  width: 1.64rem;\n  height: 0.9rem;\n}\n.SBXcy {\n  height: 0.9rem;\n  flex-direction: column;\n  justify-content: center;\n}\n.c17v8 {\n  font-family: PingFangSC-Regular;\n  font-size: 0.24rem;\n  color: #858687;\n  text-align: left;\n  line-height: 0.35rem;\n  font-weight: 400;\n}\n");
  var Pe = function (e) {
    var n = e.isMultipleTimeTabs;
    return i.jsx(t.View, {
      className: a("empty-card-wrapper", "26x95f"),
      style: {
        height: n ? "140rpx" : "192rpx",
        marginTop: n ? "30rpx" : "10rpx"
      },
      children: i.jsxs(t.View, {
        children: [i.jsx(t.Image, {
          src: "https://p0.meituan.net/dptakeaway/4559401a9cd1e9d1dbb36153e7570d1923668.png",
          className: a("empty-img", "26x95f")
        }), i.jsxs(t.View, {
          className: a("text-wrapper", "26x95f"),
          children: [i.jsx(t.Text, {
            content: "本场没有您可领的红包",
            className: a("empty-text-tip", "26x95f")
          }), i.jsx(t.Text, {
            content: "看看其他优惠吧～",
            className: a("empty-text-tip", "26x95f")
          })]
        })]
      })
    })
  };
  M(".ma38v {\n  width: 7.02rem;\n  height: 2.88rem;\n  border-radius: 0.24rem;\n  overflow: hidden;\n  justify-content: center;\n  align-items: center;\n  flex-direction: column;\n}\n.hRb-I {\n  font-family: PingFangSC-Regular;\n  font-size: 0.28rem;\n  color: #575859;\n  text-align: center;\n  line-height: 0.35rem;\n  font-weight: 400;\n  margin-bottom: 0.12rem;\n}\n.-V8F1 {\n  background-image: linear-gradient(90deg, #FFE74D 0%, #FFDD19 100%);\n  border-radius: 0.32rem;\n  height: 0.64rem;\n  width: 1.68rem;\n  text-align: center;\n  line-height: 0.64rem;\n  font-family: PingFangSC-Medium;\n  font-size: 0.28rem;\n  color: #33312D;\n  font-weight: 500;\n}\n.KTqrZ {\n  width: 2.32rem;\n  height: 1.3rem;\n}\n");
  var Oe = u.default.gdUtil,
    Re = u.default.env.isPrerender,
    Ne = [{
      img: "https://p0.meituan.net/dptakeaway/7510f5c6e1ec01e043fbda9c991bbc8615618.png",
      txt: "登录后可见详细信息~",
      btnTxt: "去登录"
    }, {
      img: "https://p0.meituan.net/dptakeaway/4559401a9cd1e9d1dbb36153e7570d1923668.png",
      txt: "不在活动时间内，看看其他优惠吧～",
      btnTxt: ""
    }, {
      img: "https://p0.meituan.net/dptakeaway/7510f5c6e1ec01e043fbda9c991bbc8615618.png",
      txt: "当前抢券人数过多，请稍后再试~",
      btnTxt: "重新加载"
    }],
    Ge = function (n) {
      var r = n.isSqjNoema,
        o = n.backgroundColor,
        c = n.abnormalType,
        s = n.bgImg,
        u = e.useCallback(function () {
          Y(),
          c === F.NeedLogin && oe(),
          c === F.OtherAnomalies && Oe.reload()
        }, [c]),
        l = e.useMemo(function () {
          return i.jsxs(t.ImageBackground, {
            className: a("login-bg", "12kyc3"),
            resizeMode: "scaleToFill",
            src: "https://p0.meituan.net/dptakeaway/770dabca1ad4bf8bc35f793571302f1c50090.png",
            children: [i.jsx(t.Image, {
              src: Ne[c].img,
              className: a("net-error-img", "12kyc3")
            }), !Re && i.jsxs(i.Fragment, {
              children: [i.jsx(t.Text, {
                content: Ne[c].txt,
                className: a("login-text-tip", "12kyc3")
              }), Ne[c].btnTxt && i.jsx(t.Text, {
                content: Ne[c].btnTxt,
                className: a("login-btn", "12kyc3")
              })]
            })]
          })
        }, [c]);
      return r ? i.jsx(t.ImageBackground, {
        onClick: u,
        className: a("login-bg", "12kyc3"),
        resizeMode: "scaleToFill",
        src: s || "https://p0.meituan.net/dptakeaway/81190aeb4118199d24f5e3d79fb8693123186.png",
        children: l
      }) : i.jsx(t.View, {
        onClick: u,
        className: a("login-bg", "12kyc3"),
        style: {
          backgroundColor: o || "#FFCBDF"
        },
        children: l
      })
    };
  M(".-jSHi {\n  background-color: #000000b3;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n.bkR7s {\n  width: 6.4rem;\n  height: 8rem;\n  overflow: hidden;\n  border-radius: 0.2rem;\n  background: #ffffff;\n  margin-bottom: 0.45rem;\n  justify-content: center;\n}\n.ZHmJS {\n  flex-direction: column;\n  width: 5.5rem;\n}\n.DL-Wu {\n  width: 100%;\n  padding: 0.5rem 0 0.38rem;\n  text-align: center;\n  font-size: 0.36rem;\n  color: #ff8200;\n  font-family: couriernew, courier, monospace;\n}\n.Z9pYA {\n  width: 0.8rem;\n  height: 0.8rem;\n}\n.RWKHk {\n  text-align: left;\n  font-size: 0.26rem;\n  color: #292929;\n  line-height: 0.4rem;\n  font-family: couriernew, courier, monospace;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  max-lines: 0;\n}\n.JBZy1 {\n  max-height: 6rem;\n  overflow-y: auto;\n  flex-direction: column;\n}\n.JBZy1::-webkit-scrollbar {\n  display: none;\n}\n");
  var ze, De = 0,
    Ue = "",
    Ee = function (n) {
      var r = n.ruleText,
        c = n.onClose;
      return e.useEffect(function () {
        return Mach.env.isH5 && (De = window.pageYOffset || document.documentElement.scrollTop || window.scrollY || 0,
          document.body.style.top = "-".concat(De, "px"),
          document.body.style.position = "fixed",
          document.body.style.width = "100%",
          document.body.style.height = "100%",
          Ue = document.body.style.overflow,
          document.body.style.overflow = "hidden"),
          function () {
            Mach.env.isH5 && (document.body.style.display = "block",
              document.body.style.position = "static",
              document.body.style.top = "0",
              document.documentElement.scrollTop = De,
              document.body.scrollTop = De,
              document.body.style.overflow = Ue)
          }
      }, []),
        i.jsx(o.Fixed, {
          offset: [0, 0],
          style: {
            width: "100%",
            height: "100%"
          },
          zIndex: 9999,
          liftToRoot: !0,
          children: i.jsxs(t.View, {
            className: a("rule", "1r4gew"),
            style: {
              width: 750,
              height: Mach.env.isH5 ? "100%" : Mach.env.screenHeight * (750 / Mach.env.screenWidth)
            },
            children: [i.jsx(t.View, {
              className: a("rule-wrap", "1r4gew"),
              children: i.jsxs(t.View, {
                className: a("rule-area", "1r4gew"),
                children: [i.jsx(t.Text, {
                  className: a("rule-title", "1r4gew"),
                  content: "活动规则"
                }), i.jsx(t.Scroller, {
                  className: a("rule-text-scroller", "1r4gew"),
                  showScrollIndicator: !1,
                  children: i.jsx("content", {
                    children: r.split("\n").map(function (e) {
                      return i.jsx(t.Text, {
                        className: a("rule-text", "1r4gew"),
                        content: e,
                        maxLines: 1e5
                      })
                    })
                  })
                })]
              })
            }), i.jsx(t.Image, {
              className: a("rule-close-btn", "1r4gew"),
              src: "https://p0.meituan.net/ingee/a49527d0668ac9e6b2583eaab9d316f74165.png",
              onClick: c,
              resizeMode: "widthFix"
            })]
          })
        })
    },
    Fe = u.default.gdAjax,
    _e = u.default.gdEvent,
    Le = {
      test: "promotion.waimai.test.sankuai.com",
      stage: "promotion.waimai.st.sankuai.com",
      prod: "promotion.waimai.meituan.com"
    },
    Ve = {
      test: "promotion.waimai.test.sankuai.com",
      stage: "promotion.waimai.st.sankuai.com",
      prod: "promotion.waimai.meituan.com"
    },
    We = "72",
    Be = "77",
    qe = "84",
    He = "87",
    Xe = u.default.gdUtil.getCtypeValue,
    Je = u.default.gdBridge.getBridge().knb,
    Qe = function () {
      return g(function e(t) {
        h(this, e),
          this.url = "",
          this.path = "",
          this.setPath(t.path),
          this.setUrl()
      }, [{
        key: "setPath",
        value: function (e) {
          this.path = e
        }
      }, {
        key: "setUrl",
        value: function (e) {
          this.url = this.getUrl(this.path, e)
        }
      }, {
        key: "getUrl",
        value: function (e, t) {
          var n = u.default.env,
            r = n.isProd,
            i = n.isStage;
          return n.isLocal,
            t ? "//".concat(t).concat(e) : (e = n.isDp() ? r ? "//".concat(Ve.prod).concat(e) : i ? "//".concat(Ve.stage).concat(e) : "//".concat(Ve.test).concat(e) : r ? "//".concat(Le.prod).concat(e) : i ? "//".concat(Le.stage).concat(e) : "//".concat(Le.test).concat(e),
            Mach.env.isH5 || n.isLocal || (e = "https:" + e),
              e)
        }
      }, {
        key: "buildQueryParams",
        value: function (e) {
          return Object.keys(e).map(function (t) {
            return encodeURIComponent(t) + "=" + encodeURIComponent(e[t])
          }).join("&")
        }
      }, {
        key: "getToken",
        value: (e = f(T().m(function e() {
            var t, n, r, i, o, c, a, s;
            return T().w(function (e) {
              for (;;)
                switch (e.n) {
                  case 0:
                    if (t = u.default.env,
                      n = u.default.gdUtil.qs() || {},
                      r = n && n.wm_logintoken || "",
                      i = u.default.gdUtil.getGlobalData(),
                      o = t.isTest && (i.group === We || i.group === qe) || (t.isStage || t.isProd) && (i.group === Be || i.group === He)) {
                      e.n = 1;
                      break
                    }
                    return e.a(2, "");
                  case 1:
                    if (!t.isAndroid && !t.isHarmonyMSI || !t.isWmApp) {
                      e.n = 5;
                      break
                    }
                    return e.p = 2,
                      e.n = 3,
                      this.getKnbUserInfo();
                  case 3:
                    c = e.v,
                    (a = c.wm_logintoken) && (r = a),
                      e.n = 5;
                    break;
                  case 4:
                    e.p = 4,
                      s = e.v,
                      console.log(s);
                  case 5:
                    if (console.log("[wm-playcenter-logic][getToken] isWelfareCenterGroup: ".concat(o, ", wm_logintoken: ").concat(r)),
                      u.default.gdMonitor.addLogan("[wm-playcenter-logic][getToken] isWelfareCenterGroup: ".concat(o, ", wm_logintoken: ").concat(r)),
                    !o || !r) {
                      e.n = 6;
                      break
                    }
                    return e.a(2, r);
                  case 6:
                    return e.a(2, "")
                }
            }, e, this, [
              [2, 4]
            ])
          })),
            function () {
              return e.apply(this, arguments)
            }
        )
      }, {
        key: "getKnbUserInfo",
        value: function () {
          return new Promise(function (e, t) {
            try {
              Je.ready(function () {
                Je.use("getUserInfo", {
                  success: function (t) {
                    var n = {},
                      r = null == t ? void 0 : t.userId,
                      i = null == t ? void 0 : t.uuid,
                      o = null == t ? void 0 : t.token;
                    n.token = o,
                      n.wm_logintoken = o,
                      n.userId = r,
                      n.uuid = i,
                      e(n)
                  },
                  fail: function (e) {
                    t(e)
                  }
                })
              })
            } catch (e) {
              t(e)
            }
          })
        }
      }, {
        key: "fetch",
        value: function (e) {
          return u.default.gdAjax.fetch(e)
        }
      }, {
        key: "extractRequestParams",
        value: function (e, t) {
          var n = e || {};
          return {
            GET: n.data,
            POST: n.params
          } [t] || {}
        }
      }, {
        key: "fetchAuth",
        value: function (e, t) {
          var n = this;
          return new Promise(function () {
            var r = f(T().m(function r(i, o) {
              var c, a, s, l, d, m, h, p, g, b, v, y, w, x;
              return T().w(function (r) {
                for (;;)
                  switch (r.n) {
                    case 0:
                      return c = u.default.env,
                        a = e.data,
                        s = void 0 === a ? {} : a,
                        l = e.needSign,
                        d = void 0 !== l && l,
                        m = e.needFingerprint,
                        h = void 0 !== m && m,
                        p = e.fingerprintKey,
                        g = void 0 === p ? "mtFingerprint" : p,
                        b = e.needCouponRefParams,
                        v = void 0 !== b && b,
                        y = "",
                        r.p = 1,
                        r.n = 2,
                        n.getToken();
                    case 2:
                      y = r.v,
                        r.n = 4;
                      break;
                    case 3:
                      r.p = 3,
                        x = r.v,
                        console.log(x);
                    case 4:
                      w = y ? "&token=".concat(y) : "",
                        c.isWxMp().then(function () {
                          var r = f(T().m(function r(a) {
                            var l, f;
                            return T().w(function (r) {
                              for (;;)
                                switch (r.n) {
                                  case 0:
                                    if (l = !1,
                                      f = "",
                                      !s.ctype) {
                                      r.n = 1;
                                      break
                                    }
                                    f = s.ctype,
                                      r.n = 3;
                                    break;
                                  case 1:
                                    return r.n = 2,
                                      Xe();
                                  case 2:
                                    f = r.v;
                                  case 3:
                                    n.fetch(Object.assign(Object.assign({}, e), {
                                      method: t,
                                      url: "".concat(n.url, "?isMini=").concat(a ? 1 : 0, "&ctype=").concat(f, "&isInDpEnv=").concat(Number(c.isDp())).concat(w),
                                      data: s,
                                      params: n.extractRequestParams(e, t),
                                      withCredentials: !0,
                                      mode: "cors",
                                      needFingerprint: h,
                                      fingerprintKey: g,
                                      needSign: d,
                                      needCouponRefParams: v
                                    })).then(function (t) {
                                      var r = t || {},
                                        c = r.code,
                                        a = r.subcode,
                                        s = r.msg;
                                      return 0 === c ? i(t) : 1 === c ? (n.checkNotLoginCase(e, t, a),
                                        i(t)) : (500 !== c && 501 !== c || n.checkHandleCase(e, "handleServerError", s),
                                        o({
                                          error: !1,
                                          response: t
                                        }))
                                    }, function (e) {
                                      return l = !0,
                                        Promise.reject(e)
                                    }).catch(function (e) {
                                      u.default.gdMonitor.addError({
                                        error: {
                                          name: "[wm-playcenter-logic] + api fetchAuth catch error",
                                          msg: "[wm-playcenter-logic] + api fetchAuth catch error"
                                        },
                                        opts: {
                                          category: window.Owl.errorModel.CATEGORY.AJAX,
                                          level: l ? window.Owl.errorModel.LEVEL.WARN : window.Owl.errorModel.LEVEL.ERROR,
                                          tags: {
                                            info: "",
                                            componentInfos: "",
                                            errorMessage: e && e.message,
                                            errorStack: e && e.stack
                                          }
                                        }
                                      }),
                                        o({
                                          error: e
                                        })
                                    });
                                  case 4:
                                    return r.a(2)
                                }
                            }, r)
                          }));
                          return function (e) {
                            return r.apply(this, arguments)
                          }
                        }());
                    case 5:
                      return r.a(2)
                  }
              }, r, null, [
                [1, 3]
              ])
            }));
            return function (e, t) {
              return r.apply(this, arguments)
            }
          }())
        }
      }, {
        key: "fetchPostAuth",
        value: function (e) {
          return this.fetchAuth(e, "POST")
        }
      }, {
        key: "fetchGetAuth",
        value: function (e) {
          return this.fetchAuth(e, "GET")
        }
      }, {
        key: "login",
        value: function () {
          return u.default.gdFeature.login()
        }
      }, {
        key: "checkNotLoginCase",
        value: function (e, t, n) {
          4 !== n && 7 !== n || ("function" == typeof e.handleLogin ? e.handleLogin(t) : this.login().then(function () {
            "function" == typeof e.loginSuccess && e.loginSuccess(t)
          }, function (n) {
            console.log("登录失败，请重新登录"),
            "function" == typeof e.loginFail && e.loginFail(t),
              u.default.gdMonitor.addError({
                error: {
                  name: "[wm-playcenter-logic] + api checkNotLoginCase error",
                  msg: "[wm-playcenter-logic] + api checkNotLoginCase error"
                },
                opts: {
                  category: window.Owl.errorModel.CATEGORY.SCRIPT,
                  tags: {
                    info: "",
                    componentInfos: "",
                    errorMessage: n && n.message,
                    errorStack: n && n.stack
                  }
                }
              })
          }))
        }
      }, {
        key: "checkHandleCase",
        value: function (e, t, n) {
          "function" == typeof e[t] ? e[t](n) : console.log(t)
        }
      }]);
      var e
    }(),
    Ze = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/doaction"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchDoAction",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }])
    }(Qe),
    Ke = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/orderLottery/taskStatus"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchTaskStatus",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    Ye = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/entry"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEntry",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    $e = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/treasurebox/signup/record"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSignRecord",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    et = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/treasurebox/signup/reward/get"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSignPrize",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    tt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/getreceivinginfo"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchGetReceivingInfo",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    nt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/myawards"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchMyAwards",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    rt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/myactions"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchMyActions",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    it = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/reminder"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchReminder",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    ot = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/reminder"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchReminderInfo",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    ct = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/playeraccount"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchPlayerAccount",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    at = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/receivinginfo"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchReceivingInfo",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    st = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/transferrecord"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchTransferRecord",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    ut = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/login"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchCheckLogin",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    lt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/supportInfo"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSupportInfo",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    dt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/leaderboard"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchLeaderBoard",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    ft = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/totalrewards"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchTotalRewards",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    mt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/lottery/limitcouponcomponent/getTime"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchServerTime",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    ht = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/preday/doaction/info"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchPredayDoActionInfo",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    pt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/coupon/multiable"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchCheckCouponMultiable",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    gt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/coupon/multiple"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchDoMultipleRedBag",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    bt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/mycoupons/shenquan"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchMyGodTickets",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    vt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/shenquan/amount/max"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchMaxExpandAmount",
          value: function (e) {
            return (null == e ? void 0 : e.domain) && this.setUrl(e.domain),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    yt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/biztime/rw"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchFirstQuitTime",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    wt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/coupon/shenquan/used"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSaveAmount",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    xt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/subsinfo"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSubsInfo",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    kt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/sn/status"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSmartSubscribeStatus",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Tt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/sn/subscribe"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchChangeSubscribeStatus",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    St = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/achievement/shareimg/url"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchUploadImage",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    Ct = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/eurocup/guess/pop/confirm"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEurocupPopConfirm",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    jt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/eurocup/guess"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEurocupGuess",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    At = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/eurocup/guess/entry"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEurocupEntry",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    It = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/activity/getvalidactivity"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchValidActivity",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    Mt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/eurocup/guess/record"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEuroCupGuessRecord",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Pt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/eurocup/point"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchEuroCupPoint",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Ot = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v2/biz/count/add"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "addCount",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    Rt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/shenquan/amount/max"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchMaxCouponValue",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Nt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/clock-in/status"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchClockinStatus",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Gt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/clock-in/reward"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchClockinReward",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    zt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/clock-in/track"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchClockinTrack",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Dt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/clock-in"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchClockin",
          value: function (e) {
            return this.fetchPostAuth(Object.assign(Object.assign({}, e), {
              needCouponRefParams: !0
            }))
          }
        }])
    }(Qe),
    Ut = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/user/toast/count/get"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "fetchSignModalTimes",
          value: function (e) {
            return this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Et = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: "/playcenter/common/v1/user/toast/count/add"
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "addSignModalTimes",
          value: function (e) {
            return this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    Ft = "/playcenter/signlottery/info",
    _t = "/playcenter/signlottery/signIn",
    Lt = "/playcenter/signlottery/lottery",
    Vt = "/playcenter/signlottery/myAward",
    Wt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: Ft
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "updateUrl",
          value: function (e) {
            this.setPath(e),
              this.setUrl()
          }
        }, {
          key: "getInfo",
          value: function (e) {
            return this.updateUrl(Ft),
              this.fetchGetAuth(e)
          }
        }, {
          key: "doSignIn",
          value: function (e) {
            return this.updateUrl(_t),
              this.fetchPostAuth(e)
          }
        }, {
          key: "doLottery",
          value: function (e) {
            return this.updateUrl(Lt),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }, {
          key: "getMyAward",
          value: function (e) {
            return this.updateUrl(Vt),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    Bt = "/playcenter/generalcoupon/info",
    qt = "/playcenter/generalcoupon/fetch",
    Ht = "/playcenter/generalcoupon/student/info",
    Xt = "/playcenter/generalcoupon/student/fetch",
    Jt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: Bt
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "updateUrl",
          value: function (e) {
            this.setPath(e),
              this.setUrl()
          }
        }, {
          key: "getInfo",
          value: function (e) {
            return this.updateUrl(Bt),
              this.fetchGetAuth(e)
          }
        }, {
          key: "doFetch",
          value: function (e) {
            return this.updateUrl(qt),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }, {
          key: "getStudentInfo",
          value: function (e) {
            return this.updateUrl(Ht),
              this.fetchGetAuth(e)
          }
        }, {
          key: "doStudentFetch",
          value: function (e) {
            return this.updateUrl(Xt),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }])
    }(Qe),
    Qt = "/playcenter/generalcoupon/magicalmember/info",
    Zt = "/playcenter/generalcoupon/magicalmember/fetch",
    Kt = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: Qt
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "updateUrl",
          value: function (e, t) {
            this.setPath(e),
              this.setUrl(t)
          }
        }, {
          key: "getInfo",
          value: function (e) {
            var t = (null == e ? void 0 : e.domain) || "";
            return this.updateUrl(Qt, t),
              this.fetchGetAuth(e)
          }
        }, {
          key: "doFetch",
          value: function (e) {
            var t = (null == e ? void 0 : e.domain) || "";
            return this.updateUrl(Zt, t),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }])
    }(Qe),
    Yt = "/placement/bannerforwx",
    $t = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: Yt
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "updateUrl",
          value: function (e) {
            this.setPath(e),
              this.setUrl()
          }
        }, {
          key: "getInfo",
          value: function (e) {
            return this.updateUrl(Yt),
              this.fetchGetAuth(e)
          }
        }])
    }(Qe),
    en = "/playcenter/generalcoupon/wxcardbag/info",
    tn = "/playcenter/generalcoupon/wxcardbag/fetch",
    nn = "/playcenter/generalcoupon/wxcardbag/notifyIssueResult",
    rn = function (e) {
      function t(e) {
        return h(this, t),
          m(this, t, [Object.assign({}, {
            path: en
          }, e || {})])
      }
      return y(t, e),
        g(t, [{
          key: "updateUrl",
          value: function (e) {
            this.setPath(e),
              this.setUrl()
          }
        }, {
          key: "fetchGetCouponInfo",
          value: function (e) {
            return this.updateUrl(en),
              this.fetchGetAuth(e)
          }
        }, {
          key: "fetchPostCoupon",
          value: function (e) {
            return this.updateUrl(tn),
              this.fetchPostAuth(Object.assign(Object.assign({}, e), {
                needCouponRefParams: !0
              }))
          }
        }, {
          key: "fetchPostWxCardAddRes",
          value: function (e) {
            return this.updateUrl(nn),
              this.fetchPostAuth(e)
          }
        }])
    }(Qe),
    on = function () {
      function e() {
        h(this, e),
          this.reminder = new it,
          this.reminderInfo = new ot,
          this.doAction = new Ze,
          this.taskStatus = new Ke,
          this.entry = new Ye,
          this.signModal = new Ut,
          this.addSignModal = new Et,
          this.signRecord = new $e,
          this.signPrize = new et,
          this.getReceivingInfo = new tt,
          this.myAwards = new nt,
          this.myActions = new rt,
          this.playerAccount = new ct,
          this.receivingInfo = new at,
          this.transferRecord = new st,
          this.checkLogin = new ut,
          this.supportInfo = new lt,
          this.leaderboard = new dt,
          this.totalRewards = new ft,
          this.serverTime = new mt,
          this.predayDoActionInfo = new ht,
          this.checkCouponMultiable = new pt,
          this.doMultipleRedBag = new gt,
          this.myGodTickets = new bt,
          this.maxExpandAmount = new vt,
          this.firstQuitTime = new yt,
          this.saveAmount = new wt,
          this.subsInfo = new xt,
          this.smartSubscribeStatus = new kt,
          this.changeSubscribeStatus = new Tt,
          this.uploadImage = new St,
          this.eurocupPopConfirm = new Ct,
          this.eurocupGuess = new jt,
          this.eurocupEntry = new At,
          this.validActivity = new It,
          this.eurocupPoint = new Pt,
          this.eurocupGuessRecord = new Mt,
          this.pvCount = new Ot,
          this.maxCouponValue = new Rt,
          this.clockinStatus = new Nt,
          this.clockinReward = new Gt,
          this.clockin = new Dt,
          this.clockinTrack = new zt,
          this.signLottery = new Wt,
          this.commonCoupon = new Jt,
          this.welfareCommonCoupon = new Kt,
          this.carouselVenue = new $t,
          this.wxCardCoupon = new rn
      }
      return g(e, [{
        key: "fetchDoAction",
        value: function (e) {
          return this.doAction.fetchDoAction(e)
        }
      }, {
        key: "fetchTaskStatus",
        value: function (e) {
          return this.taskStatus.fetchTaskStatus(e)
        }
      }, {
        key: "fetchEntry",
        value: function (e) {
          return this.entry.fetchEntry(e)
        }
      }, {
        key: "addSignModalTimes",
        value: function (e) {
          return this.addSignModal.addSignModalTimes(e)
        }
      }, {
        key: "fetchSignModalTimes",
        value: function (e) {
          return this.signModal.fetchSignModalTimes(e)
        }
      }, {
        key: "fetchSignRecord",
        value: function (e) {
          return this.signRecord.fetchSignRecord(e)
        }
      }, {
        key: "fetchSignPrize",
        value: function (e) {
          return this.signPrize.fetchSignPrize(e)
        }
      }, {
        key: "fetchGetReceivingInfo",
        value: function (e) {
          return this.getReceivingInfo.fetchGetReceivingInfo(e)
        }
      }, {
        key: "fetchMyAwards",
        value: function (e) {
          return this.myAwards.fetchMyAwards(e)
        }
      }, {
        key: "fetchMyActions",
        value: function (e) {
          return this.myActions.fetchMyActions(e)
        }
      }, {
        key: "fetchReminder",
        value: function (e) {
          return this.reminder.fetchReminder(e)
        }
      }, {
        key: "fetchReminderInfo",
        value: function (e) {
          return this.reminderInfo.fetchReminderInfo(e)
        }
      }, {
        key: "fetchPlayerAccount",
        value: function (e) {
          return this.playerAccount.fetchPlayerAccount(e)
        }
      }, {
        key: "fetchReceivingInfo",
        value: function (e) {
          return this.receivingInfo.fetchReceivingInfo(e)
        }
      }, {
        key: "fetchTransferRecord",
        value: function (e) {
          return this.transferRecord.fetchTransferRecord(e)
        }
      }, {
        key: "fetchCheckLogin",
        value: function (e) {
          return this.checkLogin.fetchCheckLogin(e)
        }
      }, {
        key: "fetchSupportInfo",
        value: function (e) {
          return this.supportInfo.fetchSupportInfo(e)
        }
      }, {
        key: "fetchLeaderBoard",
        value: function (e) {
          return this.leaderboard.fetchLeaderBoard(e)
        }
      }, {
        key: "fetchTotalRewards",
        value: function (e) {
          return this.totalRewards.fetchTotalRewards(e)
        }
      }, {
        key: "fetchServerTime",
        value: function (e) {
          return this.serverTime.fetchServerTime(e)
        }
      }, {
        key: "fetchPredayDoActionInfo",
        value: function (e) {
          return this.predayDoActionInfo.fetchPredayDoActionInfo(e)
        }
      }, {
        key: "fetchCheckCouponMultiable",
        value: function (e) {
          return this.checkCouponMultiable.fetchCheckCouponMultiable(e)
        }
      }, {
        key: "fetchDoMultipleRedBag",
        value: function (e) {
          return this.doMultipleRedBag.fetchDoMultipleRedBag(e)
        }
      }, {
        key: "fetchMyGodTickets",
        value: function (e) {
          return this.myGodTickets.fetchMyGodTickets(e)
        }
      }, {
        key: "fetchMaxExpandAmount",
        value: function (e) {
          return this.maxExpandAmount.fetchMaxExpandAmount(e)
        }
      }, {
        key: "fetchFirstQuitTime",
        value: function (e) {
          return this.firstQuitTime.fetchFirstQuitTime(e)
        }
      }, {
        key: "fetchSaveAmount",
        value: function (e) {
          return this.saveAmount.fetchSaveAmount(e)
        }
      }, {
        key: "fetchSubsInfo",
        value: function (e) {
          return this.subsInfo.fetchSubsInfo(e)
        }
      }, {
        key: "fetchSmartSubscribeStatus",
        value: function (e) {
          return this.smartSubscribeStatus.fetchSmartSubscribeStatus(e)
        }
      }, {
        key: "fetchChangeSubscribeStatus",
        value: function (e) {
          return this.changeSubscribeStatus.fetchChangeSubscribeStatus(e)
        }
      }, {
        key: "fetchUploadImage",
        value: function (e) {
          return this.uploadImage.fetchUploadImage(e)
        }
      }, {
        key: "fetchEurocupPopConfirm",
        value: function (e) {
          return this.eurocupPopConfirm.fetchEurocupPopConfirm(e)
        }
      }, {
        key: "fetchEurocupGuess",
        value: function (e) {
          return this.eurocupGuess.fetchEurocupGuess(e)
        }
      }, {
        key: "fetchEurocupEntry",
        value: function (e) {
          return this.eurocupEntry.fetchEurocupEntry(e)
        }
      }, {
        key: "fetchValidActivity",
        value: function (e) {
          return this.validActivity.fetchValidActivity(e)
        }
      }, {
        key: "fetchEuroCupGuessRecord",
        value: function (e) {
          return this.eurocupGuessRecord.fetchEuroCupGuessRecord(e)
        }
      }, {
        key: "fetchEuroCupPoint",
        value: function (e) {
          return this.eurocupPoint.fetchEuroCupPoint(e)
        }
      }, {
        key: "addPvCount",
        value: function (e) {
          return this.pvCount.addCount(e)
        }
      }, {
        key: "fetchMaxCouponValue",
        value: function (e) {
          return this.maxCouponValue.fetchMaxCouponValue(e)
        }
      }, {
        key: "fetchClockinStatus",
        value: function (e) {
          return this.clockinStatus.fetchClockinStatus(e)
        }
      }, {
        key: "fetchClockinReward",
        value: function (e) {
          return this.clockinReward.fetchClockinReward(e)
        }
      }, {
        key: "fetchClockin",
        value: function (e) {
          return this.clockin.fetchClockin(e)
        }
      }, {
        key: "fetchClockinTrack",
        value: function (e) {
          return this.clockinTrack.fetchClockinTrack(e)
        }
      }], [{
        key: "getInstance",
        value: function () {
          return e.instance || (e.instance = new e),
            e.instance
        }
      }])
    }();

  function cn(e) {
    var t = e.path,
      n = e.method,
      r = void 0 === n ? "GET" : n;
    if (!t)
      return Promise.reject("fetch path is required");
    var i = new Qe({
      path: t
    });
    return r === ze.GET ? i.fetchGetAuth(e) : r === ze.POST ? i.fetchPostAuth(e) : i.fetchAuth(e, r)
  }! function (e) {
    e.GET = "GET",
      e.POST = "POST"
  }(ze || (ze = {})),
    on.getInstance();
  var an = u.default.gdBridge,
    sn = u.default.env;

  function un(e) {
    var t = sn.isWxMpWm ? "/outer_packages/mactivity/pages/middlePage/index" : sn.isWxMpMtWm ? "/waimai/outer_packages/mactivity/pages/middlePage/index" : "";
    if (t) {
      var n = an.getBridge().wx;
      try {
        var r = encodeURIComponent(function (e, t) {
            var n = (e || []).map(function (e) {
              return {
                templateId: e || "",
                scene: t || ""
              }
            });
            try {
              return JSON.stringify(n)
            } catch (e) {
              return console.error("### _getTemplateScene failed ###", e),
                ""
            }
          }(e.templateIds, e.scene)),
          i = encodeURIComponent(e.alertTitle || ""),
          o = "".concat(t, "?channelId=gundam&templateScene=").concat(r, "&alertTitle=").concat(i);
        console.log("### jump2MiddlePage ###", "url", o),
          n.miniProgram.navigateTo({
            url: o,
            fail: function (e) {
              console.error("### jump2MiddlePage ###", "navigateTo fail", e)
            }
          })
      } catch (e) {
        console.error("### jump2MiddlePage ###", e)
      }
    } else
      Z("请前往外卖小程序参加活动~")
  }
  var ln = u.default.env,
    dn = u.default.gdBridge,
    fn = u.default.gdUtil,
    mn = u.default.gdMonitor,
    hn = u.default.gdAjax,
    pn = dn.getBridge().knb,
    gn = "订阅失败！系统未获取通知权限",
    bn = "订阅任务已过期";

  function vn() {
    var e = "";
    return e = ln.isTest ? "//promotion.waimai.test.sankuai.com" : ln.isStage ? "//promotion.waimai.st.sankuai.com" : (ln.isProd,
      "//promotion.waimai.meituan.com"),
    Mach.env.isH5 || ln.isLocal || (e = "https:".concat(e)),
      e
  }

  function yn() {
    return (yn = f(T().m(function e(t) {
      var n, r, i, o, c;
      return T().w(function (e) {
        for (;;)
          switch (e.n) {
            case 0:
              return n = vn(),
                r = "".concat(n, "/subscription/wxMiniTask"),
                i = "",
                e.p = 1,
                e.n = 2,
                u.default.gdUtil.getCtypeValue();
            case 2:
              i = e.v,
                e.n = 4;
              break;
            case 3:
              e.p = 3,
                c = e.v,
                console.log(c);
            case 4:
              return o = {
                activityId: t || "",
                isMini: fn.qs().isMini || "",
                ctype: i
              },
                e.a(2, hn.fetch({
                  url: r,
                  method: "GET",
                  params: o
                }))
          }
      }, e, null, [
        [1, 3]
      ])
    }))).apply(this, arguments)
  }
  var wn = function (e) {
      return new Promise(function (t, n) {
        pn.use("requestPermission", {
          type: "notification",
          readonly: !!e,
          forceJump: !0,
          success: t,
          fail: n
        })
      })
    },
    xn = function (e) {
      return new Promise(function () {
        var t = f(T().m(function t(n, r) {
          var i;
          return T().w(function (t) {
            for (;;)
              switch (t.n) {
                case 0:
                  if (t.p = 0,
                    !dn.use) {
                    t.n = 2;
                    break
                  }
                  return t.n = 1,
                    dn.use("requestPermission", {
                      type: "notification",
                      readonly: !!e,
                      forceJump: !0
                    });
                case 1:
                  n(!0),
                    t.n = 4;
                  break;
                case 2:
                  return t.n = 3,
                    wn(e);
                case 3:
                  n(!0);
                case 4:
                  t.n = 6;
                  break;
                case 5:
                  t.p = 5,
                    i = t.v,
                    console.log("### gdBridge.use(requestPermission) or KNB.requestPermission error", i),
                    r(!1);
                case 6:
                  return t.a(2)
              }
          }, t, null, [
            [0, 5]
          ])
        }));
        return function (e, n) {
          return t.apply(this, arguments)
        }
      }())
    },
    kn = function (t) {
      var n, i, o, c, a = fn.getGlobalData(),
        s = j(e.useState(!1), 2),
        u = s[0],
        l = s[1],
        d = j(e.useState(!0), 2),
        m = d[0],
        h = d[1],
        p = j(e.useState(!1), 2),
        g = p[0],
        b = p[1],
        v = j(e.useState(!1), 2),
        y = v[0],
        w = v[1],
        x = j(e.useState([]), 2),
        k = x[0],
        S = x[1],
        C = j(e.useState(!1), 2),
        A = C[0],
        I = C[1],
        M = e.useRef(!1),
        P = e.useMemo(function () {
          var e, n;
          return !!t && (!ln.isWxHm && !ln.isHarmonyMSI && (null !== (e = a.env) && void 0 !== e && e.isWmApp || null !== (n = a.env) && void 0 !== n && n.isMtApp ? Boolean(t.subscribeAPPConfig.subTaskId && t.subscribeAPPConfig.subAPPToggle) : !(!ln.isWxMpWm && !ln.isWxMpMtWm) && Boolean(t.subscribeMPConfig.subMPTaskId && t.subscribeMPConfig.subMPToggle)))
        }, [null === (n = a.env) || void 0 === n ? void 0 : n.isMtApp, null === (i = a.env) || void 0 === i ? void 0 : i.isWmApp, t]),
        N = e.useCallback(function (e) {
          if (!u) {
            l(!0);
            var t = function (e) {
                var t = 0,
                  n = 0;
                return ln.isIOS && (n = R.IOS),
                ln.isAndroid && (n = R.Android),
                null != e && e.isWmApp && (t = O.wm),
                null != e && e.isMtApp && (t = O.mt), {
                  appType: t,
                  deviceType: n
                }
              }(a.env),
              n = t.appType,
              r = t.deviceType;
            cn({
              path: "/playcenter/common/v1/reminder",
              data: {
                subsId: e.subscribeAPPConfig.subTaskId,
                toStatus: !m,
                appType: n,
                deviceType: r
              },
              contentType: "json",
              method: "POST",
              handleLogin: function () {
                oe()
              }
            }).then(function (e) {
              l(!1);
              var t = e.code,
                n = e.subcode;
              0 === t ? (Z(m ? "取消订阅提醒，可能错过抢券最佳时机哦!" : "预约成功，将在神券开抢前提醒您~"),
                h(!m)) : (1 !== t || 4 !== n && 7 !== n) && Z("网络异常，请稍后重试~")
            }).catch(function (e) {
              l(!1),
                Z("网络异常，请稍后重试~")
            })
          }
        }, [u, a.env, m]),
        G = e.useCallback(function (e) {
          I(e)
        }, []),
        z = e.useCallback(f(T().m(function e() {
          return T().w(function (e) {
            for (;;)
              switch (e.n) {
                case 0:
                  if (e.p = 0,
                    !dn.use) {
                    e.n = 2;
                    break
                  }
                  return e.n = 1,
                    dn.use("openAppSetting");
                case 1:
                  e.n = 3;
                  break;
                case 2:
                  pn.use("openAppSetting", {
                    fail: function (e) {
                      console.log("openAppSetting fail:", e)
                    }
                  });
                case 3:
                  e.n = 5;
                  break;
                case 4:
                  e.p = 4,
                    Z(gn);
                case 5:
                  return e.a(2)
              }
          }, e, null, [
            [0, 4]
          ])
        })), []),
        D = function () {
          var e = f(T().m(function e() {
            return T().w(function (e) {
              for (;;)
                switch (e.n) {
                  case 0:
                    if (e.p = 0,
                      !dn.use) {
                      e.n = 2;
                      break
                    }
                    return e.n = 1,
                      dn.use("openPage", {
                        url: "app-settings:"
                      });
                  case 1:
                    e.n = 3;
                    break;
                  case 2:
                    pn.use("openPage", {
                      url: "app-settings:",
                      fail: function (e) {
                        console.log("openPage-app-settings fail:", e),
                          z()
                      }
                    });
                  case 3:
                    e.n = 5;
                    break;
                  case 4:
                    e.p = 4,
                      z();
                  case 5:
                    return e.a(2)
                }
            }, e, null, [
              [0, 4]
            ])
          }));
          return function () {
            return e.apply(this, arguments)
          }
        }(),
        U = e.useRef(null),
        E = function () {
          var e = f(T().m(function e(t) {
            var n, r;
            return T().w(function (e) {
              for (;;)
                switch (e.n) {
                  case 0:
                    if (Y(),
                      n = function () {
                        var e;
                        dn.use ? dn.use("unsubscribe", {
                          action: "foreground",
                          subId: null === (e = U.current) || void 0 === e ? void 0 : e.subId,
                          success: function () {},
                          fail: function () {}
                        }) : pn.use("unsubscribe", {
                          action: "foreground",
                          success: function () {},
                          fail: function () {}
                        })
                      },
                      !ln.isIOS) {
                      e.n = 6;
                      break
                    }
                    if (r = function () {
                      xn(!0).then(function () {
                        console.log("IOS 触发成功"),
                          N(t),
                          n()
                      }, function () {
                        !M.current && Z(gn),
                          M.current = !0,
                          console.log("IOS push设置失败"),
                          mn.addLogan("[push设置失败]"),
                          n()
                      })
                    },
                      e.p = 1,
                      !dn.use) {
                      e.n = 3;
                      break
                    }
                    return e.n = 2,
                      dn.use("subscribe", {
                        action: "foreground",
                        handle: r
                      });
                  case 2:
                    U.current = e.v,
                      e.n = 4;
                    break;
                  case 3:
                    pn.use("subscribe", {
                      action: "foreground",
                      handle: r
                    });
                  case 4:
                    e.n = 6;
                    break;
                  case 5:
                    e.p = 5,
                      n();
                  case 6:
                    D(),
                      G(!1);
                  case 7:
                    return e.a(2)
                }
            }, e, null, [
              [1, 5]
            ])
          }));
          return function (t) {
            return e.apply(this, arguments)
          }
        }(),
        F = e.useCallback(function (e) {
          var t, n;
          return (null !== (t = a.env) && void 0 !== t && t.isWmApp || null !== (n = a.env) && void 0 !== n && n.isMtApp) && e ? cn({
            path: "/playcenter/common/v1/reminder",
            data: {
              subsId: e.subscribeAPPConfig.subTaskId
            },
            handleLogin: function () {
              b(!1)
            }
          }).then(function (e) {
            var t, n = e.code,
              r = e.data;
            return 0 === n ? (b(!0),
              t = r.status) : t = !1,
              h(t),
              t
          }).catch(function (e) {
            return console.log("error:", e),
              h(!1),
              !1
          }) : Promise.resolve(!1)
        }, [null === (o = a.env) || void 0 === o ? void 0 : o.isMtApp, null === (c = a.env) || void 0 === c ? void 0 : c.isWmApp]),
        _ = function (e) {
          (function (e) {
            return yn.apply(this, arguments)
          })(e.subscribeMPConfig.subMPTaskId).then(function (t) {
            var n = t.code,
              r = t.data;
            if (0 === n) {
              var i = r || [];
              if (k.length > 0 && 0 === i.length)
                return void Z(bn);
              i.length > 0 ? (S(i),
                function (e, t) {
                  un({
                    templateIds: e || [],
                    alertTitle: t.subscribeMPConfig.successToast || "",
                    scene: String(t.subscribeMPConfig.subMPTaskId || "")
                  })
                }(i, e)) : Z(bn)
            } else
              Z("网络异常，请稍后重试~")
          }).catch(function (e) {
            console.error("### fetchTaskStatus failed ###", e),
              Z("网络异常，请稍后重试~")
          })
        };
      return r.useAsyncEffect(f(T().m(function e() {
        var n, r;
        return T().w(function (e) {
          for (;;)
            switch (e.n) {
              case 0:
                if (h(!1),
                  l(!1),
                  w(!1),
                  S([]),
                  P) {
                  e.n = 1;
                  break
                }
                return e.a(2);
              case 1:
                if (!(null !== (n = a.env) && void 0 !== n && n.isWmApp || null !== (r = a.env) && void 0 !== r && r.isMtApp)) {
                  e.n = 2;
                  break
                }
                return e.n = 2,
                  F(t);
              case 2:
                return e.a(2)
            }
        }, e)
      })), [t, P]), {
        remindStatus: m,
        isShowSetting: A,
        handleRemind: function () {
          var e, n;
          t && P && (null !== (e = a.env) && void 0 !== e && e.isWmApp || null !== (n = a.env) && void 0 !== n && n.isMtApp ? function (e) {
            g ? m ? N(e) : xn(!0).then(function () {
              N(e)
            }, function () {
              G(!0)
            }) : oe()
          }(t) : (ln.isWxMpWm || ln.isWxMpMtWm) && function (e) {
            y || (w(!0),
              setTimeout(function () {
                w(!1),
                  console.log("### handleJump2Sub ###", "fetchingFlag timeout false", y)
              }, 2e3),
              _(e))
          }(t))
        },
        handleSetting: E,
        handleCancel: function () {
          Y(),
            G(!1),
            Z(gn)
        },
        enableRemind: P
      }
    };
  M(".bo7z1 {\n  position: relative;\n  width: 7.5rem;\n  flex-direction: column;\n  align-items: center;\n  padding: 0 0.24rem;\n}\n.QilV5 {\n  width: 7.02rem;\n  flex-shrink: 0;\n  min-height: 2.88rem;\n  flex-direction: column;\n  border-radius: 0.24rem;\n  overflow: hidden;\n}\n.WNoBO {\n  width: 7.02rem;\n  min-height: 2.88rem;\n  flex-direction: column;\n  padding-right: 0.24rem;\n  padding-bottom: 0.24rem;\n  padding-left: 0.24rem;\n}\n.V7tAK {\n  font-size: 0;\n}\n.YLsCy {\n  width: 7.02rem;\n}\n._4ELS8 {\n  position: absolute;\n  right: 0.24rem;\n  z-index: 1;\n}\n.-RJEV {\n  width: 0.64rem;\n  height: 0.28rem;\n  opacity: 0.32;\n  background: #000000;\n  border-top-right-radius: 0.32rem;\n  border-bottom-left-radius: 0.16rem;\n}\n.Jj3kc {\n  width: 0.64rem;\n  height: 0.28rem;\n  position: absolute;\n  top: 0;\n  right: 0;\n  font-weight: 400;\n  font-family: PingFangSC-Regular;\n  font-size: 0.2rem;\n  color: #ffffff;\n  line-height: 0.28rem;\n  text-align: center;\n  margin: 0;\n}\n.GHnBV {\n  width: 6.54rem;\n  flex-direction: column;\n  flex-shrink: 0;\n}\n.VU8Pm {\n  width: 6.54rem;\n  display: flex;\n  justify-content: space-between;\n  flex-shrink: 0;\n}\n");
  var Tn = u.default.gdUtil,
    Sn = u.default.env,
    Cn = u.default.gdLog,
    jn = t.GdcHOC(function (o) {
      var c = o.activityId,
        s = o._styleConfig,
        u = o.componentInfo,
        l = o.logicGroup,
        d = void 0 === l ? {
          compScene: "custom",
          backgroundImage: "",
          couponTips: "",
          ruleTips: "",
          layerImgGroup: {
            isShow: !1,
            imgUrl: "",
            hrefUrl: ""
          },
          componentStyle: {
            marginBtm: 0,
            marginTop: 0
          }
        } : l,
        m = o.moduleClick,
        h = j(e.useState(!1), 2),
        p = h[0],
        g = h[1],
        b = j(e.useState(G.WillBegin), 2),
        v = b[0],
        y = b[1],
        w = e.useMemo(function () {
          return d.compScene === U.SqjNoema
        }, [d.compScene]),
        x = function (t, n, i) {
          var o = j(e.useState([]), 2),
            c = o[0],
            a = o[1],
            s = j(e.useState({}), 2),
            u = s[0],
            l = s[1],
            d = e.useRef(0),
            m = j(e.useState(!1), 2),
            h = m[0],
            p = m[1],
            g = j(e.useState(""), 2),
            b = g[0],
            v = g[1],
            y = j(e.useState(-1), 2),
            w = y[0],
            x = y[1],
            S = j(e.useState(!0), 2),
            C = S[0],
            A = S[1],
            I = j(e.useState(!1), 2),
            M = I[0],
            P = I[1],
            O = j(e.useState(!1), 2),
            R = O[0],
            N = O[1],
            G = j(e.useState(!1), 2),
            z = G[0],
            D = G[1],
            U = e.useCallback(f(T().m(function e() {
              var r, i, o, c, s, u = arguments;
              return T().w(function (e) {
                for (;;)
                  switch (e.n) {
                    case 0:
                      if (o = null == (i = u.length > 0 && void 0 !== u[0] ? u[0] : {}) ? void 0 : i.roundCode,
                        c = null === (r = window) || void 0 === r || null === (r = r.location) || void 0 === r || null === (r = r.search) || void 0 === r ? void 0 : r.includes("isPreview=true"),
                      t || c) {
                        e.n = 1;
                        break
                      }
                      return e.a(2);
                    case 1:
                      s = function () {
                        var e = f(T().m(function e() {
                          var r;
                          return T().w(function (e) {
                            for (;;)
                              switch (e.n) {
                                case 0:
                                  if (!t) {
                                    e.n = 2;
                                    break
                                  }
                                  return e.n = 1,
                                    te(t, null == n ? void 0 : n.instanceID, !0);
                                case 1:
                                  return r = e.v,
                                    e.a(2, Fe.fetch({
                                      url: "".concat($("/api/rights/activity/secKill/info"), "?").concat(re(k(k({}, r), {}, {
                                        roundCode: o
                                      }))),
                                      method: "GET",
                                      withCredentials: !0
                                    }));
                                case 2:
                                  return e.a(2, Fe.fetch({
                                    url: "https://papi.sankuai.com/api/req/635e7ea5-dc1e-4a4a-83db-448596e8f5e3/gd-limited-time-grab-coupon",
                                    method: "GET"
                                  }));
                                case 3:
                                  return e.a(2)
                              }
                          }, e)
                        }));
                        return function () {
                          return e.apply(this, arguments)
                        }
                      }(),
                        s().then(function (e) {
                          var t, n = e.code,
                            r = e.data;
                          if (6 === n)
                            return N(!1),
                              void P(!0);
                          if (1 === n && r) {
                            var i = r.allGrabRounds,
                              c = r.currentGrabCouponInfo,
                              s = r.currentTime,
                              u = r.nextGrabCouponInfo,
                              f = r.activityStartTime,
                              m = r.activityEndTime;
                            if (P(!1),
                              !s)
                              return void A(!1);
                            if (p(!0),
                            f && m && s) {
                              var h = f <= (t = s) && t <= m;
                              if (!h)
                                return void A(!1);
                              A(h)
                            }
                            if (!(i && (null == i ? void 0 : i.length) > 0))
                              return void N(!0);
                            if (N(!1),
                            null != i && i.length) {
                              var g = i.sort(function (e, t) {
                                return we(e.startTime || "") - we(t.startTime || "")
                              });
                              a(g)
                            }
                            d.current = pe(s);
                            var b = o || "",
                              y = {};
                            c ? (b = (null == c ? void 0 : c.roundCode) || "",
                              y = c) : u && !o && (b = null == u ? void 0 : u.roundCode,
                              y = u);
                            var w = i.findIndex(function (e) {
                                return e.roundCode === b
                              }),
                              k = i[w],
                              T = ge([k.startTime, k.endTime], d.current),
                              S = he(d.current, T).status;
                            y.status = S,
                              x(w),
                              v(b),
                              l(y)
                          } else
                            N(!0),
                              P(!1),
                              l({}),
                              v(o || ""),
                              W("getGrabRoundInfo", e, "场次预发失败", o)
                        }).catch(function (e) {
                          N(!0),
                            P(!1),
                            l({}),
                            v(o || ""),
                            console.log("【神券节限时抢玩法】场次预发失败", e),
                            W("getGrabRoundInfo", e, "场次预发失败", i.roundCode, o)
                        }).finally(function () {
                          D(!0)
                        });
                    case 2:
                      return e.a(2)
                  }
              }, e)
            })), [t, null == n ? void 0 : n.instanceID]);
          e.useEffect(function () {
            U({})
          }, [t, U]);
          var E = e.useCallback(function () {
            U({
              roundCode: b
            })
          }, [b, U]);
          e.useEffect(function () {
            var e = Mach.on("pageWillAppear", E);
            return function () {
              "function" == typeof e && e()
            }
          }, [E]);
          var F = e.useCallback(function () {
              var e = f(T().m(function e(r) {
                var o, c, a, s, f;
                return T().w(function (e) {
                  for (;;)
                    switch (e.n) {
                      case 0:
                        return o = r.rightCode,
                          c = r.handleLogClick,
                          a = u.roundCode,
                          s = u.token,
                        i && _e.emit("wm-coupon-festival-tab-show", {
                          isStop: !0,
                          isNotClick: !0
                        }),
                          e.n = 1,
                          te(t, null == n ? void 0 : n.instanceID);
                      case 1:
                        f = e.v,
                          Fe.fetch({
                            url: "".concat($("/api/rights/activity/secKill/grab"), "?").concat(re({
                              cType: f.cType,
                              fpPlatform: f.fpPlatform,
                              wx_openid: f.wx_openid,
                              appVersion: f.appVersion,
                              latitude: f.latitude,
                              longitude: f.longitude,
                              actualLatitude: f.actualLatitude,
                              actualLongitude: f.actualLongitude
                            })),
                            data: {
                              activityId: t,
                              gdId: f.gdId,
                              pageId: f.pageId,
                              instanceId: f.instanceId,
                              rightCode: o,
                              roundCode: a,
                              grabToken: s
                            },
                            needSign: !0,
                            method: "POST",
                            contentType: "json",
                            headers: {
                              "Content-Type": "application/json"
                            },
                            needFingerprint: !0,
                            withCredentials: !0
                          }).then(function (e) {
                            var t = e.code,
                              n = e.data,
                              i = n || {},
                              s = i.currentTime,
                              f = i.coupon;
                            if (f) {
                              var m = (u.coupon || []).map(function (e) {
                                  return e.rightCode === f.rightCode ? k(k({}, e), f) : 9017 === n.subCode && e.rightCode === o ? k(k({}, e), {}, {
                                    status: 4
                                  }) : e
                                }),
                                h = k(k({}, u), {}, {
                                  coupon: m
                                });
                              l(h)
                            }
                            s && (d.current = pe(s)),
                              1 === t && n ? (1 === (null == n ? void 0 : n.subCode) && (c({
                                status: 1,
                                result: 0
                              }),
                                _e.emit("refreshCouponAssets", 3),
                                K()),
                              9017 === (null == n ? void 0 : n.subCode) && c({
                                status: 0,
                                result: 1
                              }),
                              (null == f ? void 0 : f.toastMsg) && Z(null == f ? void 0 : f.toastMsg)) : (c({
                                status: 0,
                                result: 3
                              }),
                                Z((null == f ? void 0 : f.toastMsg) || "当前抢券人数过多，请稍后再试~"),
                                W("grab", e, "抢券失败", a, null == r ? void 0 : r.rightCode))
                          }).catch(function (e) {
                            c({
                              status: 0,
                              result: 3
                            }),
                              Z("当前抢券人数过多，请稍后再试~"),
                              W("grab", e, "抢券失败", a, null == r ? void 0 : r.rightCode)
                          }).finally(function () {
                            setTimeout(function () {
                              i && _e.emit("wm-coupon-festival-tab-show", {
                                isNotClick: !1
                              })
                            }, 2e3)
                          });
                      case 2:
                        return e.a(2)
                    }
                }, e)
              }));
              return function (t) {
                return e.apply(this, arguments)
              }
            }(), [t, null == n ? void 0 : n.instanceID, u, i]),
            _ = e.useMemo(function () {
              return r.throttle(F, 1e3)
            }, [F]);
          return {
            grabRounds: c,
            currentGrabCouponInfo: u,
            currentTime: d.current,
            activeRoundCode: b,
            isInTime: C,
            needLogin: M,
            isFetchServerTime: h,
            isError: R,
            isFetchOver: z,
            activeTabIndex: w,
            getData: U,
            handelGetAwardCoupon: _
          }
        }(c, u, w),
        S = x.isFetchServerTime,
        C = x.grabRounds,
        A = x.currentGrabCouponInfo,
        I = x.currentTime,
        M = x.activeRoundCode,
        P = x.activeTabIndex,
        O = x.isInTime,
        R = x.needLogin,
        N = x.isError,
        z = x.isFetchOver,
        D = x.getData,
        E = x.handelGetAwardCoupon,
        L = e.useMemo(function () {
          return {
            gd_page_id: Tn.getGlobalData().gdId,
            instanceID: u.instanceID,
            activity_id: c,
            sub_instanceID: "",
            componentInfo: u
          }
        }, [c, u]),
        V = e.useCallback(function (e, t) {
          m({
            valBid: e,
            valLab: k(k({}, L), t)
          })
        }, [L, m]),
        B = e.useCallback(function (e, t) {
          Cn.view({
            valBid: e,
            valLab: k(k({}, L), t)
          })
        }, [L]),
        q = e.useMemo(function () {
          return {
            subscribeAPPConfig: {
              subAPPToggle: A.openAppAlert,
              subTaskId: A.appAlertId || ""
            },
            subscribeMPConfig: {
              subMPToggle: A.openAppletAlert,
              subMPTaskId: A.appletAlertId || "",
              successToast: A.appletAlertMsg || ""
            }
          }
        }, [A]),
        H = kn(q),
        X = H.remindStatus,
        J = H.isShowSetting,
        Q = H.handleRemind,
        ee = H.handleSetting,
        ne = H.handleCancel,
        ie = H.enableRemind,
        oe = e.useCallback(function () {
          Y();
          var e = d.layerImgGroup.hrefUrl;
          e && (e.startsWith("meituanwaimai") && !Sn.isApp() && Tn.handleCallApp ? Tn.handleCallApp({
            schemes: [{
              url: e
            }],
            failUrl: window.location.href
          }) : Tn.redirectTo(d.layerImgGroup.hrefUrl))
        }, [d.layerImgGroup.hrefUrl]),
        ce = e.useMemo(function () {
          return C.length > 1
        }, [C]),
        ae = e.useMemo(function () {
          var e, t = w ? 3 : 6;
          return ((null === (e = A.coupon) || void 0 === e ? void 0 : e.slice(0, t)) || []).reduce(function (e, t, n) {
            return n % 3 == 0 ? e.push([t]) : e[e.length - 1].push(t),
              e
          }, [])
        }, [A.coupon, w]),
        se = e.useCallback(function (e) {
          Y(),
            g(!e)
        }, []),
        ue = e.useCallback(function (e, t) {
          return e && t ? 20 : -2
        }, []),
        le = e.useMemo(function () {
          var e = d.couponButtonText;
          if (!e)
            return _;
          var t = Object.entries(e).reduce(function (e, t) {
            var n = j(t, 2),
              r = n[0],
              i = n[1];
            return i && (e[r] = i,
            "toUse" === r && (e.inUse = i)),
              e
          }, {});
          return k(k({}, _), t)
        }, [d.couponButtonText]),
        de = e.useMemo(function () {
          return i.jsxs(i.Fragment, {
            children: [i.jsx(Me, {
              textColor: s.textColor,
              timeTextColor: s.timeTextColor,
              grabRounds: C,
              activeRoundCode: M,
              isSqjNoema: w,
              currentTime: I,
              getData: D,
              setActiveRoundTimeStatus: y,
              activeTabIndex: P,
              logClick: V,
              logView: B
            }), ae.length > 0 ? i.jsx(t.View, {
              className: a("coupons-line-wrapper", "1ybe2o"),
              style: {
                marginTop: ue(ce, 1 === ae.length && 1 === ae[0].length)
              },
              children: ae.map(function (e, n) {
                return i.jsx(t.View, {
                  className: a("coupon-lines", "1ybe2o"),
                  children: e.map(function (t, r) {
                    return i.jsx(Ce, {
                      couponItemData: t,
                      isMultipleTimeTabs: ce,
                      sqjTagIcon: s.sqjTagIcon,
                      oneLineCouponNum: e.length,
                      currentTime: I,
                      activeRoundTimeStatus: v,
                      currentGrabStatus: A.status,
                      handelGetAwardCoupon: E,
                      remindStatus: X,
                      handleRemind: Q,
                      enableRemind: ie,
                      index: "".concat(n, "-").concat(r),
                      logClick: V,
                      logView: B,
                      activeTabIndex: P,
                      btnTextMap: le
                    }, "".concat(t.couponId, "-").concat(M))
                  })
                }, n)
              })
            }) : i.jsx(Pe, {
              isMultipleTimeTabs: ce
            })]
          })
        }, [C, M, I, ie, ae, ce, w, s.sqjTagIcon, s.textColor, s.timeTextColor, v, X, P, A.status, E, Q, D, V, B, ue]),
        fe = e.useMemo(function () {
          return R ? i.jsx(Ge, {
            abnormalType: F.NeedLogin,
            isSqjNoema: w,
            backgroundColor: s.backgroundColor,
            bgImg: d.backgroundImage
          }) : N ? i.jsx(Ge, {
            abnormalType: F.OtherAnomalies,
            isSqjNoema: w,
            backgroundColor: s.backgroundColor,
            bgImg: d.backgroundImage
          }) : O ? C.length > 0 ? i.jsx(t.View, {
            className: a("content-wrapper-outer", "1ybe2o"),
            children: w ? i.jsx(t.ImageBackground, {
              className: a("content-wrapper", "1ybe2o"),
              resizeMode: "scaleToFill",
              src: d.backgroundImage,
              style: {
                height: "288rpx"
              },
              children: de
            }) : i.jsx(t.View, {
              className: a("content-wrapper", "1ybe2o"),
              style: {
                backgroundColor: s.backgroundColor
              },
              children: de
            })
          }) : C.length <= 0 && !z ? i.jsx(t.View, {
            className: a("content-wrapper-outer", "1ybe2o"),
            children: i.jsx(t.View, {
              className: a("content-wrapper", "1ybe2o"),
              style: {
                height: "288rpx",
                backgroundColor: s.backgroundColor || "#FFCBDF",
                borderRadius: "24rpx"
              }
            })
          }) : void 0 : i.jsx(Ge, {
            abnormalType: F.NoInTime,
            isSqjNoema: w,
            backgroundColor: s.backgroundColor,
            bgImg: d.backgroundImage
          })
        }, [s.backgroundColor, C.length, N, O, w, d.backgroundImage, R, de]);
      return i.jsxs(t.View, {
        className: a("component-wrapper", "1ybe2o"),
        style: {
          marginTop: w ? 0 : d.componentStyle.marginTop,
          marginBottom: w ? 0 : d.componentStyle.marginBtm,
          flexDirection: "column"
        },
        onClick: Y,
        children: [d.layerImgGroup.isShow && d.layerImgGroup.imgUrl && !w && i.jsx(t.View, {
          className: a("layer-img-container", "1ybe2o"),
          children: i.jsx(t.Image, {
            src: d.layerImgGroup.imgUrl,
            className: a("layer-img", "1ybe2o"),
            onClick: oe,
            resizeMode: "widthFix"
          })
        }), fe, !w && S && i.jsxs(t.View, {
          className: a("rule-btn", "1ybe2o"),
          onClick: function () {
            return se(!1)
          },
          children: [i.jsx(t.View, {
            className: a("rule-btn-bg", "1ybe2o")
          }), i.jsx(t.Text, {
            className: a("rule-btn-text", "1ybe2o"),
            content: "规则"
          })]
        }), p && i.jsx(Ee, {
          onClose: function () {
            return se(!0)
          },
          ruleText: d.ruleTips
        }), i.jsx(n.Modal, {
          open: J,
          title: "开启推送通知",
          okText: "去设置",
          cancelText: "不开启",
          onCancel: ne,
          onOk: function () {
            return ee(q)
          },
          children: i.jsx(t.Text, {
            content: "为了您能收到活动订阅提醒，请前往系统设置中开启消息通知权限",
            maxLines: 0
          })
        })]
      })
    });
  return jn
});
window["@gdc/gd-limited-time-grab-coupon"] = window.__component_output__;
