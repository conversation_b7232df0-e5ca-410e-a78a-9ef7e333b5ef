{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"composite": false, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "declaration": true, "declarationMap": true, "inlineSources": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "preserveWatchOutput": true, "skipLibCheck": true}, "exclude": ["node_modules"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}