{"name": "js-de-obfuscator", "type": "module", "version": "0.3.0", "license": "MIT", "scripts": {"website:dev": "pnpm run -F website dev", "website:build": "pnpm run -F website build", "website:generate": "pnpm run -F website generate", "website:serve": "pnpm run -F website serve", "tmp": "npx deob tmp/input.js -o tmp", "lint": "eslint './{packages,website,scripts,meta}/**/*.{js,ts,tsx,vue,md,json}'", "lint:fix": "nr lint --fix", "release": "bumpp -r", "test": "vitest"}, "workspaces": ["website", "example", "packages/*"], "devDependencies": {"@antfu/eslint-config": "^1.1.2", "bumpp": "^9.2.0", "deob": "workspace:*", "eslint": "^8.53.0", "esm": "^3.2.25", "prettier": "^3.0.3", "tsx": "^4.6.2", "typescript": "^5.2.2", "vitest": "^1.0.0"}}