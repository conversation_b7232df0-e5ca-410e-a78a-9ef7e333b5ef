var _0x17d6 = ["#chatpad", "trace", "Frame", "sv8img", "pack_upact", "wklXD", "bXIrC", "Bp3img", "联机模式暂不支持金手指", "拳皇格斗进人30秒后不可踢人，请友好对战<br>", "<option\x20value=\x22", "test", "plmenu_switch", "extinputset", "createProgram", "XrraI", "calljoin", "房间人数已满", "rgba(255,\x20255,\x20255,1.0)", "gamepadlagV", "msgmenu_kick", "sev", "getProgramParameter", "KeyX5", "\x20的UID是：", "open", "fill", "gametools", "changedTouches", "watchplayer", "flex", "rgba(0,\x200,\x200,\x201.0)", "LxNyf", "1.0)\x22>", "tCCOl", "UNSIGNED_BYTE", "48px", "IGWGameCore", "fullmsgclose", "charAt", "kbOPg", "-10px", "px)", "reversetting", "INPUT", "run", "stopPropagation", "origin", "btn_tolook", "lineCap", "创建的录像", "Cwmky", "translate", "notify", "screen", "rgba(102,\x20101,\x2099,\x201.0)", "Bp1nick", "sv2", "\x20<div\x20class=\x22systemmsg\x20successsystemmsg\x22>\x0a{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "分享存档即将开放", "/lsvimg?sid=2&game=", "5秒后进入观战模式", "xtfHr", "createElement", "title", "repeat", "【专家】", "sbtn1", "getChannelData", "gamehelptitle", "add", "vip", "touchend", "rgba(255,\x20255,\x20255,0.8)", "房主加载了云存档,观众会在稍后同步", "#f0fff0", "vrhUr", "btn_record", "gSoundVol", "TEXTURE_2D", "marginLeft", "1180px", "texImage2D", "p3dev", "KeyA", "kick", "plmenu_close", "getElementById", "port", "savecard", "pswcheckval", "OmCWm", "looker", "btn_gamespeed", "AutoSpeed", "KeyX1", "Info", "联机模式目前不支持调速度", "destination", "rgba(0,\x200,\x200,0.8)", "igwi-jixu", "close", "igw:cmd:", ".wo1wan.com:6001/fcnext/link?lid=", "isArray", "】金手指大全", "btn_voice", "moveTo", "已重置所有按键位置", "随机武将", "[col]:", "fullscreenElement", "sbtn5", "iPad", "firstElementChild", "background-size", "p2frame", "rgba(0,0,\x200,\x200.8)", "program\x20filed\x20to\x20link:", "marginTop", "Vdglv", "Mobset", "lookcount", "敌人不攻击", "btn_sharesave", "btn_reset", "TEXTURE_MAG_FILTER", "sv4", "#soundvol", "input", "wSmGa", "sizeset_zone", "game_", "rgba(41,\x2040,\x2038,\x200.6)", "arc", "status", "gDSuper", "upload", "bnfTM", "0px", "createShader", "HP:", "#sv1time", "viptips", "ZgRwl", "geeker", "sv2img", "btn_kickapply", "basicRangeWidth", "igwi-zanting", "getExtension", "parentElement", "igwi-chacha1", "<div\x20class=\x22selectOption\x22\x20name=\x22inputdevicename\x22\x20key=\x22-1\x22>默认键盘</div>", "重开了游戏", "p4img", "popwin_savemanager", "unload", "畅玩大叔正在测，马上开放", "apply", "background", "checkfunc", "#909090", "VipEvent", "get", "CuWGI", "107px", "KeyY", "&sev=", "#if\x20__VERSION__\x20>=\x20130\x0a#define\x20COMPAT_VARYING\x20out\x0a#define\x20COMPAT_ATTRIBUTE\x20in\x0a#define\x20COMPAT_TEXTURE\x20texture\x0a#else\x0a#define\x20COMPAT_VARYING\x20varying\x20\x0a#define\x20COMPAT_ATTRIBUTE\x20attribute\x20\x0a#define\x20COMPAT_TEXTURE\x20texture2D\x0a#endif\x0a\x0a#ifdef\x20GL_ES\x0a#define\x20COMPAT_PRECISION\x20mediump\x0a#else\x0a#define\x20COMPAT_PRECISION\x0a#endif\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20VARps;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t3;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t2;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20_texCoord1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_color1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_position1;\x0astruct\x20input_dummy\x20{\x0a\x20\x20\x20\x20vec2\x20_video_size;\x0a\x20\x20\x20\x20vec2\x20_texture_size;\x0a\x20\x20\x20\x20vec2\x20_output_dummy_size;\x0a};\x0astruct\x20out_vertex\x20{\x0a\x20\x20\x20\x20vec4\x20_position1;\x0a\x20\x20\x20\x20vec4\x20_color1;\x0a\x20\x20\x20\x20vec2\x20_texCoord1;\x0a\x20\x20\x20\x20vec4\x20_t1;\x0a\x20\x20\x20\x20vec4\x20_t2;\x0a\x20\x20\x20\x20vec4\x20_t3;\x0a\x20\x20\x20\x20vec2\x20VARps;\x0a};\x0aout_vertex\x20_ret_0;\x0ainput_dummy\x20_IN1;\x0avec4\x20_r0010;\x0avec4\x20_v0010;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20aVertexCoord;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20COLOR;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20TexCoord;\x0aCOMPAT_VARYING\x20vec4\x20COL0;\x0aCOMPAT_VARYING\x20vec4\x20TEX0;\x0aCOMPAT_VARYING\x20vec4\x20TEX1;\x0aCOMPAT_VARYING\x20vec4\x20TEX2;\x0aCOMPAT_VARYING\x20vec4\x20TEX3;\x0a\x20\x0auniform\x20COMPAT_PRECISION\x20vec2\x20TextureSize;\x0avoid\x20main()\x0a{\x0a\x20\x20\x20\x20out_vertex\x20_OUT;\x0a\x20\x20\x20\x20vec2\x20_ps;\x0a\x20\x20\x20\x20_OUT._position1\x20=\x20vec4((aVertexCoord.x\x20*\x202.0\x20*\x20", "/replay/info?rid=", "toolsBounce", "\x20</text>", "webgl", "fillStyle", "bindTexture", "PCSet", "{}.constructor(\x22return\x20this\x22)(\x20)", "image/png", "[worker]", "qIPKG", "Backspace", "onerror", "wBvvz", "SP_C", "测试中,即将开放", "buttons", "remove", "baseHeightTxt", "rgba(0,0,0,1.0)", "touchstart", "none", "popwin_gamehelp", "set_vidstyle", "SetUp", "cookie", "txt_shareinfo", "CLAMP_TO_EDGE", "MobBase", "*关闭*", "btn_geeker", "KeyUp", "RangeSlider", "transferroom", "\x20暂停", "sbtn3", "=([^&]*)(&|$)", "/lsvimg?sid=8&game=", "Level", "LINK_STATUS", "KeyDown", "&rpsw=", "card_save7", "preservationAct", "mhRBA", "30px\x20Verdana", "location", "plmenu_open", "popwin_kickinfo", "charCodeAt", "igwi-jingyin1", "-200px", "sbtn4", "popwin_", "hide", "messagemenu", "createBuffer", "QvMUe", "set", "练习模式", "uHSQt", "258px", "mozCancelFullScreen", "className", "Lv.0", "sgqbpl_xst", "click", "self.webContents.openDevTools();", "closep", "sharedtip", "Img1", "chatpad", "KeyRight", "card_save5", "marginRight", "winclose", "usermenu_close", "card_save4", "globalAlpha", "gExtX3Key", "popwin_password", "selectTip", "RGBA", "usermenuList", "HmulW", "shadowOffsetY", "{{someopt}}", "usermenu_nobb", "Reinit", "&mode=1&rpsw=", "SaveReplay", "sv4img", "本浏览器不支持最新web特性，请下载畅玩空间PC版或者使用chrome游戏😿", "msg", "sgqbpl_yx", "cb_xbtnE", "ctlRepRan", "KeyJ", "Select", "NPmpc", "Nick4", "√请在手机微信查看", "rpQnA", "st4", "_com_TabAct", "basicset", "optionDown", "/lsvimg?sid=7&game=", "gGes4", "color", "d2i", "loadconfig", "cyc/gstyphoon.js?gstate=", "rgba(249,\x20211,\x2066,\x201.0)", "scrollTo", "name", "<div\x20class=\x22talkRandom\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkedName\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{nick}}：\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x27color:\x20#ff2800\x27>{{text}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20</div>", "<div\x20class=\x22talkRandom\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkedName\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{nick}}：\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20style=\x27color:\x20#01ff5b\x27>{{text}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20</div>", "gExtX1", ")\x20-\x201.0,\x200.0\x20-\x20((a_position.x\x20*\x202.0\x20*\x20", "IGWorld", "mozRequestFullScreen", "gh_showdmg", "revleftright", "pathname", "getUniformLocation", "Img3", "rgba(57,\x2055,\x2050,1.0", "top_left", "[未知难度]", "Hide", "sbtn", "init", "FnvGz", "全部隐藏", "YIRfB", "sendmsgbtn", "compileShader", "atob", "#fffc00", "link", "btn_loadgame", "compile", "Digit5", "HeadImg", "欢迎进入\x20【", "card_save8", "uniform2f", "YKOFD", "sgqbws_ms", "识别到POV摇杆，已自动设置好所有方向😁", "UMtpy", "一分钟内只允许保存一次", "btn_cheat", "x-shader/x-fragment", "btn_kbindreset", "lbtn8", "录像回放结束", "suspendIcon", "[SLkWZUpWiKHFXqYgGQYtsTUfGqY]", "请插入手柄后，以2P玩家游戏", "player", "p1lv", "popwin_save", "p4card", "\x20被玩家\x20", "TEXTURE0", "cb_lock43", "reversal", "AfpUf", "selectOption", "计时器", "then", "set_vidsize", "MYEcA", "EwFSr", "bufferData", "self.minimize();", "./cyc/click.wav", "viewport", "p4lv", "KeyH", "Size", "gamefillwinodw", "p3nick", "#2d2d2d", "GesSpeedText", "#sv5time", "p3card", "p2devtip", "剩余敌人", "系统消息", "SP_B", "KeyV", "timespan", "reserveTopDown", "layoutopttxt", "popwin_room", "borderRadius", "#ff0050", "suspend", "len", "\x20<text\x20style=\x27color:\x20rgb(255,121,0)\x27>录像ID:", "显卡驱动过久😢,画质提升已禁用，请升级显卡驱动", "._com_Tab\x20div", "FPgSb", "continue", "<p\x20style=\x27color:\x20#b15dff\x27>请开通会员解锁该云存档位<br>会员到期后存档永久存在，可随时续费使用</p>", "YnVEE", "房间:\x20", "mQLiC", "activeTexture", "gExtX5", "#layout_size", "/nextgame/trace/gamefeedback", "SP_E", "roompanel", "PC玩家", "linkProgram", "HvrQv", "btn_cancelpop", "usermenu_report", "replayctl", "Mob3", "Bind", "录像回放", "WwaTG", "邀请发送成功，等待伙伴到来吧", "btn_kickok", "p4nick", "pushbtn", "./img/waithead.png", "KkLgg", "whathis", "AudioContext", "UserAction", "#if\x20__VERSION__\x20>=\x20130\x0a#define\x20COMPAT_VARYING\x20out\x0a#define\x20COMPAT_ATTRIBUTE\x20in\x0a#define\x20COMPAT_TEXTURE\x20texture\x0a#else\x0a#define\x20COMPAT_VARYING\x20varying\x20\x0a#define\x20COMPAT_ATTRIBUTE\x20attribute\x20\x0a#define\x20COMPAT_TEXTURE\x20texture2D\x0a#endif\x0a\x0a#ifdef\x20GL_ES\x0a#define\x20COMPAT_PRECISION\x20mediump\x0a#else\x0a#define\x20COMPAT_PRECISION\x0a#endif\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20VARps;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t3;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t2;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20_texCoord1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_color1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_position1;\x0astruct\x20input_dummy\x20{\x0a\x20\x20\x20\x20vec2\x20_video_size;\x0a\x20\x20\x20\x20vec2\x20_texture_size;\x0a\x20\x20\x20\x20vec2\x20_output_dummy_size;\x0a};\x0astruct\x20out_vertex\x20{\x0a\x20\x20\x20\x20vec4\x20_position1;\x0a\x20\x20\x20\x20vec4\x20_color1;\x0a\x20\x20\x20\x20vec2\x20_texCoord1;\x0a\x20\x20\x20\x20vec4\x20_t1;\x0a\x20\x20\x20\x20vec4\x20_t2;\x0a\x20\x20\x20\x20vec4\x20_t3;\x0a\x20\x20\x20\x20vec2\x20VARps;\x0a};\x0aout_vertex\x20_ret_0;\x0ainput_dummy\x20_IN1;\x0avec4\x20_r0010;\x0avec4\x20_v0010;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20aVertexCoord;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20COLOR;\x0aCOMPAT_ATTRIBUTE\x20vec4\x20TexCoord;\x0aCOMPAT_VARYING\x20vec4\x20COL0;\x0aCOMPAT_VARYING\x20vec4\x20TEX0;\x0aCOMPAT_VARYING\x20vec4\x20TEX1;\x0aCOMPAT_VARYING\x20vec4\x20TEX2;\x0aCOMPAT_VARYING\x20vec4\x20TEX3;\x0a\x20\x0auniform\x20COMPAT_PRECISION\x20vec2\x20TextureSize;\x0avoid\x20main()\x0a{\x0a\x20\x20\x20\x20out_vertex\x20_OUT;\x0a\x20\x20\x20\x20vec2\x20_ps;\x0a\x20\x20\x20\x20_OUT._position1\x20=\x20vec4((aVertexCoord.y\x20*\x202.0\x20*\x20", "sgqbws_xst", "rgba(41,\x2040,\x2038,\x201.0)", "UNMASKED_RENDERER_WEBGL", "send", "sv1", "{{name}}", "&rj=", "#f01000", "onorientationchange", "layoutconfig", "floor", "ng=fuck\x20me&where=110\x20calling\x20", "p1card", "ayoXb", "application/json", "helpergui", "getElementsByName", "\x27>点击观看</a>", "700px", "addColorStop", "undefined", "isFunction", "enabledmg", "getAttribLocation", "gamedisplay", "svip", "【随机B武将开启】", "SpeedKey", "录像长度小于一分钟，不允许保存", "p3badge", "nvFff", "#cf0000", "在本房间已被禁言", "Ges2", "加入游戏", "axes", "\x20<div\x20class=\x22systemmsg\x22>\x0a{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "欢迎加入畅玩FC群一起交流，QQ群号:768212005", "oFTIq", "sv3img", "stop", "#aaa", "search", "【练习模式开启】", "height", "getGamepads", "gExtX4", "90px", "about:blank", "tab_keyborad", "yrQBS", "{{text}}", "RUbRu", "css", "nextSibling", "Bp2img", "suspended", "sHQX", "非房主不可重开", "#sv8time", "{{time}}", "KeyFire3", "auto", "&rname=", "onload", "info", "188px", "switch", "延时过高,请稍后", "byteLength", "preventDefault", "calc(", "gamepadbind", "x-shader/x-vertex", "usermenu", "外设\x20【", "ReplayLen", "p1badge", "[li]:", "exitFullscreen", "ARRAY_BUFFER", "sendBeacon", "scanlines", "sgqbws_rankwj", "LevelInfo", "addClass", "from", "calc(100%\x20-\x20300px)", "d4i", "vefFQ", "soundvol", "SP_F", "gh_showhptext", "href", "lookerlist", "DWudT", "iBeeo", "style", "response", "金手指只对会员和黑暗骑士开放,谢谢支持", "tab_roomwatch", "application/octet-stream", "%,100%", "return\x20(function()\x20", "lbtn1", "sgqbws_dead", "cb_xbtnD", "TextureSize", "16px", "each", "POST", "cbg_gamepadrev", "sv3time", "DONE", "vKHCT", "app.getGPUFeatureStatus()", "p2lv", "JkiBg", "tab_roomchat", "62px", "shaderSource", "useProgram", "ATZjD", "KeyX", "setRequestHeader", "非房主不可设置", ".replay-slider__value", "gFSuper", "selectOptionBox", "显卡加速初始化失败😢,你会看到黑屏,请重新开游戏试试,或者联系畅玩大叔反馈问题", "#ff0000", "【敌人不动开启】", "gamepad", "%\x20100%", "num", "forEach", "oncontextmenu", "rgba(57,\x2055,\x2050,\x201)", "ddJPf", "success", "</span>", "inputlayout", "#3fdd2e61", "sampleRate", "btn_savelayout", "KeyD", "sgqbws_yx", "backgroundColor", "外设【", "VERTEX_SHADER", "shadowColor", "KeyLeft", "(Ven", "boss", "ReplayName", "btn_closepop", "DrXwh", "QYeku", "ldXRm", "请开通黑暗骑士解锁该云存档位<br>黑暗骑士到期后存档永久存在，可随时续费使用", "createGain", "gSuperSpeed", "autokeyselect", "btn_restart", "igwflyfree", "path", "控制台：显伤害关闭", "显HP", "kwlah", "GesSpeed", "touchmove", "card_save2", "SetUpLeft_active", "cb_xbtnList", "#layout_transparent", "block", "qcWiZ", "NewQT", "#keybind_auto", "p2card", "rwRgn", "#F9A942", "/notgood", "shadowOffsetX", "RNzLV", "popwin_setting", "quickBox", "loadingimg", "{{id}}", "egAkt", "lobby", "JOxSl", "\x20<a\x20target=\x27_blank\x27\x20href=\x27https://play.wo1wan.com/jj/replay?rid=", "game_playeract", "keybind", "rZqaL", "gGes2", "#50ff60", "usermenu_kick", "KeyU", "tab_keyauto", "GQpcO", "onopen", "toolsBtn", "gametap", "tNMwC", "createLinearGradient", "bindBuffer", "check", "#888888", "sv3", "Mob4", "fillRect", "repsset", "clientWidth", "self.webContents.send(\x27gamesync\x27,", "p4frame", "KeyX6", "basics", "toLowerCase", "scrollHeight", "connect", "#30ff30", "Bp4img", "fcin_", "单人玩呢，发啥消息啊😢", "Name", "pressed", "plmenu_give", "igwi-shouji", "other_com_TabAct", "timeStamp", "attribute", "NTaRF", "usermenu_add", "118px", "rgba(0,\x200,\x200,\x200)", "#shareimg", "sv7img", "scrollLeft", "btn_save", "subarray", "keybd_fillkey", "联机已掉线，本场已变为单机模式，请重新连接", "btn_fullscr", "yIugX", "siblings", "UserStyle", "Key", "appendChild", "】\x20房间", "lbtn4", "popwin_sharegame", "</div><div></div>", "reversalBox", "#gamemode", "pos", "/lsvimg?sid=1&game=", "igwi-yinliang", "removeClass", "GPSetx", "进入【1P\x20触屏】\x20，【2P\x20手柄摇杆】多人模式", "alrJW", ".wo1wan.com:6001/fcnext/watch?lid=", "82px", "size", "speedbtn", "【极难】", "colno", "classList", "{{nick}}", "单机双人", "v1.0", "readyState", "aVertexCoord", "menuOverlay", "plmenu_kick", "KeyN", "bind", ")\x20*\x20-1.0\x20+\x201.0,\x200,\x201);\x0a\x20\x20\x20\x20_ps\x20=\x201.00000000E+00/TextureSize;\x0a\x20\x20\x20\x20_OUT._t1\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x20float(float(-_ps.y)));\x0a\x20\x20\x20\x20_OUT._t2\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x200.00000000E+00);\x0a\x20\x20\x20\x20_OUT._t3\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x20float(float(_ps.y)));\x0a\x20\x20\x20\x20_ret_0._position1\x20=\x20_OUT._position1;\x0a\x20\x20\x20\x20_ret_0._color1\x20=\x20COLOR;\x0a\x20\x20\x20\x20_ret_0._texCoord1\x20=\x20TexCoord.xy;\x0a\x20\x20\x20\x20_ret_0._t1\x20=\x20_OUT._t1;\x0a\x20\x20\x20\x20_ret_0._t2\x20=\x20_OUT._t2;\x0a\x20\x20\x20\x20_ret_0._t3\x20=\x20_OUT._t3;\x0a\x20\x20\x20\x20VARps\x20=\x20_ps;\x0a\x20\x20\x20\x20gl_Position\x20=\x20_OUT._position1;\x0a\x20\x20\x20\x20COL0\x20=\x20COLOR;\x0a\x20\x20\x20\x20TEX0.xy\x20=\x20TexCoord.xy;\x0a\x20\x20\x20\x20TEX1\x20=\x20_OUT._t1;\x0a\x20\x20\x20\x20TEX2\x20=\x20_OUT._t2;\x0a\x20\x20\x20\x20TEX3\x20=\x20_OUT._t3;\x0a\x20\x20\x20\x20return;\x0a\x20\x20\x20\x20COL0\x20=\x20_ret_0._color1;\x0a\x20\x20\x20\x20TEX0.xy\x20=\x20_ret_0._texCoord1;\x0a\x20\x20\x20\x20TEX1\x20=\x20_ret_0._t1;\x0a\x20\x20\x20\x20TEX2\x20=\x20_ret_0._t2;\x0a\x20\x20\x20\x20TEX3\x20=\x20_ret_0._t3;\x0a\x20\x20\x20\x20return;\x0a}\x20", "KeyJoy", "bgnHw", "function", "p1frame", "只有房主可禁言", "】断开", "beginPath", "iPhone请使用添加到桌面实现全屏App", "单机模式，可使用云存档等方便功能", "\x20被房主在本房间禁言一小时", "canvas", "attachShader", "<div>", "房间超过10小时，暂不支持观战", "webgl2", "WEBGL_debug_renderer_info", "\x20快速存1号档", "sgqbqx_xst", "setAttribute", "/lsvimg?sid=4&game=", "Lv.", "#00c030", "webkitRequestFullScreen", "checkbox", "qPmxL", "YMpxE", "#sv7time", "warn", "mouseleave", "swiftshader", "rgba(255,255,255,1.0)", "drawArrays", "请先输入邀请信息", "https://static.wo1wan.com/game/empsv.png", "active-jianpan", "opacity", "option", "cyc/gstyphoon.js?geeker=1", "split", "inline-block", "getProgramInfoLog", "attribute\x20vec2\x20a_position;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20varying\x20highp\x20vec2\x20v_textureCoord;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20void\x20main()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20vec4((a_position.x\x20*\x202.0\x20*\x20", "ceil", "FVCrv", "IYaOB", "#sv6time", "rgba(249,\x20161,\x2066,0.8)", "u_sampler", "MicroMessenger", "rgba(249,\x20211,\x2066,\x200.8)", "jLKGf", "gamecheat_setlist", "【大师】", "#e09a06", "KeyFire1", "baseWidthTxt", "tqPSO", "openwin", "/nextgame/igwuser/useropeninfo?uid=", "cyc/gstyphoon.js?sev=", "acx", "Toody", "sv5img", "bLddq", "\x20继续", "chattab", "Tqvsu", "msgsubmenu", "iPhone", "ryJug", "#111", "watchsay", "rpsw", "GameType", "GET", "createTexture", "DDAhP", "tooltip", "gExtX4Key", "【随机B面武将开启】", "touxiangname", "<span>", "removeChild", "body", "p2dev", "HLhLn", "#ffffff", "game", "drawbtn", "createScriptProcessor", "&game=", "Svip", "setupover", "TRIANGLES", "#igwWinCtl", "1.0", "</text>", "0.3", "concat", "getContext", "cheatclose", "kbdbind", "ScrStyle", "Nick", "webkitExitFullscreen", "ingame", "&mob=1", "tab_gamepad", "childNodes", "act", "^([^\x20]+(\x20+[^\x20]+)+)+[^\x20]}", "(?)", "rgba(0,\x200,\x200,\x200.4)", "inputdevicename", "显伤害", "usermenu_uid", "不可选", "tab_keyetc", "禁言失败", "messageinput", "replace", "clientY", "fullScreenMsgBox", "scale", "rname", "btn_expandPlayer", "AutoData", "local", "random", "\x20\x20<div\x20class=\x22chatmessage\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22prefix\x22></span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22chatnick\x22>{{nick}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22msgtime\x22>{{time}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22msgtext\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "sgqbqx_yx", "Bp1img", "gamepad_select", "bIwAL", ")\x20*\x20-1.0\x20+\x201.0,\x200,\x201);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20v_textureCoord\x20=\x20vec2(a_position.x,\x20a_position.y);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}", "firstChild", "手机玩家", "visibilitychange", "plmenu_addfriend", "Digit1", "#202020", "游戏速度已调到", "【随机武将开启】", "boostSpeed", "switchChatAndWatch", "sv1img", "Uid", "clientHeight", "setProperty", "a_position", "sv8time", "/fc/lsvimg?sid=5&game=", "yTEli", "\x20在本房间禁言一小时", "&linkid=", "rect", "width", "gDisHoldLR", "minWidth", "exception", "active-kbdext", "error", "jjin_", "p3lv", "联机金手指还未开放", "dZXEr", "gExtX3", "qahjz", "[name=plmenuitem]", "/lsvimg?sid=3&game=", "gExtX", "children", "icEes", "text", "sendSync", "string", "gstate", "step", "cb_xbtnB", "100%", "blur", "parse", "BlAvo", "0.0", "280px", "】接入", "visibilityState", "0.8", "lag", "只有联机模式才允许保存录像", "aroJj", "outputBuffer", "gScrType", "st1", "#5be8a0", "/lsvimg?sid=6&game=", "igwi-diannao-copy", "Vip", "texSubImage2D", "KeyF", "layout_transparent", "/nextgame/feedback/event", "sv1time", "baseLatency", "Lock43", "fillText", "gh_showmode", "responseType", "src", "popwin_vote", "TEXTURE_WRAP_T", "backgroundSize", "elrCK", "GcrLO", "d1i", "rid", "128px", "#eee", "KeyI", "debug", "max", "btn_savemgr", "getShaderParameter", "dxEGR", "minHeight", "gamehelp_setlist", "loadingbox", "lockScr", "EFPOH", "请出房间", "FLOAT", "\x20col:", "2px", "Content-type", "rgba(255,\x20255,\x20255,0.16)", "vertexAttribPointer", "time.", "Digit", "if(!self.isMaximized())\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20self.maximize()\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20else\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20self.unmaximize()", "onofftools", "--screenwidth", "显血条", "LUT", "keybd_autofix", "&gstate=", "btn_kb_reset", "btn_sharegame", "#whathis", "p4badge", "/saveset?type=fc&gid=", "wss://link00", "phonewrap", "stroke", "SBiix", "Start", "rgba(249,\x20211,\x2066,\x200.9)", "card_save1", "42px", "Ges1", "pop_usercardmenu", "畅玩空间\x20-\x20", "KeyK", "toLocaleTimeString", "Obj", "strokeText", "/replay/create", "随机B面武将", "KeyS", "enableVertexAttribArray", "roOcQ", "identifier", "linkid", "uniform1i", "checked", "SP_A", "/fc/lsvimg?sid=7&game=", "userAgent", "TEXTAREA", "eRGvm", "AllHide", "/fc/lsvimg?sid=2&game=", "Mob2", "cb_lrrever", "attr", "Repspan", "btn_pause", "st2", "【传说】", "gESuper", "btn_showquickmsg", "font", "本游戏金手指可用", "lookupdate", "UNSIGNED_SHORT_5_5_5_1", "suspendTxt", "cb_xbtnC", "gamebigwin", "middle", "/nextgame/igwuser/userinfo", "#f06f00", "PndhS", "#sv3time", "#keybind_ext", "p2badge", "vPAKt", "application/x-www-form-urlencoded", "未连接手柄", "GamePadType", "btn_setok", "KeyStart", "NISKB", "{\x22type\x22:\x22screen\x22,\x20\x22style\x22:", "eNTRe", "22px", "PrivateBtn_readact", "roominfo", "1346580azhUObsoIETRcfyy23f923f3n2", "KeyL", "calc(100%\x20-\x20180px)", "\x20<div\x20class=\x22systemmsg\x20lightsystemmsg\x22>\x0a{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "<option\x20value=\x22-1\x22>无</option>", "vibrate", "kicklook", "KeyP", "lbtn7", "calc(70%\x20+\x2060px)", "roomOwnerFlag", "clearRect", "https://static.wo1wan.com/game/close.png", "sgqbws_rankBwj", "pixelated", "显模式", "igwmobgame", "watchset", "/fc/lsvimg?sid=8&game=", "checkpwd", "4\x203\x20A\x20", "|}?{:><_)(*&!@#$%^~", "gCSuper", "look", "toFixed", "LookerNick", "html", "kvnOJ", "innerHTML", "usermenu_open", "gSkipFrame", "drawImage", "usersubmenu", "gXfWS", "fail", "could\x20not\x20compile\x20shader:", "btn_cancellayout", "d3i", "vSPFy", "/helper/pushwx?type=jj&id=", "gamename", "#lookerpad", "sgqxzq_rankwj", "sgqbws_time", "oSwNh", "strokeStyle", "btn_vote_yes", "GameID", "plmenu_report", "p2img", "altKey", "lbtn6", "img", "object", "/fc/lsvimg?sid=1&game=", "play", "savereplay", "substr", "file", "Height", "lbtn5", "正在观看\x20", "code", "top", "OrLMe", "uicvn", "#keybind_keyboard", "p2nick", "mouseup", "ajax", "createRadialGradient", "onreadystatechange", "activeElement", "/fc/lsvimg?sid=4&game=", "basicRangeHeight", "dir", "#F9D342", "innerText", "tWrmP", "banicon", "cloneNode", "left", ".0)\x20-\x201.0,\x20(aVertexCoord.y\x20*\x202.0\x20*\x20", "textContent", "lineWidth", "onmessage", "QfXMY", "length", "NickName", "KeyCoin", "item", "Width", "qfvuG", "已隐藏", "popwin_kicktips", "marginBottom", ":\x20<", "margin", "p3img", "sgqbpl_xs", "rgba(249,\x20161,\x2066,\x201.0)", "gExtX5Key", "texParameteri", "inroomset", "btn_setting", "next", "tzMTn", "/fc/lsvimg?sid=3&game=", "nMAJi", "抱歉，显卡加速失败，画质提升不可用", "目前只允许房主保存房间录像", "quickMessage", "Mob1", "oWiWJ", "gJoyLagFix", "p1dev", "message", "active", "/fc/svimg?sid=", "hasOwnProperty", "#keybind_etc", "allowlookermsg", "<p\x20style=\x27color:\x20#b15dff\x27>请开通黑暗骑士解锁该云存档位<br>黑暗骑士到期后存档永久存在，可随时续费使用</p>", "xIezG", "DxCms", "stringify", "ZBUmJ", "./img/loadfail.png", "nodeName", "mode", "TEXTURE_MIN_FILTER", "STATIC_DRAW", "/lsvimg?sid=5&game=", "4\x208\x202\x20A\x20", "&state=", "popwin_keybind", "#keybind_gamepad", "gGesSpeed", "nogamehelp", "chatBtn", "log", "https://world.wo1wan.com/sharegame", "Tab", "Get", "popwin_gamecheat", "reload", "st3", "TEXTURE1", "IBFAo", "ScFuK", "显优先", "KeyB", "btn_savegame", "gExtX6Key", "p1nick", "angle", "gKeyRev", "rgba(220,\x2067,\x2059,\x201)", "gh_showhhp", "邀请发送失败", ".SetUpLeft_tab", "start", "p4dev", "igwi-gengduo2", "count", "card_save6", "AutoSpeedText", "gain", "resume", "textAlign", "igwi-kaishi", "Ges3", "script", "Nick3", "lookout", "gh_studymod", "btn_resetlayout", "orientation", "KeyG", "lineTo", "#sv2time", "table", "data", "parent", "恢复单机单人模式", "px\x20Microsoft\x20YaHei", "File_ind_act", "控制台：显伤害打开", "gBSuper", "#sv4time", "round", "cb_xbtnA", "chatpanel", "setcheat", "indexOf", "repspeedRan", "p1img", "abs", "AllXP", "Numpad", "target", "KeyFire4", "/gameinfo?type=fc&gameid=", "optionDownRev", "change", "process", "lbtn", "AViCd", "WlDEH", ")\x20-\x201.0,\x200.0\x20-\x20((aVertexCoord.x\x20*\x202.0\x20*\x20", "gASuper", "lastgamepad", "button", "disabled", "UUID", "nick", "\x20快速读1号档", "div", "now", "allowlook", "Texture", "focus", "******************", "loadingpos", "ctrlKey", "/svload?game=", "friend", "addEventListener", "gGes1", "type", "sv7time", "peripheral", "wEAXy", "btn_save_write", "进入【1P\x20键盘】\x20，【2P\x20手柄摇杆】多人模式", "KeyM", "usermenu_give", "decodeAudioData", "FcZHG", "gh_deadman", "shadowBlur", "Bp2nick", "removeEventListener", "closePath", "clientX", "Bp4nick", "pmode=pc", "模式:", "<div\x20class=\x22groupgamecheatset\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x27onegamehelpsetName\x27>{{name}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<select\x20\x20id=\x22{{id}}\x22\x20class=\x22\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<option\x20value=\x22-1\x22>关</option>{{someopt}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</select>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "/cheatcode?type=fc&gid=", "\x20\x20<div\x20class=\x22talking\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTop\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22yellowStick\x22></div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingName\x22>{{nick}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTime\x22>{{time}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingWords\x22>{{text}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "PwXOg", "value", "async\x20function\x20fsssdsad()\x20{\x0a\x20\x20\x20\x20console.log(\x27init\x20aw\x27,acx);\x0a\x20\x20\x20\x20await\x20acx.audioWorklet.addModule(\x27cyc/cycsd.js\x27);\x0a\x0a\x20\x20\x20\x20var\x20igwSoundDrv\x20=\x20new\x20AudioWorkletNode(acx,\x20\x27igw-sound-processor\x27,{numberOfOutputs:2})\x0a\x20\x20\x20\x20igwSoundDrv.connect(gain)\x0a\x20\x20\x20\x20return\x20igwSoundDrv;}\x0areturn\x20fsssdsad()", "image-rendering", "layout_size", "Nick2", "cb_xbtnF", "votetext", "msgmenu_ban", "qaRWy", "btn_vote_no", "&uuid=", "shift", "lineno", "revall", "p3frame", "\x20\x20<div\x20class=\x22chatmessage\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22prefix\x22></span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22lookflag\x22>观战</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22chatnick\x22>{{nick}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22msgtime\x22>{{time}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22msgtext\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "gh_randsel", "VipLevel", "gKeyRevLR", "set_vidimpove", "post", "【天王】", "tcDoa", "CoLDH", "sgqbqx_sh", "json", "loadingprogress", "superkeyset", "dbtn", "rgba(10,\x205,\x205,\x200.7)", "toolsbtn", "sTextureSize", "#0478D2", "Img2", "xhRZm", "录像已保存到服务器\x20时长:", "#loadingpos", "msgmenu_uid", "#6bff00", "?&look=1&id=", "#888", "点击上位", "--sceenleft", "游戏大厅无法找到，请从大厅开始游戏", "min", "Badge", "cb_vidstyle", "sv6img", "push", "sgqbws_sh", "eqFyt", "&mode=1&sev=", "btn_save_read", "gamepadlag", "gGes3", "等待连接", "ADMco", "vote", "0.7", "arraybuffer", "/sayban", "NEAREST", "--screenheight", "set_vidscanline", "sbtn2", "keydown", "11px\x20zcool-gdh", "房主才可设置", "OPEN", "sync", "scrollTop", "gamepaddisconnected", "VyDEU", "key", "toLocaleString", "FRAGMENT_SHADER", "return\x20/\x22\x20+\x20this\x20+\x20\x22/", "buffer", "textBaseline", "join", "tabIndex", "5分钟只能邀请一次", "好友请求发送成功，对方会在大厅收到消息", "rgba(255,\x20255,\x20255,\x20\x200.6)", "setItem", "state", "rgba(249,\x20211,\x2066,1.0)", "console", "如果一直没声音，请检查手机是否静音了。", "Ges4", "plmenu_ban", "call", "/fc/lsvimg?sid=6&game=", "/svupdate?game=", "录像保存失败", "address", "KeyFire2", "setupclose", "usercard", "gamescr", "sgqbws_study", "getItem", "winmin", "Pad", "gh_randbsel", "Enter", "LookerList", "voterez", "devchange", "Escape", "SP_D", "constructor", "GbOuI", "</option>", "getClientRects", "#fff", "offsetWidth", "TexCoord", "Alpha", "Nick1", "Img4", "房主重开了游戏", "keyup", "cb_allrever", "requestFullscreen", "UibCK", "postMessage", "./img/close.png", "watch", "<div\x20class=\x22talking\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTop\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22yellowStick\x22></div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22watchingIcon\x22>观战</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingName\x22>{{nick}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTime\x22>{{time}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingWords\x22>{{text}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>", "sgqbpl_sh", "/fc/svupdate?game=", "xJLqd", "watchpanel", "match", "nXDvA", "display", "#FFFFFF", "ZqerC", "gExtX6", "onaudioprocess", "center", "gh_showyouxian", "gCtlZD", "Image", "LHJQh"];
(function (_0x518782, _0x17d62a) {
  var _0x36287b = function (_0x2ba768) {
    while (--_0x2ba768) {
      _0x518782["push"](_0x518782["shift"]());
    }
  };
  var _0x55680e = function () {
    var _0x511f60 = {
      data: {
        key: "cookie",
        value: "timeout"
      },
      setCookie: function (_0x132c6c, _0x51a951, _0x447e46, _0x135eb6) {
        _0x135eb6 = _0x135eb6 || {};
        var _0x598da1 = _0x51a951 + "=" + _0x447e46;
        var _0x5dc587 = 0x0;
        for (var _0x299c26 = 0x0, _0x550973 = _0x132c6c["length"]; _0x299c26 < _0x550973; _0x299c26++) {
          var _0x1c0090 = _0x132c6c[_0x299c26];
          _0x598da1 += ";\x20" + _0x1c0090;
          var _0x590a46 = _0x132c6c[_0x1c0090];
          _0x132c6c["push"](_0x590a46);
          _0x550973 = _0x132c6c["length"];
          if (_0x590a46 !== !![]) {
            _0x598da1 += "=" + _0x590a46;
          }
        }
        _0x135eb6["cookie"] = _0x598da1;
      },
      removeCookie: function () {
        return "dev";
      },
      getCookie: function (_0xee4793, _0xc6bab) {
        _0xee4793 = _0xee4793 || function (_0x3ddbfb) {
          return _0x3ddbfb;
        };
        var _0x529189 = _0xee4793(new RegExp("(?:^|;\x20)" + _0xc6bab["replace"](/([.$?*|{}()[]\/+^])/g, "$1") + "=([^;]*)"));
        var _0x4328e8 = function (_0x39e050, _0x5b0e23) {
          _0x39e050(++_0x5b0e23);
        };
        _0x4328e8(_0x36287b, _0x17d62a);
        return _0x529189 ? decodeURIComponent(_0x529189[0x1]) : undefined;
      }
    };
    var _0x45b829 = function () {
      var _0x1aacf6 = new RegExp("\x5cw+\x20*\x5c(\x5c)\x20*{\x5cw+\x20*[\x27|\x22].+[\x27|\x22];?\x20*}");
      return _0x1aacf6["test"](_0x511f60["removeCookie"]["toString"]());
    };
    _0x511f60["updateCookie"] = _0x45b829;
    var _0x7ddd80 = "";
    var _0xcb15e7 = _0x511f60["updateCookie"]();
    if (!_0xcb15e7) {
      _0x511f60["setCookie"](["*"], "counter", 0x1);
    } else if (_0xcb15e7) {
      _0x7ddd80 = _0x511f60["getCookie"](null, "counter");
    } else {
      _0x511f60["removeCookie"]();
    }
  };
  _0x55680e();
})(_0x17d6, 0x1ba);
var _0x3628 = function (_0x518782, _0x17d62a) {
  _0x518782 = _0x518782 - 0x0;
  var _0x36287b = _0x17d6[_0x518782];
  return _0x36287b;
};
var _0x14245c = function () {
  var _0x2fc5eb = !![];
  return function (_0x425f1e, _0x142117) {
    var _0x5ddaad = _0x2fc5eb ? function () {
      if (_0x142117) {
        var _0x5c0f70 = _0x142117[_0x3628("0x45e")](_0x425f1e, arguments);
        _0x142117 = null;
        return _0x5c0f70;
      }
    } : function () {};
    _0x2fc5eb = ![];
    return _0x5ddaad;
  };
}();
var _0x57fa55 = _0x14245c(this, function () {
  var _0xe11fe5 = function () {
    var _0x31d5a1 = _0xe11fe5[_0x3628("0x39c")](_0x3628("0x379"))()[_0x3628("0x4f7")](_0x3628("0x163"));
    return !_0x31d5a1[_0x3628("0x3ca")](_0x57fa55);
  };
  return _0xe11fe5();
});
_0x57fa55();
var _0x132c6c = function () {
  var _0x647674 = !![];
  return function (_0x3b9796, _0x5eccf7) {
    var _0x2a8b1d = _0x647674 ? function () {
      if (_0x3628("0xaa") === "TQwYr") {
        (i_CYQWn = i_sxfci_("gamescr"))[_0x3628("0x3e")]["width"] = "", i_CYQWn[_0x3628("0x3e")][_0x3628("0x9")] = "", i_CYQWn[_0x3628("0x3e")][_0x3628("0x292")] = _0x3628("0x44a");
      } else {
        if (_0x5eccf7) {
          var _0x19d953 = _0x5eccf7[_0x3628("0x45e")](_0x3b9796, arguments);
          _0x5eccf7 = null;
          return _0x19d953;
        }
      }
    } : function () {};
    _0x647674 = ![];
    return _0x2a8b1d;
  };
}();
var _0xcb15e7 = _0x132c6c(this, function () {
  var _0x2443de = function () {
    if (_0x3628("0x8e") === _0x3628("0x8e")) {
      var _0x122529;
      try {
        _0x122529 = Function(_0x3628("0x44") + _0x3628("0x470") + ");")();
      } catch (_0x58db5c) {
        if ("LHJQh" === _0x3628("0x3be")) {
          _0x122529 = window;
        } else {
          i_YBrGP_[_0x3628("0x1bb")](i_YBrGP_["TEXTURE_2D"], 0x0, 0x0, 0x0, i_kRDkS_, i_kPQcC_, i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_[_0x3628("0x21a")], i_GSRYe), i_YBrGP_[_0x3628("0x114")](i_YBrGP_[_0x3628("0x152")], 0x0, 0x6);
        }
      }
      return _0x122529;
    } else {
      i_FSFze[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(i_FSFze)];
    }
  };
  var _0x728e71 = _0x2443de();
  var _0x5ae432 = function () {
    return {
      key: _0x3628("0x28b"),
      value: _0x3628("0xc5"),
      getAttribute: function () {
        for (var _0x208e12 = 0x0; _0x208e12 < 0x3e8; _0x208e12--) {
          var _0x4cbf8c = _0x208e12 > 0x0;
          switch (_0x4cbf8c) {
            case !![]:
              return this[_0x3628("0x28b")] + "_" + this[_0x3628("0x32d")] + "_" + _0x208e12;
            default:
              this["item"] + "_" + this["value"];
          }
        }
      }()
    };
  };
  var _0xef2efa = new RegExp(_0x3628("0x508"), "g");
  var _0x147717 = "S.LkWZUwopW1iKHFXwaqYgnG.comQYtsTUfGqY"["replace"](_0xef2efa, "")[_0x3628("0x11b")](";");
  var _0x5345c6;
  var _0x4ec35d;
  var _0x1d50bf;
  var _0x58622c;
  for (var _0x5c39a3 in _0x728e71) {
    if (_0x5c39a3["length"] == 0x8 && _0x5c39a3[_0x3628("0x49a")](0x7) == 0x74 && _0x5c39a3[_0x3628("0x49a")](0x5) == 0x65 && _0x5c39a3["charCodeAt"](0x3) == 0x75 && _0x5c39a3[_0x3628("0x49a")](0x0) == 0x64) {
      _0x5345c6 = _0x5c39a3;
      break;
    }
  }
  for (var _0x257b33 in _0x728e71[_0x5345c6]) {
    if (_0x257b33[_0x3628("0x288")] == 0x6 && _0x257b33["charCodeAt"](0x5) == 0x6e && _0x257b33["charCodeAt"](0x0) == 0x64) {
      if (_0x3628("0x36") !== _0x3628("0x36")) {
        i_tXrZr_(_0x3628("0x86"), "input", function (_0x53ba1e) {
          i_sxfci_("GesSpeedText")["innerText"] = this["value"], i_WmyDOu[_0x3628("0x2ba")] = parseInt(this[_0x3628("0x32d")]);
        });
      } else {
        _0x4ec35d = _0x257b33;
        break;
      }
    }
  }
  if (!("~" > _0x4ec35d)) {
    for (var _0x5a456b in _0x728e71[_0x5345c6]) {
      if (_0x5a456b["length"] == 0x8 && _0x5a456b[_0x3628("0x49a")](0x7) == 0x6e && _0x5a456b[_0x3628("0x49a")](0x0) == 0x6c) {
        if (_0x3628("0x3f2") !== _0x3628("0x3f2")) {
          i_Qyfwws[_0x3628("0x23c")](i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo[_0x3628("0x191")], i_CwRAHo[_0x3628("0x9")]), i_aZfEec ? (i_GHKWya["ln"](), i_Qyfwws[_0x3628("0x250")](i_CwRAHo, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"])) : (i_GHKWya["un"](), i_Qyfwws[_0x3628("0x250")](i_NWETLo, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"]));
        } else {
          _0x1d50bf = _0x5a456b;
          break;
        }
      }
    }
    for (var _0x2442d6 in _0x728e71[_0x5345c6][_0x1d50bf]) {
      if (_0x3628("0x19c") !== "qahjz") {
        i_WwiRv_(_0x3628("0x2b8"), _0x3628("0x8c"));
      } else {
        if (_0x2442d6[_0x3628("0x288")] == 0x8 && _0x2442d6["charCodeAt"](0x7) == 0x65 && _0x2442d6[_0x3628("0x49a")](0x0) == 0x68) {
          if (_0x3628("0x13a") === _0x3628("0x13a")) {
            _0x58622c = _0x2442d6;
            break;
          } else {
            if (i_cNjsB_ = i_PhEme["data"][i_esjAjl], i_ZHidMe = i_PhEme[_0x3628("0x2e7")][i_XcWfHl], i_thkew_[_0x3628("0x191")] = i_PhEme[_0x3628("0x2e7")][i_CSsKql], i_thkew_[_0x3628("0x9")] = i_PhEme["data"][i_WKTXVl], i_thkew_[_0x3628("0x191")] < i_thkew_[_0x3628("0x9")]) {
              var _0x430d96 = i_thkew_[_0x3628("0x191")];
              i_thkew_[_0x3628("0x191")] = i_thkew_[_0x3628("0x9")], i_thkew_[_0x3628("0x9")] = _0x430d96, i_TBJJz_ = !0x0;
            }
            i_kRDkS_ = i_thkew_[_0x3628("0x191")], i_kPQcC_ = i_thkew_[_0x3628("0x9")], i_iDQBme && (i_thkew_["width"] *= i_yhDYO_, i_thkew_[_0x3628("0x9")] *= i_yhDYO_), ctlEnable = i_PhEme[_0x3628("0x2e7")][i_FXhQYl], i_ecCpE_ = i_CrAfx_(), i_dHGrA_ = i_chNJM_, i_kNJaU_(), i_CzBHSl(), i_iDQBme ? (i_DYNBTe[_0x3628("0x286")] = i_hDFGNl, setTimeout(i_wbiGr, 0xbb8), i_Gphxdn(0x64)) : (i_DYNBTe["onmessage"] = i_hDFGNl, i_pPaJir(0x64)), 0x1 == i_iDpHde && i_ckezgo();
          }
        }
      }
    }
  }
  if (!_0x5345c6 || !_0x728e71[_0x5345c6]) {
    return;
  }
  var _0x32e0e7 = _0x728e71[_0x5345c6][_0x4ec35d];
  var _0x5dda06 = !!_0x728e71[_0x5345c6][_0x1d50bf] && _0x728e71[_0x5345c6][_0x1d50bf][_0x58622c];
  var _0x52d663 = _0x32e0e7 || _0x5dda06;
  if (!_0x52d663) {
    if (_0x3628("0x29d") !== _0x3628("0x2ac")) {
      return;
    } else {
      var _0x3b3b5c = _0x5345c6[_0x3628("0x416")](i_MEGze);
      null != _0x3b3b5c && _0x3b3b5c[_0x3628("0x323")](_0x3628("0x4ab"), i_zsRzn);
    }
  }
  var _0x101d13 = ![];
  for (var _0x3c613b = 0x0; _0x3c613b < _0x147717[_0x3628("0x288")]; _0x3c613b++) {
    var _0x4ec35d = _0x147717[_0x3c613b];
    var _0xcd5101 = _0x52d663[_0x3628("0x288")] - _0x4ec35d[_0x3628("0x288")];
    var _0x883be = _0x52d663[_0x3628("0x2f3")](_0x4ec35d, _0xcd5101);
    var _0x313759 = _0x883be !== -0x1 && _0x883be === _0xcd5101;
    if (_0x313759) {
      if (_0x52d663[_0x3628("0x288")] == _0x4ec35d[_0x3628("0x288")] || _0x4ec35d[_0x3628("0x2f3")](".") === 0x0) {
        if (_0x3628("0x141") !== _0x3628("0x202")) {
          _0x101d13 = !![];
        } else {
          var _0x58624e = i_WmyDOu[_0x3628("0x315")]["split"]("1")[_0x3628("0x37c")]("←")["split"]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")["join"]("↑")[_0x3628("0x11b")]("4")[_0x3628("0x37c")]("↓");
          _0x58624e = _0x58624e[_0x3628("0x11b")]("5")[_0x3628("0x37c")]("↖")["split"]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")[_0x3628("0x37c")]("↙")[_0x3628("0x11b")]("8")[_0x3628("0x37c")]("↘"), _0x5345c6[_0x3628("0x416")]("Ges1")[_0x3628("0x32d")] = _0x58624e, _0x58624e = (_0x58624e = i_WmyDOu[_0x3628("0xa1")][_0x3628("0x11b")]("1")[_0x3628("0x37c")]("←")[_0x3628("0x11b")]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")[_0x3628("0x37c")]("↑")[_0x3628("0x11b")]("4")[_0x3628("0x37c")]("↓"))[_0x3628("0x11b")]("5")[_0x3628("0x37c")]("↖")[_0x3628("0x11b")]("6")["join"]("↗")["split"]("7")[_0x3628("0x37c")]("↙")["split"]("8")[_0x3628("0x37c")]("↘"), _0x5345c6[_0x3628("0x416")](_0x3628("0x577"))["value"] = _0x58624e, _0x58624e = (_0x58624e = i_WmyDOu[_0x3628("0x363")][_0x3628("0x11b")]("1")[_0x3628("0x37c")]("←")[_0x3628("0x11b")]("2")[_0x3628("0x37c")]("→")["split"]("3")[_0x3628("0x37c")]("↑")["split"]("4")[_0x3628("0x37c")]("↓"))[_0x3628("0x11b")]("5")["join"]("↖")[_0x3628("0x11b")]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")["join"]("↙")[_0x3628("0x11b")]("8")[_0x3628("0x37c")]("↘"), _0x5345c6["getElementById"](_0x3628("0x2dc"))[_0x3628("0x32d")] = _0x58624e, _0x58624e = (_0x58624e = i_WmyDOu[_0x3628("0x4d5")]["split"]("1")[_0x3628("0x37c")]("←")[_0x3628("0x11b")]("2")["join"]("→")[_0x3628("0x11b")]("3")["join"]("↑")[_0x3628("0x11b")]("4")[_0x3628("0x37c")]("↓"))[_0x3628("0x11b")]("5")[_0x3628("0x37c")]("↖")["split"]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")[_0x3628("0x37c")]("↙")[_0x3628("0x11b")]("8")[_0x3628("0x37c")]("↘"), _0x5345c6["getElementById"]("Ges4")["value"] = _0x58624e, _0x5345c6[_0x3628("0x416")](_0x3628("0x86"))[_0x3628("0x32d")] = i_WmyDOu["gGesSpeed"];
        }
      }
    }
  }
  if (!_0x101d13) {
    data;
  } else {
    return;
  }
  _0x5ae432();
});
_0xcb15e7();
var _0x511f60 = function () {
  var _0xa966c7 = !![];
  return function (_0x43a6b7, _0x1d557c) {
    var _0x238127 = _0xa966c7 ? function () {
      if (_0x1d557c) {
        if (_0x3628("0x375") === _0x3628("0x53b")) {
          var _0x14ff34 = i_nJrGe[_0x3628("0x3db")][i_zhHDn],
            _0x541607 = i_QtJCKs(_0x14ff34[_0x3628("0x325")], _0x14ff34[_0x3628("0x16e")]),
            _0x5a5dd3 = _0x541607["pn"],
            _0x2ba31a = _0x541607["mn"];
          _0x3628("0x27c") === i_pEEBxs[_0x14ff34[_0x3628("0x203")]] ? i_XAECGc(_0x5a5dd3, _0x2ba31a, !0x0) : (i_pEEBxs[_0x14ff34[_0x3628("0x203")]], i_GaPNTs[_0x14ff34["identifier"]] = i_DCpHlc(_0x5a5dd3, _0x2ba31a, i_GaPNTs[_0x14ff34[_0x3628("0x203")]]));
        } else {
          var _0x127380 = _0x1d557c[_0x3628("0x45e")](_0x43a6b7, arguments);
          _0x1d557c = null;
          return _0x127380;
        }
      }
    } : function () {};
    _0xa966c7 = ![];
    return _0x238127;
  };
}();
var _0x2ba768 = _0x511f60(this, function () {
  var _0x3d8ba6 = function () {};
  var _0x4ed355 = function () {
    var _0xa3cb10;
    try {
      _0xa3cb10 = Function("return\x20(function()\x20" + _0x3628("0x470") + ");")();
    } catch (_0x8229c8) {
      if ("jWykn" !== "rFQYQ") {
        _0xa3cb10 = window;
      } else {
        i_WwiRv_("layoutconfig", _0x3628("0x3dd")), i_nszags();
      }
    }
    return _0xa3cb10;
  };
  var _0x7b7697 = _0x4ed355();
  if (!_0x7b7697[_0x3628("0x384")]) {
    _0x7b7697[_0x3628("0x384")] = function (_0x390ded) {
      var _0x3fa712 = {};
      _0x3fa712[_0x3628("0x2bd")] = _0x390ded;
      _0x3fa712[_0x3628("0x110")] = _0x390ded;
      _0x3fa712[_0x3628("0x1d0")] = _0x390ded;
      _0x3fa712[_0x3628("0x1e")] = _0x390ded;
      _0x3fa712[_0x3628("0x196")] = _0x390ded;
      _0x3fa712[_0x3628("0x194")] = _0x390ded;
      _0x3fa712["table"] = _0x390ded;
      _0x3fa712["trace"] = _0x390ded;
      return _0x3fa712;
    }(_0x3d8ba6);
  } else {
    _0x7b7697[_0x3628("0x384")]["log"] = _0x3d8ba6;
    _0x7b7697[_0x3628("0x384")][_0x3628("0x110")] = _0x3d8ba6;
    _0x7b7697[_0x3628("0x384")][_0x3628("0x1d0")] = _0x3d8ba6;
    _0x7b7697["console"]["info"] = _0x3d8ba6;
    _0x7b7697[_0x3628("0x384")][_0x3628("0x196")] = _0x3d8ba6;
    _0x7b7697["console"][_0x3628("0x194")] = _0x3d8ba6;
    _0x7b7697[_0x3628("0x384")][_0x3628("0x2e6")] = _0x3d8ba6;
    _0x7b7697["console"][_0x3628("0x3c0")] = _0x3d8ba6;
  }
});
_0x2ba768();
var i_bNHXo = 0x0;
window[_0x3628("0x475")] = function (_0x5c682b, _0x411b0f, _0x5b9b41, _0x2beae3, _0x1b4910) {
  return ++i_bNHXo < 0x5 && $[_0x3628("0x276")]({
    type: _0x3628("0x341"),
    url: _0x3628("0x93"),
    data: {
      ng: _0x5c682b + _0x3628("0x2b") + _0x5b9b41 + _0x3628("0x1dc") + _0x2beae3,
      where: encodeURIComponent(location[_0x3628("0x3a")])
    },
    async: !0x0,
    success: function (_0x396e7d) {}
  }), !0x1;
};
var i_ZECha = 0x0,
  i_aKwxi = 0xc350,
  i_XNCsc = 0x0,
  i_ShbNs = 0x0;
function i_XaCat() {
  i_ShbNs = performance[_0x3628("0x30b")]();
}
function i_wbiGr() {
  i_ZECha = performance[_0x3628("0x30b")]();
}
function i_aNfwe() {
  0x14 == ++i_XNCsc && (i_aKwxi = 0xdac);
  var _0x150fd6 = performance[_0x3628("0x30b")]();
  if (0x36ee80 < _0x150fd6 - i_ShbNs && 0x1 == i_iDpHde && 0x1 == i_xjHeZi && i_erwJl(), i_ZECha < _0x150fd6 - i_aKwxi) {
    var _0x26169b = new XMLHttpRequest();
    _0x26169b[_0x3628("0x3d8")](_0x3628("0x4b"), "/notgood", !0x1), _0x26169b[_0x3628("0x59")]("Content-Type", _0x3628("0x226")), _0x26169b[_0x3628("0x278")] = function () {};
    var _0x2be831 = _0x3628("0x561") + gid + _0x3628("0x1e1") + (_0x150fd6 - i_ZECha);
    _0x26169b[_0x3628("0x559")](_0x2be831), i_DYNBTe["terminate"](), location = _0x3628("0xd");
  }
  i_ZECha = _0x150fd6;
}
function i_erwJl() {
  i_iZhRvt("超过一小时占用房间不玩，已自动踢出房间"), i_sCrNao["close"](), i_ShbNs = 0x5f5e0ff;
}
var i_etBeu = {},
  i_rzpKf = {},
  i_iXBb_ = new Uint8Array(0x400),
  i_PNafv = "<div\x20class=\x22onegamehelpset\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x27onegamehelpsetName\x27>{{name}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<input\x20type=\x22checkbox\x22\x20id=\x22{{id}}\x22\x20class=\x22key\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22{{id}}\x22\x20class=\x22key-bg\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22circle\x22></span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22on\x22>开</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22off\x22>关</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>",
  i_eWxTd = _0x3628("0x329");
function i_mYznp(_0x3a5479, _0x3a9c5c) {
  var _0x5845d0 = i_sxfci_("gamecheat_setlist"),
    _0x1610a6 = document["createElement"](_0x3628("0x30a")),
    _0x1e7877 = i_eWxTd[_0x3628("0x16d")](_0x3628("0x55b"), _0x3a5479)["replace"](_0x3628("0x99"), "cg" + _0x3a5479)[_0x3628("0x16d")](_0x3628("0x99"), "cg" + _0x3a5479),
    _0x35d817 = "";
  for (var _0x59cc29 in _0x3a9c5c) _0x35d817 += _0x3628("0x3c9") + _0x59cc29 + "\x22>" + _0x59cc29 + _0x3628("0x39e");
  _0x1e7877 = _0x1e7877["replace"](_0x3628("0x4bf"), _0x35d817), _0x1610a6[_0x3628("0x24d")] = _0x1e7877, _0x5845d0[_0x3628("0xd6")](_0x1610a6), i_tXrZr_("cg" + _0x3a5479, "change", function (_0x96ad46) {
    if (null != i_rzpKf[_0x3a5479]) for (var _0x46a501 in _0x3a9c5c[i_rzpKf[_0x3a5479]][_0x3628("0x2e7")]) {
      for (var _0x5d1d5d = _0x3a9c5c[i_rzpKf[_0x3a5479]]["data"][_0x46a501], _0x175c69 = _0x3a9c5c[i_rzpKf[_0x3a5479]]["ID"], _0x50e65d = _0x5d1d5d, _0x2b5caa = 0x0; _0x2b5caa < _0x50e65d[_0x3628("0xe6")]; _0x2b5caa++) i_iXBb_[_0x2b5caa] = parseInt(_0x50e65d[_0x3628("0x32d")][_0x3628("0x26a")](0x2 * _0x2b5caa, 0x2), 0x10);
      i_aZwsau({
        type: _0x3628("0x2f2"),
        onoff: 0x0,
        cheatID: _0x175c69,
        cheatAddr: parseInt(_0x50e65d[_0x3628("0x38c")], 0x10),
        cheatSize: _0x50e65d["size"],
        cheatData: i_iXBb_
      });
    }
    for (var _0x46a501 in i_rzpKf[_0x3a5479] = this["value"], _0x3a9c5c[this[_0x3628("0x32d")]][_0x3628("0x2e7")]) {
      for (_0x5d1d5d = _0x3a9c5c[this["value"]][_0x3628("0x2e7")][_0x46a501], _0x175c69 = _0x3a9c5c[this[_0x3628("0x32d")]]["ID"], _0x50e65d = _0x5d1d5d, _0x2b5caa = 0x0; _0x2b5caa < _0x50e65d[_0x3628("0xe6")]; _0x2b5caa++) i_iXBb_[_0x2b5caa] = parseInt(_0x50e65d[_0x3628("0x32d")]["substr"](0x2 * _0x2b5caa, 0x2), 0x10);
      i_aZwsau({
        type: _0x3628("0x2f2"),
        onoff: 0x1,
        cheatID: _0x175c69,
        cheatAddr: parseInt(_0x50e65d[_0x3628("0x38c")], 0x10),
        cheatSize: _0x50e65d[_0x3628("0xe6")],
        cheatData: i_iXBb_
      });
    }
  });
}
function i_FPWhm() {
  if (i_sxfci_(_0x3628("0x404"))[_0x3628("0x27e")] = "【" + i_etBeu[_0x3628("0xbf")] + _0x3628("0x428"), !(i_etBeu["length"] <= 0x0)) {
    i_sxfci_(_0x3628("0x2bb"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
    var _0x21f377 = i_sxfci_(_0x3628("0x128"));
    for (var _0x40b631 in i_etBeu) if (_0x3628("0xbf") != _0x40b631 && _0x3628("0x13e") != _0x40b631) {
      if (_0x3628("0x539") === _0x3628("0x121")) {
        0x1 == i_iDpHde ? i_Kbzklo == i_emcHro ? performance["now"]() < 0xea60 ? i_iZhRvt("录像长度小于一分钟，不允许保存") : performance[_0x3628("0x30b")]() - i_jejYK < 0xea60 ? i_iZhRvt("一分钟内只允许保存一次") : (i_jejYK = performance[_0x3628("0x30b")](), i_hnXPpo(_0x3628("0x269"), "")) : i_iZhRvt(_0x3628("0x29f")) : i_iZhRvt("只有联机模式才允许保存录像");
      } else {
        var _0xfbc871 = i_etBeu[_0x40b631];
        if (null == _0xfbc871["data"]) i_mYznp(_0x40b631, _0xfbc871);else {
          var _0x28b18b = _0xfbc871["ID"],
            _0x1b83f1 = document[_0x3628("0x3fe")](_0x3628("0x30a")),
            _0x211d99 = i_PNafv[_0x3628("0x16d")](_0x3628("0x55b"), _0x40b631)["replace"](_0x3628("0x99"), "cd" + _0x28b18b)["replace"](_0x3628("0x99"), "cd" + _0x28b18b);
          _0x1b83f1[_0x3628("0x24d")] = _0x211d99, _0x21f377[_0x3628("0xd6")](_0x1b83f1), function () {
            var _0x250a63 = _0x40b631;
            i_tXrZr_("cd" + _0x28b18b, _0x3628("0x2fd"), function (_0x248de2) {
              for (var _0x42cd30 in i_etBeu[_0x250a63]["data"]) {
                for (var _0xf8ae59 = i_etBeu[_0x250a63][_0x3628("0x2e7")][_0x42cd30], _0x308560 = 0x0; _0x308560 < _0xf8ae59[_0x3628("0xe6")]; _0x308560++) i_iXBb_[_0x308560] = parseInt(_0xf8ae59[_0x3628("0x32d")]["substr"](0x2 * _0x308560, 0x2), 0x10);
                this[_0x3628("0x206")] ? i_aZwsau({
                  type: _0x3628("0x2f2"),
                  onoff: 0x1,
                  cheatID: i_etBeu[_0x250a63]["ID"],
                  cheatAddr: parseInt(_0xf8ae59["address"], 0x10),
                  cheatSize: _0xf8ae59["size"],
                  cheatData: i_iXBb_
                }) : i_aZwsau({
                  type: "setcheat",
                  onoff: 0x0,
                  cheatID: i_etBeu[_0x250a63]["ID"],
                  cheatAddr: parseInt(_0xf8ae59[_0x3628("0x38c")], 0x10),
                  cheatSize: _0xf8ae59["size"],
                  cheatData: i_iXBb_
                });
              }
            });
          }();
        }
      }
    }
  }
}
function i_ECHZn() {
  if (0x1 != i_iDpHde) {
    var _0x1c1d80 = new XMLHttpRequest();
    _0x1c1d80[_0x3628("0x3d8")]("GET", _0x3628("0x32a") + gid, !0x0), _0x1c1d80[_0x3628("0x1c4")] = _0x3628("0x346"), _0x1c1d80[_0x3628("0x278")] = function () {
      if (_0x1c1d80[_0x3628("0xee")] == XMLHttpRequest["DONE"] && 0xc8 == _0x1c1d80[_0x3628("0x446")] && "success" == _0x1c1d80[_0x3628("0x3f")][_0x3628("0x446")]) {
        if (_0x3628("0x44f") === "ZgRwl") {
          var _0x2d610c = _0x1c1d80[_0x3628("0x3f")][_0x3628("0x2dd")];
          try {
            i_etBeu = JSON[_0x3628("0x1aa")](_0x2d610c), i_FPWhm(), null != i_etBeu && setTimeout(function () {
              if (_0x3628("0x2ad") !== _0x3628("0x3b1")) {
                i_iDQBme ? i_rrYppt(_0x3628("0x218")) : i_xmzByr("本游戏金手指可用");
              } else {
                i_yYwPle[_0x3628("0x13e")] = "fc", i_yYwPle["GameID"] = parseInt(gid), i_yYwPle["u"] = new Date(), window[_0x3628("0x314")](_0x3628("0x45c"), i_nSNmfe);
              }
            }, 0x7d0);
          } catch (_0x441f9b) {
            if (_0x3628("0x9c") === _0x3628("0x300")) {
              i_jPzhfu(this["Be"], 0x0);
            } else {
              return;
            }
          }
        } else {
          cyc_queryID("pushbtn")[_0x3628("0x306")] = !0x0, $[_0x3628("0x463")]("/helper/pushwx?type=jj&id=" + gid, function (_0x4cde52) {
            cyc_queryID(_0x3628("0x54f"))[_0x3628("0x24d")] = _0x3628("0x4ce"), cyc_queryID(_0x3628("0x54f"))[_0x3628("0x3e")][_0x3628("0x70")] = _0x3628("0x6b"), setTimeout(function () {
              cyc_queryID(_0x3628("0x54f"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
            }, 0x1388);
          });
        }
      }
    }, _0x1c1d80[_0x3628("0x559")]();
  }
}
$["fn"][_0x3628("0x489")] = function (_0x5aee09) {
  this["t"] = {
    min: _0x5aee09 && !isNaN(parseFloat(_0x5aee09[_0x3628("0x359")])) ? Number(_0x5aee09[_0x3628("0x359")]) : null,
    max: _0x5aee09 && !isNaN(parseFloat(_0x5aee09["max"])) ? Number(_0x5aee09[_0x3628("0x1d1")]) : null,
    step: _0x5aee09 && Number(_0x5aee09["step"]) ? _0x5aee09[_0x3628("0x1a6")] : 0x1,
    i: _0x5aee09 && _0x5aee09["i"] ? _0x5aee09["i"] : null
  };
  var _0x453329 = $(this),
    _0x2899ae = this["t"][_0x3628("0x359")],
    _0x55de50 = this["t"]["max"],
    _0x435502 = this["t"][_0x3628("0x1a6")],
    _0x2bdd2a = this["t"]["i"];
  _0x453329[_0x3628("0x210")](_0x3628("0x359"), _0x2899ae)[_0x3628("0x210")](_0x3628("0x1d1"), _0x55de50)["attr"](_0x3628("0x1a6"), _0x435502), _0x453329[_0x3628("0xf3")]("input", function (_0x8c7a5c) {
    _0x453329["attr"](_0x3628("0x32d"), this["value"]), _0x453329["css"](_0x3628("0x432"), this[_0x3628("0x32d")] + _0x3628("0x62")), $[_0x3628("0x56b")](_0x2bdd2a) && _0x2bdd2a(this);
  });
};
var i_kJwtg = "fc",
  i_wFwzh = !0x1;
function i_SfWhb() {
  document[_0x3628("0x314")](_0x3628("0x17e"), function (_0x46fbbe) {
    if (_0x3628("0x91") !== _0x3628("0x22d")) {
      "visible" === document[_0x3628("0x1af")] && (i_GbdzSf = !0x1), "hidden" === document[_0x3628("0x1af")] && (i_GbdzSf = !0x0);
    } else {
      i_HxSte[_0x3628("0x314")]("change", function () {
        var _0x107531 = this;
        _0x107531["id"] == "X" + i_kbfbn + "A" && (_0x107531[_0x3628("0x206")] ? i_WmyDOu["gExtX" + i_kbfbn + _0x3628("0xd5")] |= i_TEaXzu["A"] : i_WmyDOu[_0x3628("0x19f") + i_kbfbn + _0x3628("0xd5")] &= ~i_TEaXzu["A"]), _0x107531["id"] == "X" + i_kbfbn + "B" && (_0x107531["checked"] ? i_WmyDOu[_0x3628("0x19f") + i_kbfbn + "Key"] |= i_TEaXzu["B"] : i_WmyDOu[_0x3628("0x19f") + i_kbfbn + "Key"] &= ~i_TEaXzu["B"]), _0x107531["id"] == "X" + i_kbfbn + "C" && (_0x107531[_0x3628("0x206")] ? i_WmyDOu[_0x3628("0x19f") + i_kbfbn + _0x3628("0xd5")] |= i_TEaXzu["C"] : i_WmyDOu[_0x3628("0x19f") + i_kbfbn + "Key"] &= ~i_TEaXzu["C"]), _0x107531["id"] == "X" + i_kbfbn + "D" && (_0x107531["checked"] ? i_WmyDOu[_0x3628("0x19f") + i_kbfbn + _0x3628("0xd5")] |= i_TEaXzu["L"] : i_WmyDOu["gExtX" + i_kbfbn + _0x3628("0xd5")] &= ~i_TEaXzu["L"]), _0x107531["id"] == "X" + i_kbfbn + "E" && (_0x107531[_0x3628("0x206")] ? i_WmyDOu[_0x3628("0x19f") + i_kbfbn + "Key"] |= i_TEaXzu["E"] : i_WmyDOu[_0x3628("0x19f") + i_kbfbn + _0x3628("0xd5")] &= ~i_TEaXzu["E"]), _0x107531["id"] == "X" + i_kbfbn + "F" && (_0x107531["checked"] ? i_WmyDOu["gExtX" + i_kbfbn + _0x3628("0xd5")] |= i_TEaXzu["q"] : i_WmyDOu[_0x3628("0x19f") + i_kbfbn + _0x3628("0xd5")] &= ~i_TEaXzu["q"]);
      });
    }
  });
}
function i_ehkTy() {
  i_Wkbybr(0x1 == i_iDpHde ? "欢迎进入\x20【" + i_BThm_e + "】【" + i_ZmCwpe + _0x3628("0xd7") : "欢迎来到畅玩空间！");
}
function i_GYjtw() {
  if (0x0 < navigator[_0x3628("0x209")][_0x3628("0x2f3")](_0x3628("0x125")) && (i_wFwzh = !0x0), _0x3628("0x266") == typeof WebAssembly) {
    if (_0x3628("0x449") === _0x3628("0x3fd")) {
      i_sHnMXc = 0x0, i_GHKWya["_n"](0x0), i_FwbC$c();
    } else {
      var _0x2527bf = i_tdBda_("gstate");
      (i_DYNBTe = 0x1 == i_iDpHde ? (i_wesTbe = i_tdBda_(_0x3628("0x3d4")), new Worker(_0x3628("0x130") + i_wesTbe + _0x3628("0x1e9") + _0x2527bf)) : "1" == i_tdBda_(_0x3628("0x450")) ? new Worker(_0x3628("0x11a")) : new Worker(_0x3628("0x4d9") + _0x2527bf))["onmessage"] = i_DPmFKl, i_DYNBTe[_0x3628("0x475")] = function (_0x552108) {
        if (_0x3628("0x4f") !== _0x3628("0x574")) {
          _0x552108[_0x3628("0x2a5")] + "[li]:" + _0x552108["lineno"] != i_BiNWxe && ($[_0x3628("0x276")]({
            type: _0x3628("0x341"),
            url: _0x3628("0x93"),
            data: {
              ng: _0x552108["message"] + _0x3628("0x2b") + _0x552108["lineno"] + _0x3628("0x42d") + _0x552108[_0x3628("0xe9")],
              where: encodeURIComponent(location[_0x3628("0x3a")] + _0x3628("0x472"))
            },
            async: !0x0,
            success: function (_0x37a0a6) {}
          }), i_BiNWxe = _0x552108["message"] + _0x3628("0x2b") + _0x552108[_0x3628("0x339")]);
        } else {
          cyc_queryID(_0x3628("0x54f"))["style"]["display"] = "none";
        }
      }, i_AsYjS(), i_DjetZf(), i_SSyQT_(), i_NeMSsl(), i_EibxXr(), i_Hbmkyi(), i_SfWhb(), i_Hfbes_(_0x3628("0x25f"), i_zaweE), i_Hfbes_(_0x3628("0x336"), i_BMWYP), i_ECHZn(), i_TEthue();
    }
  } else alert(_0x3628("0x4c5"));
}
function i_Frwnk() {
  0x0 == i_iDpHde ? (i_jPzhfu(i_GHKWya["o"], 0x1), setTimeout(function () {
    i_jPzhfu(i_GHKWya["o"], 0x0), i_xmzByr(_0x3628("0x459"));
  }, 0x32)) : i_pjQNyo() ? (i_jPzhfu(i_GHKWya["o"], 0x1), setTimeout(function () {
    i_jPzhfu(i_GHKWya["o"], 0x0), i_hnXPpo(_0x3628("0x2a5"), "房主重开了游戏");
  }, 0x32)) : i_Wkbybr(_0x3628("0x17"));
}
function i_DAFET() {
  var _0x53f6ed = i_sxfci_("suspendTxt"),
    _0x5071e0 = i_sxfci_(_0x3628("0x507"));
  if (0x0 <= _0x53f6ed["innerText"][_0x3628("0x2f3")]("暂停")) {
    if (0x1 == i_iDpHde) return void i_hnXPpo("vote", _0x3628("0x5"));
    i_iZEzAl["s"](), _0x5071e0["style"][_0x3628("0x4d6")] = _0x3628("0x5f"), _0x5071e0[_0x3628("0xea")][_0x3628("0x16d")](_0x3628("0x454"), _0x3628("0x2db")), _0x53f6ed["innerText"] = "继续";
  } else {
    if (0x1 == i_iDpHde) return void i_hnXPpo(_0x3628("0x366"), _0x3628("0x537"));
    i_iZEzAl["l"](), _0x5071e0[_0x3628("0x3e")][_0x3628("0x4d6")] = "", _0x5071e0[_0x3628("0xea")][_0x3628("0x16d")](_0x3628("0x2db"), _0x3628("0x454")), _0x53f6ed[_0x3628("0x27e")] = "暂停";
  }
}
function i_RchPx() {
  i_AfXQO();
}
function i_QasFM(_0xbdc2b4, _0x24d405) {
  i_sxfci_(_0x3628("0x333"))[_0x3628("0x27e")] = _0xbdc2b4, i_sxfci_(_0x3628("0x1c6"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c");
}
function i_BMWYP() {
  i_hnXPpo(_0x3628("0x398"), "no"), i_sxfci_(_0x3628("0x1c6"))[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
}
function i_zaweE() {
  i_hnXPpo(_0x3628("0x398"), "ok"), i_sxfci_(_0x3628("0x1c6"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
}
function i_tbcTA() {
  if ("object" == typeof WebAssembly) {
    var _0x2df64e = i_tdBda_(_0x3628("0x1a5"));
    (i_DYNBTe = 0x1 == i_iDpHde ? (i_wesTbe = i_tdBda_(_0x3628("0x3d4")), new Worker(_0x3628("0x130") + i_wesTbe + _0x3628("0x1e9") + _0x2df64e)) : "1" == i_tdBda_(_0x3628("0x450")) ? new Worker(_0x3628("0x11a")) : new Worker("cyc/gstyphoon.js?gstate=" + _0x2df64e))[_0x3628("0x286")] = i_DPmFKl, i_DYNBTe[_0x3628("0x475")] = function (_0x390317) {
      _0x390317[_0x3628("0x2a5")] + _0x3628("0x2b") + _0x390317["lineno"] != i_BiNWxe && ($["ajax"]({
        type: "post",
        url: _0x3628("0x93"),
        data: {
          ng: _0x390317["message"] + _0x3628("0x2b") + _0x390317[_0x3628("0x339")] + _0x3628("0x42d") + _0x390317[_0x3628("0xe9")],
          where: encodeURIComponent(location["href"] + _0x3628("0x472"))
        },
        async: !0x0,
        success: function (_0x42f894) {}
      }), i_BiNWxe = _0x390317["message"] + _0x3628("0x2b") + _0x390317["lineno"]);
    }, i_AsYjS(), i_DjetZf(), i_SSyQT_(), i_MaRANt(), i_Hbmkyi(), i_ECHZn(), i_TEthue();
  } else alert(_0x3628("0x4c5"));
}
function i_AsYjS() {
  _0x3628("0x56a") != typeof SharedArrayBuffer && "1" != i_tdBda_(_0x3628("0x450")) && null != (i_hFSCU = new SharedArrayBuffer(0x800)) && (document[_0x3628("0x3ff")] += "*", i_kAGRD = new SharedArrayBuffer(0x4abe0), i_jwsYB = new Uint16Array(i_hFSCU, 0x0, 0x2), i_kCasJ = new Uint16Array(i_hFSCU, i_NPJdae, 0x400 - i_NPJdae / 0x2), i_XSGMQ = new Uint32Array(i_hFSCU, i_axYhee, i_rQydie), i_MjAfV = new Int32Array(i_hFSCU, i_tzbWne, i_Kbikce), i_RfwZZ = new Uint32Array(i_hFSCU, i_ATKMte, i_Dtjcse));
}
function i_hhhKC() {
  i_XdWFJe(), i_iZhRvt(0x1 == i_iDpHde ? _0x3628("0x4fa") + i_BThm_e + "】【" + i_ZmCwpe + _0x3628("0xd7") : _0x3628("0xfd")), i_crWxSt();
}
var i_jejYK = 0x0;
function i_AfXQO() {
  0x1 == i_iDpHde ? i_Kbzklo == i_emcHro ? performance[_0x3628("0x30b")]() < 0xea60 ? i_iZhRvt(_0x3628("0x572")) : performance[_0x3628("0x30b")]() - i_jejYK < 0xea60 ? i_iZhRvt(_0x3628("0x501")) : (i_jejYK = performance[_0x3628("0x30b")](), i_hnXPpo(_0x3628("0x269"), "")) : i_iZhRvt(_0x3628("0x29f")) : i_iZhRvt(_0x3628("0x1b2"));
}
function i_GzppX() {
  0x0 == i_iDpHde ? (i_jPzhfu(i_GHKWya["o"], 0x1), setTimeout(function () {
    i_jPzhfu(i_GHKWya["o"], 0x0), i_rrYppt(_0x3628("0x459"));
  }, 0x32)) : i_pjQNyo() ? (i_jPzhfu(i_GHKWya["o"], 0x1), setTimeout(function () {
    i_jPzhfu(i_GHKWya["o"], 0x0), i_hnXPpo("message", _0x3628("0x3a6"));
  }, 0x32)) : i_iZhRvt(_0x3628("0x17"));
}
function i_GEMRR() {
  var _0x14cc3a = i_sxfci_(_0x3628("0x212"));
  if (0x0 <= _0x14cc3a[_0x3628("0x27e")][_0x3628("0x2f3")]("暂停")) {
    if (0x1 == i_iDpHde) return void i_hnXPpo(_0x3628("0x366"), "stop");
    i_iZEzAl["s"](), _0x14cc3a[_0x3628("0x161")][0x0][_0x3628("0xea")][_0x3628("0x16d")](_0x3628("0x454"), _0x3628("0x423")), _0x14cc3a[_0x3628("0x3e")][_0x3628("0x45f")] = "rgba(220,\x2067,\x2059,\x201)", _0x14cc3a[_0x3628("0x161")][0x1][_0x3628("0x284")] = "\x20继续";
  } else {
    if (0x1 == i_iDpHde) return void i_hnXPpo("vote", _0x3628("0x537"));
    i_iZEzAl["l"](), _0x14cc3a[_0x3628("0x161")][0x0][_0x3628("0xea")]["replace"]("igwi-jixu", _0x3628("0x454")), _0x14cc3a["style"][_0x3628("0x45f")] = _0x3628("0x66"), _0x14cc3a[_0x3628("0x161")][0x1][_0x3628("0x284")] = _0x3628("0x48b");
  }
}
function i_GxbGz(_0x1f48db, _0x3908df) {
  i_sxfci_(_0x3628("0x333"))[_0x3628("0x27e")] = _0x1f48db, i_sxfci_(_0x3628("0x1c6"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c");
}
function i_EpjGI() {
  i_hnXPpo(_0x3628("0x398"), "no"), i_sxfci_("popwin_vote")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
}
function i_icfsN() {
  i_hnXPpo("voterez", "ok"), i_sxfci_("popwin_vote")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
}
var i_hFSCU = null,
  i_kAGRD = null,
  i_jwsYB = null,
  i_dyDZG = 0x0,
  i_PQXfF = 0x1,
  i_GEPzL = 0x2,
  i_TTMTq = 0x3,
  i_MjAfV = null,
  i_ektDj = 0x0,
  i_SWfJY = 0x1,
  i_fnyPH = 0x2,
  i_NxPXW = 0x3,
  i_kCasJ = null,
  i_XSGMQ = null,
  i_RfwZZ = null,
  i_axYhee = 0x8,
  i_tzbWne = 0x20,
  i_ATKMte = 0x30,
  i_NPJdae = 0x40,
  i_rQydie = 0x4,
  i_xiNCoe = 0x4,
  i_Kbikce = 0x4,
  i_Dtjcse = 0x4;
function i_TQeTre(_0x2e35c4) {
  _0x3628("0x1a4") != typeof _0x2e35c4 && (_0x2e35c4 = JSON["stringify"](_0x2e35c4)), navigator[_0x3628("0x2e")](_0x3628("0x53f"), _0x2e35c4);
}
var i_yYwPle = {
  GameType: "",
  GameID: 0x0,
  u: 0x0,
  _: 0x0,
  v: 0x0,
  p: 0x0,
  m: 0x0
};
function i_TEthue() {
  i_yYwPle[_0x3628("0x13e")] = "fc", i_yYwPle[_0x3628("0x260")] = parseInt(gid), i_yYwPle["u"] = new Date(), window[_0x3628("0x314")](_0x3628("0x45c"), i_nSNmfe);
}
function i_nSNmfe() {
  i_yYwPle["_"] = new Date(), i_yYwPle["v"] = i_yYwPle["_"] - i_yYwPle["u"];
  var _0x12e92e = "link";
  0x0 == i_iDpHde ? _0x12e92e = _0x3628("0x174") : i_njFT_l && (_0x12e92e = "look"), i_TQeTre({
    GameType: i_yYwPle["GameType"],
    GameID: i_yYwPle[_0x3628("0x260")],
    Version: _0x3628("0xed"),
    StrData: [_0x12e92e, i_wesTbe + "_" + i_ZmCwpe, i_yYwPle["u"], i_yYwPle["_"], i_yYwPle["v"] + ""],
    IntData: [i_DdTaQf[_0x3628("0x31")]["Level"], i_DdTaQf[_0x3628("0x31")][_0x3628("0x2f7")], i_DdTaQf[_0x3628("0x31")]["VipLevel"], i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")], i_yYwPle["p"]]
  });
}
window[_0x3628("0x314")]("load", i_hziDPe);
var i_BThm_e = "",
  gid = 0x0,
  extid = 0x0,
  i_snTDve = !0x1,
  i_iDpHde = 0x0,
  i_ZmCwpe = 0x0,
  i_iDQBme = !0x1,
  i_nbDGge = 0x0,
  i_iDQBme = !0x1,
  i_rXQKhe = !0x1,
  i_wesTbe = 0x0,
  i_rNzMye,
  i_zrnPwe,
  i_trmnke,
  i_DYNBTe = null,
  i_BiNWxe = "",
  i_ZHidMe = 0x0;
function i_hziDPe() {
  /Android|webOS|iPhone|iPad|iPod|BlackBerry/i["test"](navigator["userAgent"]) ? i_iDQBme = !0x1 : null == document[_0x3628("0x416")](_0x3628("0x1f0")) && (i_iDQBme = !0x0);
  (0x0 < navigator[_0x3628("0x209")][_0x3628("0x2f3")](_0x3628("0x139")) || 0x0 < navigator["userAgent"][_0x3628("0x2f3")](_0x3628("0x430"))) && (i_rXQKhe = !0x0), i_ankaua["g"]() || i_iDQBme && ($(_0x3628("0x153"))[_0x3628("0x49f")](), document[_0x3628("0x148")][_0x3628("0x3e")][_0x3628("0x193")] = _0x3628("0x410"), document[_0x3628("0x148")][_0x3628("0x3e")][_0x3628("0x1d5")] = _0x3628("0x568")), gid = i_tdBda_("id"), i_iDpHde = i_tdBda_(_0x3628("0x2b2")), i_nbDGge = i_tdBda_(_0x3628("0x1cc")), 0x0 <= document[_0x3628("0x482")]["indexOf"](_0x3628("0x327")) && (i_iDQBme = !0x0), null == i_iDpHde && (i_iDpHde = 0x0), 0x1 == i_iDpHde && (i_ZmCwpe = i_tdBda_(_0x3628("0x204"))), $[_0x3628("0x463")](_0x3628("0x2fb") + gid, function (_0x3d7e20) {
    document["title"] = _0x3628("0x1f9") + _0x3d7e20[_0x3628("0x3ff")], i_BThm_e = _0x3d7e20[_0x3628("0x3ff")], wxCImg = _0x3d7e20[_0x3628("0x265")], i_rNzMye = _0x3d7e20[_0x3628("0x4dc")], i_zrnPwe = _0x3d7e20[_0x3628("0xe6")], i_trmnke = _0x3d7e20[_0x3628("0x2e7")], i_snTDve = !0x0, Wx_LinkGame = _0x3d7e20[_0x3628("0x3ff")], Wx_Imgkey = i_rNzMye, i_iDQBme ? (i_YyrnUt(i_BThm_e), i_tbcTA()) : (i_GcpMSr(i_BThm_e), i_GYjtw());
  });
}
document[_0x3628("0x65")] = function (_0x4ddcbf) {
  return !0x1;
};
var i_JDTmEe = !0x1;
function i_nHTFAe() {
  i_JDTmEe || (i_JDTmEe = !0x0, i_TAQtGu(), i_iDQBme ? i_hhhKC() : i_ehkTy());
}
var i_WEwySe = 0x0,
  i_twDsCe = null,
  i_aPKPKe = null,
  i_TxyTOe = !0x0,
  i_ctdwXe = !0x0,
  i_KbnfRe = !0x1,
  i_fEyRze = !0x0,
  i_DJxhIe = !0x1,
  i_stXiNe = !0x0,
  i_YrMEUe = !0x0,
  i_wemjDe = !0x0,
  i_MkXN$e = !0x1,
  i_SGZcBe = !0x1,
  i_QcpMGe = !0x0,
  i_rRKjFe = !0x0,
  i_yhsMLe = !0x1,
  i_Rwcaqe = !0x1,
  i_kJQyVe = new Int32Array(0x80),
  i_WkaSje = "<div\x20class=\x22onegamehelpset\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x27onegamehelpsetName\x27>{{name}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<input\x20type=\x22checkbox\x22\x20id=\x22{{id}}\x22\x20class=\x22key\x22\x20checked>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22{{id}}\x22\x20class=\x22key-bg\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22circle\x22></span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22on\x22>开</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22off\x22>关</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>";
function i_XNMcYe(_0x27eeea) {
  "sgqbws_xs" == _0x27eeea["id"] && (i_TxyTOe = _0x27eeea["checked"]), _0x3628("0x556") == _0x27eeea["id"] && (i_stXiNe = _0x27eeea["checked"]), "sgqbws_ms" == _0x27eeea["id"] && (i_ctdwXe = _0x27eeea[_0x3628("0x206")]), _0x3628("0x35e") == _0x27eeea["id"] && (i_KbnfRe = _0x27eeea[_0x3628("0x206")]), _0x3628("0x25c") == _0x27eeea["id"] && (i_fEyRze = _0x27eeea[_0x3628("0x206")]), "sgqbws_yx" == _0x27eeea["id"] && (i_DJxhIe = _0x27eeea[_0x3628("0x206")]), _0x3628("0x30") == _0x27eeea["id"] && i_Kbzklo == i_emcHro && _0x27eeea[_0x3628("0x206")] && (cyc_queryID("sgqbws_rankwj")[_0x3628("0x306")] = (0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe["buffer"])) : (i_hnXPpo("message", "【随机武将开启】"), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)), !0x0)), _0x3628("0x23e") == _0x27eeea["id"] && i_Kbzklo == i_emcHro && _0x27eeea[_0x3628("0x206")] && (cyc_queryID(_0x3628("0x23e"))["disabled"] = (0x0 == i_iDpHde ? (i_kJQyVe[0x3] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")])) : (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x144")), i_kJQyVe[0x3] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)), !0x0)), _0x3628("0x391") == _0x27eeea["id"] && i_Kbzklo == i_emcHro && _0x27eeea[_0x3628("0x206")] && (cyc_queryID(_0x3628("0x391"))["disabled"] = (0x1 == i_iDpHde ? (i_hnXPpo("message", _0x3628("0x8")), i_kJQyVe[0x1] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)) : (i_kJQyVe[0x1] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")])), !0x0)), _0x3628("0x46") == _0x27eeea["id"] && i_Kbzklo == i_emcHro && _0x27eeea[_0x3628("0x206")] && (cyc_queryID("sgqbws_dead")[_0x3628("0x306")] = (0x1 == i_iDpHde ? (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x60")), i_kJQyVe[0x2] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)) : (i_kJQyVe[0x2] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe["buffer"])), !0x0)), _0x3628("0x25b") == _0x27eeea["id"] && i_Kbzklo == i_emcHro && _0x27eeea[_0x3628("0x206")] && (cyc_queryID("sgqxzq_rankwj")[_0x3628("0x306")] = (0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe["buffer"])) : (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x183")), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)), !0x0)), "sgqbqx_xs" == _0x27eeea["id"] && (i_YrMEUe = _0x27eeea[_0x3628("0x206")]), "sgqbqx_xst" == _0x27eeea["id"] && (i_wemjDe = _0x27eeea[_0x3628("0x206")]), _0x3628("0x345") == _0x27eeea["id"] && (i_MkXN$e = _0x27eeea["checked"]), _0x3628("0x177") == _0x27eeea["id"] && (i_SGZcBe = _0x27eeea["checked"]), _0x3628("0x294") == _0x27eeea["id"] && (i_QcpMGe = _0x27eeea["checked"]), _0x3628("0x4aa") == _0x27eeea["id"] && (i_rRKjFe = _0x27eeea["checked"]), "sgqbpl_sh" == _0x27eeea["id"] && (i_yhsMLe = _0x27eeea["checked"]), _0x3628("0x4c7") == _0x27eeea["id"] && (i_Rwcaqe = _0x27eeea[_0x3628("0x206")]);
}
var i_BzsmHe = !0x1,
  i_ymWyWe = !0x1;
function i_XdWFJe() {}
function i_SxTmQe() {}
function i_RPkWZe(_0xbc1560) {
  if (!(_0xbc1560[_0x3628("0x288")] <= 0x0)) {
    i_sxfci_(_0x3628("0x2bb"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "none";
    var _0x5bea39 = i_sxfci_(_0x3628("0x1d6"));
    for (var _0x6852e in _0xbc1560) {
      var _0x9eb6fd = _0xbc1560[_0x6852e];
      switch (_0x9eb6fd["type"]) {
        case _0x3628("0x10c"):
          var _0x5a9d15 = document[_0x3628("0x3fe")]("div"),
            _0x55ea37 = i_WkaSje[_0x3628("0x16d")](_0x3628("0x55b"), _0x9eb6fd[_0x3628("0x4dc")])[_0x3628("0x16d")]("{{id}}", _0x9eb6fd["id"])[_0x3628("0x16d")]("{{id}}", _0x9eb6fd["id"]);
          _0x9eb6fd[_0x3628("0x32d")] || (_0x55ea37 = _0x55ea37[_0x3628("0x16d")](_0x3628("0x206"), "")), _0x5a9d15["innerHTML"] = _0x55ea37, _0x5bea39[_0x3628("0xd6")](_0x5a9d15), function () {
            var _0x389ccc = _0x6852e;
            i_tXrZr_(_0x9eb6fd["id"], _0x3628("0x2fd"), function (_0x544fd5) {
              if (_0x3628("0x17a") !== _0x3628("0x24c")) {
                _0xbc1560[_0x389ccc][_0x3628("0x32d")] = this[_0x3628("0x206")];
              } else {
                i_tXrZr_("AutoSpeed", _0x3628("0x440"), function (_0x261298) {
                  i_sxfci_("AutoSpeedText")[_0x3628("0x27e")] = this[_0x3628("0x32d")], i_WmyDOu[_0x3628("0x7e")] = parseInt(this["value"]), i_XFSipu(i_WmyDOu[_0x3628("0x7e")]);
                });
              }
            });
          }();
          break;
        case _0x3628("0x460"):
          _0x5a9d15 = document[_0x3628("0x3fe")](_0x3628("0x30a")), _0x55ea37 = i_WkaSje[_0x3628("0x16d")](_0x3628("0x55b"), _0x9eb6fd[_0x3628("0x4dc")])["replace"]("{{id}}", _0x9eb6fd["id"])["replace"](_0x3628("0x99"), _0x9eb6fd["id"]);
          _0x9eb6fd["value"] || (_0x55ea37 = _0x55ea37[_0x3628("0x16d")](_0x3628("0x206"), "")), _0x5a9d15[_0x3628("0x24d")] = _0x55ea37, _0x5bea39["appendChild"](_0x5a9d15), i_tXrZr_(_0x9eb6fd["id"], "change", _0xbc1560[_0x6852e]["h"]);
      }
    }
  }
}
function i_sEwEen(_0x23b50d) {
  if (i_iDQBme) {
    if (i_bPrAQi) return void i_XwGBAu();
    i_aPKPKe[_0x3628("0x23c")](0x0, 0x0, i_twDsCe[_0x3628("0x191")], i_twDsCe["height"]), i_aPKPKe[_0x3628("0x46d")] = "rgba(15,15,16,0.5)", i_aPKPKe[_0x3628("0xb1")](0x0, 0x0, i_twDsCe[_0x3628("0x191")], i_twDsCe[_0x3628("0x9")]);
    var _0x5dcc4d = i_aPKPKe["font"];
    i_aPKPKe[_0x3628("0x217")] = _0x3628("0x496");
    var _0x19f1a1 = i_aPKPKe[_0x3628("0xab")](0x0, 0x0, i_twDsCe[_0x3628("0x191")], 0x0);
    _0x19f1a1[_0x3628("0x569")](0x0, "#fff"), _0x19f1a1[_0x3628("0x569")](0.5, _0x3628("0x6")), _0x19f1a1[_0x3628("0x569")](0x1, _0x3628("0x355")), i_aPKPKe[_0x3628("0x46d")] = _0x19f1a1, i_aPKPKe[_0x3628("0x1c2")](_0x23b50d, i_twDsCe["width"] / 0x2 - 0x32, i_twDsCe["height"] / 0x2, 0x12c), i_aPKPKe[_0x3628("0x217")] = _0x5dcc4d;
  }
}
function i_ntwTnn() {
  i_aPKPKe["clearRect"](0x0, 0x0, 0x2 * i_twDsCe[_0x3628("0x191")], i_twDsCe["height"]);
}
var i_XSeBtn = new Array();
function i_BSKman(_0x2e3fb5) {}
var i_tdceon = {
  k: {
    name: _0x3628("0x1e6"),
    id: _0x3628("0x2cf"),
    value: !0x0,
    type: _0x3628("0x10c")
  },
  T: {
    name: "显HP",
    id: "gh_showhptext",
    value: !0x0,
    type: _0x3628("0x10c")
  },
  M: {
    name: "显优先",
    id: "gh_showyouxian",
    value: !0x1,
    type: _0x3628("0x10c")
  },
  P: {
    name: _0x3628("0x167"),
    id: _0x3628("0x4e3"),
    value: !0x1,
    type: _0x3628("0x10c")
  }
};
function i_zWYrcn(_0x5868c5) {
  i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b"), i_aPKPKe[_0x3628("0x73")] = "#202020", i_aPKPKe[_0x3628("0x94")] = 0x1, i_aPKPKe[_0x3628("0x4be")] = 0x1, i_aPKPKe[_0x3628("0x321")] = 0x2;
  for (var _0x5b522b = 0x0; _0x5b522b < 0x8; _0x5b522b++) {
    var _0x4f46e0 = 0x2 * _0x5868c5[0x31 + _0x5b522b],
      _0x1ec832 = 0x2 * _0x5868c5[0x41 + _0x5b522b] + 0x2 * _0x5868c5[0x51 + _0x5b522b] - 0x94,
      _0x34a73a = _0x5868c5[0x63 + _0x5b522b];
    if (0x0 != _0x34a73a && i_tdceon["P"][_0x3628("0x32d")]) {
      if ("nfMIh" === _0x3628("0x518")) {
        if (i_Kbzklo != i_emcHro) return i_iZhRvt("非房主不可设置"), void (this["checked"] = !0x1);
        0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")]), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0, i_iZhRvt(_0x3628("0x183"))) : (i_hnXPpo(_0x3628("0x2a5"), "【随机武将开启】"), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0);
      } else {
        var _0x3dbf04 = _0x1ec832 + 0x1e + Math[_0x3628("0x560")](_0x34a73a / 0x4);
        _0x1ec832 < 0x14 && (_0x3dbf04 = 0x1e + Math[_0x3628("0x560")](_0x34a73a / 0x4));
        var _0x5bc370 = {
          x: _0x4f46e0 + Math[_0x3628("0x560")](0x3c * Math[_0x3628("0x175")]()) - 0x1e,
          y: _0x3dbf04,
          S: 0x64 * _0x34a73a,
          K: 0xf,
          O: 0xc,
          R: Math[_0x3628("0x560")](_0x34a73a / 0x28) + 0x2
        };
        i_XSeBtn[_0x3628("0x35d")](_0x5bc370);
      }
    }
  }
  if (i_MChKiu % 0x2 != 0x0) {
    if (_0x3628("0x7b") === _0x3628("0x7b")) {
      if (i_aPKPKe[_0x3628("0x23c")](0x0, 0x0, i_twDsCe[_0x3628("0x191")], i_twDsCe[_0x3628("0x191")]), i_aPKPKe["fillStyle"] = _0x3628("0x1ce"), i_tdceon["k"][_0x3628("0x32d")]) {
        var _0x520cfb = 0x0;
        for (_0x5b522b = 0x0; _0x5b522b < 0x8; _0x5b522b++) if (0x0 != _0x5868c5[0x11 + _0x5b522b]) {
          _0x4f46e0 = 0x2 * _0x5868c5[0x31 + _0x5b522b], _0x1ec832 = 0x2 * _0x5868c5[0x41 + _0x5b522b] + 0x2 * _0x5868c5[0x51 + _0x5b522b] - 0x94;
          i_aPKPKe[_0x3628("0x46d")] = "#eee", i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b");
          var _0x2ca803 = "";
          i_tdceon["M"][_0x3628("0x32d")] && (_0x2ca803 = _0x5b522b + 0x1 + ".\x20"), i_tdceon["T"][_0x3628("0x32d")] && (i_aPKPKe["strokeText"](_0x2ca803 + "HP:" + 0x64 * _0x5868c5[0x11 + _0x5b522b], _0x4f46e0 + 0x1 - 0xa, _0x1ec832 + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")](_0x2ca803 + _0x3628("0x44c") + 0x64 * _0x5868c5[0x11 + _0x5b522b], _0x4f46e0 - 0xa, _0x1ec832, 0x64)), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x3a0"), i_aPKPKe[_0x3628("0x285")] = 0x1, i_aPKPKe[_0x3628("0xfb")](), i_aPKPKe[_0x3628("0x190")](_0x4f46e0 - 0x10, _0x1ec832 - 0x14, 0x3c, 0x2), i_aPKPKe[_0x3628("0x324")](), i_aPKPKe[_0x3628("0x1f1")]();
          var _0x59286c = _0x5868c5[0x11 + _0x5b522b] / _0x5868c5[0x21 + _0x5b522b] * 0x3c;
          i_aPKPKe["fillStyle"] = 0x28 < _0x59286c ? _0x3628("0xa2") : 0x14 < _0x59286c ? _0x3628("0x220") : "#cf0000", i_aPKPKe[_0x3628("0xb1")](_0x4f46e0 - 0x10, _0x1ec832 - 0x14, _0x59286c, 0x2), _0x520cfb++;
        }
        0x0 < _0x5868c5[0x0] ? i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x40a") : i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x461"), i_aPKPKe[_0x3628("0x25e")] = "#111", i_aPKPKe[_0x3628("0x1fd")](_0x3628("0x527") + _0x520cfb, 0x2 * i_kRDkS_ - 0x5a + 0x1, 0x2 * i_kPQcC_ - 0x23 + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")]("剩余敌人" + _0x520cfb, 0x2 * i_kRDkS_ - 0x5a, 0x2 * i_kPQcC_ - 0x23, 0x64);
      }
      if (i_tdceon["P"][_0x3628("0x32d")]) {
        var _0x413538,
          _0x4c7bc1 = i_aPKPKe[_0x3628("0x217")];
        for (_0x413538 in i_aPKPKe[_0x3628("0x73")] = _0x3628("0x4f4"), i_aPKPKe[_0x3628("0x321")] = 0x5, i_aPKPKe[_0x3628("0x285")] = 0x2, i_XSeBtn) if (i_XSeBtn[_0x413538]["K"]--, i_XSeBtn[_0x413538]["y"] -= 0x2, 0x5 < i_XSeBtn[_0x413538]["K"] && (i_XSeBtn[_0x413538]["O"] += i_XSeBtn[_0x413538]["R"]), 0x0 < i_XSeBtn[_0x413538]["K"]) {
          i_aPKPKe[_0x3628("0x217")] = "900\x20" + i_XSeBtn[_0x413538]["O"] + "px\x20Microsoft\x20YaHei";
          var _0x23f9e2 = i_aPKPKe["createLinearGradient"](i_XSeBtn[_0x413538]["x"], i_XSeBtn[_0x413538]["y"] - i_XSeBtn[_0x413538]["O"] + 0x5, i_XSeBtn[_0x413538]["x"], i_XSeBtn[_0x413538]["y"] - 0x5);
          _0x23f9e2[_0x3628("0x569")](0x0, "#5be8a0"), _0x23f9e2["addColorStop"](0x1, "#ff0050"), i_aPKPKe[_0x3628("0x46d")] = _0x23f9e2, i_aPKPKe["fillText"](i_XSeBtn[_0x413538]["S"], i_XSeBtn[_0x413538]["x"], i_XSeBtn[_0x413538]["y"], 0x12c), i_aPKPKe[_0x3628("0x1fd")](i_XSeBtn[_0x413538]["S"], i_XSeBtn[_0x413538]["x"], i_XSeBtn[_0x413538]["y"], 0x12c);
        } else i_XSeBtn[_0x3628("0x338")]();
        i_aPKPKe[_0x3628("0x217")] = _0x4c7bc1;
      }
    } else {
      var _0x5f22b9 = i_hGaila(i_sxfci_(_0x3628("0x16c"))[_0x3628("0x32d")]);
      if ("" != _0x5f22b9) i_sxfci_(_0x3628("0x16c"))["value"] = "", i_jPrYkr(_0x5f22b9);else if (0x0 == i_iDpHde) i_sxfci_("messageinput")[_0x3628("0x32d")] = "", i_jPrYkr("我", _0x3628("0xbe"));else {
        var _0x177cfc = i_sxfci_(_0x3628("0x16c"))[_0x3628("0x32d")];
        "" != _0x177cfc && i_hnXPpo("message", _0x177cfc), i_sxfci_("messageinput")[_0x3628("0x32d")] = "";
      }
      i_sxfci_(_0x3628("0x16c"))[_0x3628("0x1a9")]();
    }
  }
}
var i_mQEfsn = {
  k: {
    name: _0x3628("0x1e6"),
    id: _0x3628("0x2cf"),
    value: !0x0,
    type: _0x3628("0x10c")
  },
  T: {
    name: _0x3628("0x84"),
    id: _0x3628("0x39"),
    value: !0x0,
    type: _0x3628("0x10c")
  },
  M: {
    name: _0x3628("0x2c7"),
    id: _0x3628("0x3bb"),
    value: !0x1,
    type: _0x3628("0x10c")
  },
  P: {
    name: _0x3628("0x167"),
    id: _0x3628("0x4e3"),
    value: !0x1,
    type: "checkbox"
  },
  I: {
    name: _0x3628("0x42c"),
    id: _0x3628("0x33d"),
    type: _0x3628("0x460"),
    value: !0x1,
    h: function (_0xacd2fd) {
      if (this[_0x3628("0x206")]) {
        if ("wBvvz" !== _0x3628("0x476")) {
          i_iDQBme ? (i_zkHZqn(), i_bPrAQi = !0x0) : 0x1 < i_QYKJTo && i_ZQAWMu();
        } else {
          if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this[_0x3628("0x206")] = !0x1);
          0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe["buffer"]), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0, i_iZhRvt(_0x3628("0x183"))) : (i_hnXPpo("message", _0x3628("0x183")), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao["send"](i_kJQyVe), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0);
        }
      }
    }
  }
};
function i_SShjrn(_0x495baf) {
  i_aPKPKe[_0x3628("0x25e")] = "#111", i_aPKPKe["shadowColor"] = _0x3628("0x181"), i_aPKPKe[_0x3628("0x94")] = 0x1, i_aPKPKe["shadowOffsetY"] = 0x1, i_aPKPKe[_0x3628("0x321")] = 0x2;
  for (var _0x57776e = 0x0; _0x57776e < 0x8; _0x57776e++) {
    var _0x353a8e = 0x2 * _0x495baf[0x31 + _0x57776e],
      _0x466601 = 0x2 * _0x495baf[0x41 + _0x57776e] + 0x2 * _0x495baf[0x51 + _0x57776e] - 0x94,
      _0x1be253 = _0x495baf[0x63 + _0x57776e];
    if (0x0 != _0x1be253 && i_mQEfsn["P"][_0x3628("0x32d")]) {
      var _0x20fb70 = _0x466601 + 0x1e + Math[_0x3628("0x560")](_0x1be253 / 0x4);
      _0x466601 < 0x14 && (_0x20fb70 = 0x1e + Math["floor"](_0x1be253 / 0x4));
      var _0x36bd40 = {
        x: _0x353a8e + Math[_0x3628("0x560")](0x3c * Math[_0x3628("0x175")]()) - 0x1e,
        y: _0x20fb70,
        S: 0x64 * _0x1be253,
        K: 0xf,
        O: 0xc,
        R: Math[_0x3628("0x560")](_0x1be253 / 0x28) + 0x2
      };
      i_XSeBtn[_0x3628("0x35d")](_0x36bd40);
    }
  }
  if (i_MChKiu % 0x2 != 0x0) {
    if (i_aPKPKe[_0x3628("0x23c")](0x0, 0x0, i_twDsCe["width"], i_twDsCe["width"]), i_aPKPKe["fillStyle"] = _0x3628("0x1ce"), i_mQEfsn["k"][_0x3628("0x32d")]) {
      var _0x276b33 = 0x0;
      for (_0x57776e = 0x0; _0x57776e < 0x8; _0x57776e++) if (0x0 != _0x495baf[0x11 + _0x57776e]) {
        _0x353a8e = 0x2 * _0x495baf[0x31 + _0x57776e], _0x466601 = 0x2 * _0x495baf[0x41 + _0x57776e] + 0x2 * _0x495baf[0x51 + _0x57776e] - 0x94;
        i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x1ce"), i_aPKPKe[_0x3628("0x25e")] = "#111";
        var _0x40d694 = "";
        i_mQEfsn["M"]["value"] && (_0x40d694 = _0x57776e + 0x1 + ".\x20"), i_mQEfsn["T"][_0x3628("0x32d")] && (i_aPKPKe[_0x3628("0x1fd")](_0x40d694 + _0x3628("0x44c") + 0x64 * _0x495baf[0x11 + _0x57776e], _0x353a8e + 0x1 - 0xa, _0x466601 + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")](_0x40d694 + "HP:" + 0x64 * _0x495baf[0x11 + _0x57776e], _0x353a8e - 0xa, _0x466601, 0x64)), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x3a0"), i_aPKPKe[_0x3628("0x285")] = 0x1, i_aPKPKe[_0x3628("0xfb")](), i_aPKPKe[_0x3628("0x190")](_0x353a8e - 0x10, _0x466601 - 0x14, 0x3c, 0x2), i_aPKPKe[_0x3628("0x324")](), i_aPKPKe[_0x3628("0x1f1")]();
        var _0x26b1cd = _0x495baf[0x11 + _0x57776e] / _0x495baf[0x21 + _0x57776e] * 0x3c;
        i_aPKPKe["fillStyle"] = 0x28 < _0x26b1cd ? _0x3628("0xa2") : 0x14 < _0x26b1cd ? _0x3628("0x220") : "#cf0000", i_aPKPKe[_0x3628("0xb1")](_0x353a8e - 0x10, _0x466601 - 0x14, _0x26b1cd, 0x2), _0x276b33++;
      }
      0x0 < _0x495baf[0x0] ? i_aPKPKe[_0x3628("0x46d")] = "#f0fff0" : i_aPKPKe["fillStyle"] = _0x3628("0x461"), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b"), i_aPKPKe["strokeText"]("剩余敌人" + _0x276b33, 0x2 * i_kRDkS_ - 0x5a + 0x1, 0x2 * i_kPQcC_ - 0x23 + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")](_0x3628("0x527") + _0x276b33, 0x2 * i_kRDkS_ - 0x5a, 0x2 * i_kPQcC_ - 0x23, 0x64);
    }
    if (i_mQEfsn["P"][_0x3628("0x32d")]) {
      var _0xf32f47,
        _0x30f771 = i_aPKPKe["font"];
      for (_0xf32f47 in i_aPKPKe[_0x3628("0x73")] = _0x3628("0x4f4"), i_aPKPKe[_0x3628("0x321")] = 0x5, i_aPKPKe[_0x3628("0x285")] = 0x2, i_XSeBtn) if (i_XSeBtn[_0xf32f47]["K"]--, i_XSeBtn[_0xf32f47]["y"] -= 0x2, 0x5 < i_XSeBtn[_0xf32f47]["K"] && (i_XSeBtn[_0xf32f47]["O"] += i_XSeBtn[_0xf32f47]["R"]), 0x0 < i_XSeBtn[_0xf32f47]["K"]) {
        i_aPKPKe[_0x3628("0x217")] = "900\x20" + i_XSeBtn[_0xf32f47]["O"] + _0x3628("0x2ea");
        var _0x5ed5d6 = i_aPKPKe["createLinearGradient"](i_XSeBtn[_0xf32f47]["x"], i_XSeBtn[_0xf32f47]["y"] - i_XSeBtn[_0xf32f47]["O"] + 0x5, i_XSeBtn[_0xf32f47]["x"], i_XSeBtn[_0xf32f47]["y"] - 0x5);
        _0x5ed5d6["addColorStop"](0x0, "#5be8a0"), _0x5ed5d6[_0x3628("0x569")](0x1, _0x3628("0x530")), i_aPKPKe["fillStyle"] = _0x5ed5d6, i_aPKPKe["fillText"](i_XSeBtn[_0xf32f47]["S"], i_XSeBtn[_0xf32f47]["x"], i_XSeBtn[_0xf32f47]["y"], 0x12c), i_aPKPKe[_0x3628("0x1fd")](i_XSeBtn[_0xf32f47]["S"], i_XSeBtn[_0xf32f47]["x"], i_XSeBtn[_0xf32f47]["y"], 0x12c);
      } else i_XSeBtn[_0x3628("0x338")]();
      i_aPKPKe[_0x3628("0x217")] = _0x30f771;
    }
  }
}
var i_EBCZln = {
  k: {
    name: _0x3628("0x1e6"),
    id: _0x3628("0x2cf"),
    value: !0x0,
    type: _0x3628("0x10c")
  },
  N: {
    name: _0x3628("0x240"),
    id: _0x3628("0x1c3"),
    value: !0x0,
    type: "checkbox"
  },
  U: {
    name: _0x3628("0x514"),
    id: "gh_showtime",
    value: !0x0,
    type: _0x3628("0x10c")
  },
  T: {
    name: _0x3628("0x84"),
    id: _0x3628("0x39"),
    value: !0x0,
    type: _0x3628("0x10c")
  },
  M: {
    name: "显优先",
    id: _0x3628("0x3bb"),
    value: !0x1,
    type: _0x3628("0x10c")
  },
  P: {
    name: _0x3628("0x167"),
    id: _0x3628("0x4e3"),
    value: !0x1,
    type: _0x3628("0x10c")
  },
  I: {
    name: "随机武将",
    id: _0x3628("0x33d"),
    type: _0x3628("0x460"),
    value: !0x1,
    h: function (_0x4aad6e) {
      if (this[_0x3628("0x206")]) {
        if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this[_0x3628("0x206")] = !0x1);
        0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe["buffer"]), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0, i_iZhRvt("【随机武将开启】")) : (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x183")), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao["send"](i_kJQyVe), i_sxfci_(_0x3628("0x33d"))[_0x3628("0x306")] = !0x0);
      }
    }
  },
  D: {
    name: _0x3628("0x1ff"),
    id: _0x3628("0x395"),
    type: _0x3628("0x460"),
    value: !0x1,
    h: function (_0x496b5c) {
      if (this["checked"]) {
        if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this[_0x3628("0x206")] = !0x1);
        0x0 == i_iDpHde ? (i_kJQyVe[0x3] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")]), i_sxfci_("gh_randbsel")[_0x3628("0x306")] = !0x0, i_iZhRvt(_0x3628("0x570"))) : (i_hnXPpo("message", "【随机B面武将开启】"), i_kJQyVe[0x3] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe), i_sxfci_(_0x3628("0x395"))[_0x3628("0x306")] = !0x0);
      }
    }
  },
  G: {
    name: _0x3628("0x4a4"),
    id: _0x3628("0x2e0"),
    type: _0x3628("0x460"),
    value: !0x1,
    h: function (_0x2458f7) {
      if (this[_0x3628("0x206")]) {
        if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this[_0x3628("0x206")] = !0x1);
        0x1 == i_iDpHde ? (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x8")), i_kJQyVe[0x1] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe), i_sxfci_(_0x3628("0x2e0"))["disabled"] = !0x0) : (i_kJQyVe[0x1] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe["buffer"]), i_sxfci_(_0x3628("0x2e0"))[_0x3628("0x306")] = !0x0, i_GJQKdt("【练习模式开启】"));
      }
    }
  },
  F: {
    name: _0x3628("0x43a"),
    id: "gh_deadman",
    type: _0x3628("0x460"),
    value: !0x1,
    h: function (_0x16c1da) {
      if (this[_0x3628("0x206")]) {
        if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this["checked"] = !0x1);
        0x1 == i_iDpHde ? (i_hnXPpo(_0x3628("0x2a5"), "【敌人不动开启】"), i_kJQyVe[0x2] = i_EHEBeo + 0xb4, i_sCrNao["send"](i_kJQyVe), i_sxfci_(_0x3628("0x320"))[_0x3628("0x306")] = !0x0) : (i_kJQyVe[0x2] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe[_0x3628("0x37a")]), i_sxfci_("gh_deadman")[_0x3628("0x306")] = !0x0, i_GJQKdt("【敌人不动开启】"));
      }
    }
  }
};
function i_dkWyun(_0x26f273) {
  for (var _0x35a0af = 0x0; _0x35a0af < _0x26f273[0x0]; _0x35a0af++) {
    var _0x472aed = 0x2 * _0x26f273[0x31 + _0x35a0af],
      _0x25b52e = 0x2 * _0x26f273[0x41 + _0x35a0af] + 0x2 * _0x26f273[0x51 + _0x35a0af] - 0x94,
      _0x50a47a = _0x26f273[0x63 + _0x35a0af];
    if (0x0 != _0x50a47a && i_EBCZln["P"]["value"]) {
      var _0xe6120 = _0x25b52e + 0x1e + Math[_0x3628("0x560")](_0x50a47a / 0x4);
      _0x25b52e < 0x14 && (_0xe6120 = 0x1e + Math[_0x3628("0x560")](_0x50a47a / 0x4));
      var _0x32a1d0 = {
        x: _0x472aed + Math[_0x3628("0x560")](0x3c * Math[_0x3628("0x175")]()) - 0x1e,
        y: _0xe6120,
        S: 0x64 * _0x50a47a,
        K: 0xf,
        O: 0xc,
        R: Math["floor"](_0x50a47a / 0x28) + 0x2
      };
      i_XSeBtn["push"](_0x32a1d0);
    }
  }
  if (i_MChKiu % 0x2 != 0x0) {
    if (i_aPKPKe[_0x3628("0x23c")](0x0, 0x0, i_twDsCe[_0x3628("0x191")], i_twDsCe[_0x3628("0x191")]), i_EBCZln["U"][_0x3628("0x32d")]) {
      var _0x4948c1 = _0x26f273[0x62],
        _0x43ef8f = Math[_0x3628("0x560")](16.66666 * _0x4948c1 / 0x3e8),
        _0x29b34f = Math[_0x3628("0x560")](_0x43ef8f / 0x3c);
      _0x43ef8f %= 0x3c;
      var _0x388d7f = Math[_0x3628("0x560")](16.66666 * _0x4948c1 % 0x3e8 / 0xa);
      i_aPKPKe["fillStyle"] = _0x3628("0xbb"), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b"), i_aPKPKe[_0x3628("0x73")] = "#202020", i_aPKPKe["shadowOffsetX"] = 0x1, i_aPKPKe[_0x3628("0x4be")] = 0x1, i_aPKPKe[_0x3628("0x321")] = 0x2, i_aPKPKe[_0x3628("0x1fd")]("\x20" + _0x29b34f + "分" + _0x43ef8f + "." + _0x388d7f + "秒", 0x2 * i_kRDkS_ - 0x50 + 0x1, 0x2 * i_kPQcC_ - 0x4 + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")]("\x20" + _0x29b34f + "分" + _0x43ef8f + "." + _0x388d7f + "秒", 0x2 * i_kRDkS_ - 0x50, 0x2 * i_kPQcC_ - 0x4, 0x64);
    }
    if (i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x1ce"), i_EBCZln["N"]["value"]) {
      var _0x468ae8 = "";
      switch (0x8 & _0x26f273[0x61] && (_0x468ae8 = "上"), 0x4 & _0x26f273[0x61] && (_0x468ae8 += "下"), 0x1 & _0x26f273[0x61] && (_0x468ae8 += "A"), 0x2 & _0x26f273[0x61] && (_0x468ae8 += "B"), 0x10 & _0x26f273[0x61] && (_0x468ae8 += "C"), "" == _0x468ae8 && (_0x468ae8 = "无"), _0x26f273[0x73]) {
        case 0x1:
          _0x468ae8 += "【简单】";
          break;
        case 0x2:
          _0x468ae8 += _0x3628("0xe8");
          break;
        case 0x3:
          _0x468ae8 += "【达人】";
          break;
        case 0x4:
          _0x468ae8 += _0x3628("0x401");
          break;
        case 0x5:
          _0x468ae8 += _0x3628("0x129");
          break;
        case 0x6:
          _0x468ae8 += "【宗师】";
          break;
        case 0x7:
          _0x468ae8 += _0x3628("0x342");
          break;
        case 0x8:
          _0x468ae8 += "【至尊】";
          break;
        case 0x9:
          _0x468ae8 += _0x3628("0x214");
          break;
        default:
          _0x468ae8 += _0x3628("0x4ea");
      }
      i_aPKPKe[_0x3628("0x1fd")](_0x3628("0x328") + _0x468ae8, 0x2 * i_kRDkS_ - 0x78 + 0x1, 0x2 * i_kPQcC_ - 0x32 + 0x1, 0x78), i_aPKPKe[_0x3628("0x1c2")](_0x3628("0x328") + _0x468ae8, 0x2 * i_kRDkS_ - 0x78, 0x2 * i_kPQcC_ - 0x32, 0x78);
    }
    if (i_EBCZln["k"][_0x3628("0x32d")]) {
      var _0x2fc528 = 0x0;
      for (_0x35a0af = 0x0; _0x35a0af < _0x26f273[0x0]; _0x35a0af++) if (0x0 != _0x26f273[0x11 + _0x35a0af]) {
        if (_0x3628("0x57") !== _0x3628("0x22b")) {
          _0x472aed = 0x2 * _0x26f273[0x31 + _0x35a0af], _0x25b52e = 0x2 * _0x26f273[0x41 + _0x35a0af] + 0x2 * _0x26f273[0x51 + _0x35a0af] - 0x94;
          i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x1ce"), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b");
          var _0x529df8 = "";
          i_EBCZln["M"][_0x3628("0x32d")] && (_0x529df8 = _0x35a0af + 0x1 + ".\x20"), i_EBCZln["T"] && (i_aPKPKe[_0x3628("0x1fd")](_0x529df8 + _0x3628("0x44c") + 0x64 * _0x26f273[0x11 + _0x35a0af], _0x472aed + 0x1 - 0xa, _0x25b52e + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")](_0x529df8 + "HP:" + 0x64 * _0x26f273[0x11 + _0x35a0af], _0x472aed - 0xa, _0x25b52e, 0x64)), i_aPKPKe[_0x3628("0x25e")] = "#fff", i_aPKPKe[_0x3628("0x285")] = 0x1, i_aPKPKe[_0x3628("0xfb")](), i_aPKPKe[_0x3628("0x190")](_0x472aed - 0x10, _0x25b52e - 0x14, 0x3c, 0x2), i_aPKPKe[_0x3628("0x324")](), i_aPKPKe[_0x3628("0x1f1")]();
          var _0x24d52b = _0x26f273[0x11 + _0x35a0af] / _0x26f273[0x21 + _0x35a0af] * 0x3c;
          i_aPKPKe[_0x3628("0x46d")] = 0x28 < _0x24d52b ? "#50ff60" : 0x14 < _0x24d52b ? _0x3628("0x220") : _0x3628("0x575"), i_aPKPKe["fillRect"](_0x472aed - 0x10, _0x25b52e - 0x14, _0x24d52b, 0x2), _0x2fc528++;
        } else {
          i_CHHkh_[i_YxFxe] = 0x0, i_tXrZr_(i_YxFxe, _0x3628("0x87"), function (_0x53c3bf) {
            if (this[_0x3628("0x306")]) _0x53c3bf[_0x3628("0x23")]();else {
              var _0x3358ba = this["getClientRects"](),
                _0x35dcd9 = i_QtJCKs(_0x3358ba[0x0]["x"], _0x3358ba[0x0]["y"]),
                _0x12c39b = i_QtJCKs(_0x53c3bf[_0x3628("0x3db")][0x0][_0x3628("0x325")], _0x53c3bf["changedTouches"][0x0][_0x3628("0x16e")])["pn"];
              this[_0x3628("0x32d")] = (_0x12c39b - _0x35dcd9["pn"]) / (this["offsetWidth"] / (parseInt(this[_0x3628("0x1d1")]) - parseInt(this[_0x3628("0x359")]))) + parseInt(this[_0x3628("0x359")]), i_iSHNi(this), _0x53c3bf[_0x3628("0x23")]();
            }
          });
        }
      }
      if (0x0 < _0x26f273[0x0] ? i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x40a") : i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x461"), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x13b"), i_aPKPKe[_0x3628("0x1fd")](_0x3628("0x527") + _0x2fc528, 0x2 * i_kRDkS_ - 0x5a + 0x1, 0x2 * i_kPQcC_ - 0x23 + 0x1, 0x64), i_aPKPKe["fillText"](_0x3628("0x527") + _0x2fc528, 0x2 * i_kRDkS_ - 0x5a, 0x2 * i_kPQcC_ - 0x23, 0x64), i_EBCZln["P"][_0x3628("0x32d")]) {
        var _0x51cb03,
          _0x39f0aa = i_aPKPKe[_0x3628("0x217")];
        for (_0x51cb03 in i_aPKPKe[_0x3628("0x73")] = _0x3628("0x4f4"), i_aPKPKe["shadowBlur"] = 0x5, i_aPKPKe[_0x3628("0x285")] = 0x2, i_XSeBtn) if (i_XSeBtn[_0x51cb03]["K"]--, i_XSeBtn[_0x51cb03]["y"] -= 0x2, 0x5 < i_XSeBtn[_0x51cb03]["K"] && (i_XSeBtn[_0x51cb03]["O"] += i_XSeBtn[_0x51cb03]["R"]), 0x0 < i_XSeBtn[_0x51cb03]["K"]) {
          i_aPKPKe["font"] = "900\x20" + i_XSeBtn[_0x51cb03]["O"] + _0x3628("0x2ea");
          var _0x38dd3f = i_aPKPKe[_0x3628("0xab")](i_XSeBtn[_0x51cb03]["x"], i_XSeBtn[_0x51cb03]["y"] - i_XSeBtn[_0x51cb03]["O"] + 0x5, i_XSeBtn[_0x51cb03]["x"], i_XSeBtn[_0x51cb03]["y"] - 0x5);
          _0x38dd3f[_0x3628("0x569")](0x0, _0x3628("0x1b7")), _0x38dd3f[_0x3628("0x569")](0x1, _0x3628("0x530")), i_aPKPKe[_0x3628("0x46d")] = _0x38dd3f, i_aPKPKe[_0x3628("0x1c2")](i_XSeBtn[_0x51cb03]["S"], i_XSeBtn[_0x51cb03]["x"], i_XSeBtn[_0x51cb03]["y"], 0x12c), i_aPKPKe["strokeText"](i_XSeBtn[_0x51cb03]["S"], i_XSeBtn[_0x51cb03]["x"], i_XSeBtn[_0x51cb03]["y"], 0x12c);
        } else i_XSeBtn[_0x3628("0x338")]();
        i_aPKPKe[_0x3628("0x217")] = _0x39f0aa;
      }
    }
  }
}
function i_MNhEfn() {}
var i_ZkGQ_n = !0x1;
function i_kRWdvn() {
  $(_0x3628("0x351"))[_0x3628("0x489")]({
    min: 0x0,
    max: 0x64,
    step: 0.1,
    i: function (_0x3181ba) {}
  });
}
function i_Gphxdn(_0x1ac960) {
  i_sxfci_("loadingpos")[_0x3628("0x32d")] = _0x1ac960, i_sxfci_(_0x3628("0x310"))[_0x3628("0x3e")][_0x3628("0x1c8")] = _0x1ac960 + _0x3628("0x62"), 0x64 <= _0x1ac960 && 0x0 == i_ZkGQ_n && (i_ZkGQ_n = !0x0, i_sxfci_(_0x3628("0x347"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_sxfci_(_0x3628("0x98"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", i_nHTFAe());
}
var i_GFETpn = "<option\x20value=\x22{{pos}}\x22>{{name}}</option>";
function i_dWCtmn(_0x2d8a86) {
  i_HjtSei = parseInt(this[_0x3628("0x32d")]), localStorage["setItem"](_0x3628("0x304"), v_allGamePadName[this[_0x3628("0x32d")]]["id"]);
}
function i_ysQfgn() {
  i_tXrZr_(_0x3628("0x179"), "change", i_dWCtmn);
}
function i_StRmhn(_0x22950e) {
  i_sxfci_(_0x3628("0x179"))[_0x3628("0x24d")] = "";
  var _0x446143 = !0x1,
    _0x23e0ff = 0x0;
  i_HjtSei = -0x1, v_allGamePadName = {};
  for (var _0x17a968 = localStorage[_0x3628("0x392")]("lastgamepad"), _0x12f96e = 0x0; _0x12f96e < _0x22950e["length"]; _0x12f96e++) if (null != _0x22950e[_0x12f96e]) {
    if ("qPmxL" === _0x3628("0x10d")) {
      _0x446143 = !0x0;
      var _0xc8de5b = document[_0x3628("0x3fe")](_0x3628("0x119"));
      _0xc8de5b[_0x3628("0x32d")] = _0x12f96e, _0xc8de5b[_0x3628("0x107")](_0x3628("0x376"), _0x22950e[_0x12f96e]["id"]), _0xc8de5b[_0x3628("0x24d")] = _0x22950e[_0x12f96e]["id"][_0x3628("0x26a")](0x0, _0x22950e[_0x12f96e]["id"]["indexOf"](_0x3628("0x75"))), "" == _0xc8de5b[_0x3628("0x24d")] && (_0xc8de5b["innerHTML"] = _0x22950e[_0x12f96e]["id"][_0x3628("0x26a")](0x0, _0x22950e[_0x12f96e]["id"][_0x3628("0x2f3")]("("))), i_sxfci_(_0x3628("0x179"))[_0x3628("0xd6")](_0xc8de5b), _0x23e0ff++, -0x1 != i_HjtSei && _0x17a968 != _0x22950e[_0x12f96e]["id"] || (i_HjtSei = _0x12f96e), v_allGamePadName[_0x12f96e] = {}, v_allGamePadName[_0x12f96e]["id"] = _0x22950e[_0x12f96e]["id"], v_allGamePadName[_0x12f96e][_0x3628("0x63")] = _0x12f96e;
    } else {
      i_yYwPle["_"] = new Date(), i_yYwPle["v"] = i_yYwPle["_"] - i_yYwPle["u"];
      var _0x4033ec = _0x3628("0x4f5");
      0x0 == i_iDpHde ? _0x4033ec = _0x3628("0x174") : i_njFT_l && (_0x4033ec = _0x3628("0x248")), i_TQeTre({
        GameType: i_yYwPle[_0x3628("0x13e")],
        GameID: i_yYwPle[_0x3628("0x260")],
        Version: _0x3628("0xed"),
        StrData: [_0x4033ec, i_wesTbe + "_" + i_ZmCwpe, i_yYwPle["u"], i_yYwPle["_"], i_yYwPle["v"] + ""],
        IntData: [i_DdTaQf["LevelInfo"]["Level"], i_DdTaQf[_0x3628("0x31")]["AllXP"], i_DdTaQf[_0x3628("0x31")][_0x3628("0x33e")], i_DdTaQf[_0x3628("0x31")]["Svip"], i_yYwPle["p"]]
      });
    }
  }
  return _0x446143 ? i_sxfci_("gamepad_select")["value"] = v_allGamePadName[i_HjtSei][_0x3628("0x63")] : i_sxfci_("gamepad_select")[_0x3628("0x24d")] = _0x3628("0x235"), _0x23e0ff;
}
var i_cJzsbn = !0x1;
function i_mPGKyn(_0x1b417d) {
  if ("NO" == (_0x1f07e5 = (_0x1f07e5 = (_0x1f07e5 = i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(_0x1b417d)][_0x3628("0x16d")](_0x3628("0xd5"), ""))["replace"](_0x3628("0x1e2"), ""))[_0x3628("0x16d")]("Numpad", "")) && (_0x1f07e5 = "空"), _0x1b417d[_0x3628("0x32d")] = _0x1f07e5, i_cJzsbn && (0x0 <= i_Brhwo_(_0x1b417d)["indexOf"](_0x3628("0x58")) || 0x0 <= i_Brhwo_(_0x1b417d)["indexOf"]("KeyS"))) {
    var _0x1f07e5 = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x1b417d)];
    _0x1b417d[_0x3628("0x32d")] = _0x1f07e5;
  }
}
function i_JKSNwn(_0x12f0d5) {
  i_WmyDOu["gExtX" + _0x12f0d5 + _0x3628("0xd5")] & i_TEaXzu["A"] && (document[_0x3628("0x416")]("X" + _0x12f0d5 + "A")[_0x3628("0x206")] = !0x0), i_WmyDOu[_0x3628("0x19f") + _0x12f0d5 + _0x3628("0xd5")] & i_TEaXzu["B"] && (document[_0x3628("0x416")]("X" + _0x12f0d5 + "B")[_0x3628("0x206")] = !0x0), i_WmyDOu["gExtX" + _0x12f0d5 + _0x3628("0xd5")] & i_TEaXzu["C"] && (document["getElementById"]("X" + _0x12f0d5 + "C")[_0x3628("0x206")] = !0x0), i_WmyDOu["gExtX" + _0x12f0d5 + "Key"] & i_TEaXzu["L"] && (document[_0x3628("0x416")]("X" + _0x12f0d5 + "D")["checked"] = !0x0), i_WmyDOu[_0x3628("0x19f") + _0x12f0d5 + _0x3628("0xd5")] & i_TEaXzu["E"] && (document[_0x3628("0x416")]("X" + _0x12f0d5 + "E")[_0x3628("0x206")] = !0x0), i_WmyDOu[_0x3628("0x19f") + _0x12f0d5 + _0x3628("0xd5")] & i_TEaXzu["q"] && (document[_0x3628("0x416")]("X" + _0x12f0d5 + "F")[_0x3628("0x206")] = !0x0);
}
function i_Bckxkn() {
  document[_0x3628("0x566")](_0x3628("0x15a"))[_0x3628("0x64")](function (_0x36b127) {
    i_mPGKyn(_0x36b127);
  }), i_QZBzTn();
}
function i_QZBzTn() {
  i_sxfci_(_0x3628("0x1e8"))["checked"] = !i_WmyDOu["gDisHoldLR"], i_tXrZr_(_0x3628("0x1e8"), _0x3628("0x2fd"), function (_0x2e9dbc) {
    i_WmyDOu[_0x3628("0x192")] = !_0x2e9dbc[_0x3628("0x2f9")][_0x3628("0x206")];
  });
}
function i_nTkXxn(_0x3ebbf4) {
  document[_0x3628("0x566")]("kbdbind")["forEach"](function (_0x556d72) {
    i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(_0x556d72)] == _0x3ebbf4 && (i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(_0x556d72)] = "NO", i_mPGKyn(_0x556d72));
  });
}
function i_FTQZMn() {
  document[_0x3628("0x566")](_0x3628("0x15a"))[_0x3628("0x64")](function (_0xab3f4) {
    var _0xd252c2 = null,
      _0x3a7a30 = {
        buttons: []
      };
    _0xab3f4["addEventListener"](_0x3628("0x30e"), function () {
      if (i_cJzsbn) {
        var _0xbd9c81 = i_HHPb_i();
        if (null == _0xbd9c81) return _0xab3f4["blur"](), void i_iZhRvt(_0x3628("0x227"));
        for (var _0x35e7e8 in _0xbd9c81[_0x3628("0x479")]) _0x3a7a30[_0x3628("0x479")][_0x35e7e8] = _0xbd9c81[_0x3628("0x479")][_0x35e7e8][_0x3628("0xc0")];
        _0x3a7a30[_0x3628("0x0")] = _0xbd9c81[_0x3628("0x0")], _0xd252c2 = setInterval(function () {
          for (var _0xae5b0 = i_HHPb_i(), _0x5e0677 = 0x0; _0x5e0677 < _0xae5b0[_0x3628("0x479")][_0x3628("0x288")]; _0x5e0677++) if (_0xae5b0[_0x3628("0x479")][_0x5e0677][_0x3628("0xc0")] && 0x0 == _0x3a7a30[_0x3628("0x479")][_0x5e0677]) {
            i_NazaSn(_0x5e0677), i_WmyDOu["GPSetx"][i_Brhwo_(_0xab3f4)] = _0x5e0677, "KeyLeft" == i_Brhwo_(_0xab3f4) && (i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x0), _0xab3f4[_0x3628("0x1a9")]();
            break;
          }
          for (_0x5e0677 = 0x0; _0x5e0677 < _0xae5b0["axes"][_0x3628("0x288")]; _0x5e0677++) if (0.5 < Math[_0x3628("0x2f6")](_0xae5b0["axes"][_0x5e0677]) && _0xae5b0["axes"][_0x5e0677] != _0x3a7a30[_0x3628("0x0")][_0x5e0677]) {
            i_WmyDOu["GPSetx"]["KeyJoy"] = 0x1, i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0xab3f4)] = _0x5e0677, _0xab3f4[_0x3628("0x1a9")]();
            break;
          }
        }, 0x32);
      }
    }), _0xab3f4["addEventListener"](_0x3628("0x1a9"), function () {
      i_cJzsbn ? (_0xab3f4[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0xab3f4)], clearInterval(_0xd252c2)) : i_mPGKyn(this);
    }), _0xab3f4[_0x3628("0x314")](_0x3628("0x36e"), function (_0x418cef) {
      if (_0x3628("0x512") !== _0x3628("0x52")) {
        _0x3628("0x2bf") != _0x418cef[_0x3628("0x376")] ? (i_nTkXxn(_0x418cef[_0x3628("0x26f")]), _0x3628("0x474") == _0x418cef[_0x3628("0x26f")] ? i_WmyDOu["PCSet"][i_Brhwo_(this)] = "NO" : i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(this)] = _0x418cef["code"], i_mPGKyn(this), this[_0x3628("0x1a9")](), $(this)["next"]()[_0x3628("0x30e")](), _0x418cef[_0x3628("0x23")]()) : this[_0x3628("0x1a9")]();
      } else {
        var _0x24cf12 = firstCall ? function () {
          if (fn) {
            var _0x19427a = fn[_0x3628("0x45e")](context, arguments);
            fn = null;
            return _0x19427a;
          }
        } : function () {};
        firstCall = ![];
        return _0x24cf12;
      }
    });
  });
}
function i_CmAEPn(_0x1d5f31) {
  document[_0x3628("0x566")]("X" + _0x1d5f31 + _0x3628("0x549"))["forEach"](function (_0x3d0671) {
    _0x3d0671[_0x3628("0x314")](_0x3628("0x2fd"), function () {
      var _0x41273d = this;
      _0x41273d["id"] == "X" + _0x1d5f31 + "A" && (_0x41273d[_0x3628("0x206")] ? i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + _0x3628("0xd5")] |= i_TEaXzu["A"] : i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + _0x3628("0xd5")] &= ~i_TEaXzu["A"]), _0x41273d["id"] == "X" + _0x1d5f31 + "B" && (_0x41273d[_0x3628("0x206")] ? i_WmyDOu["gExtX" + _0x1d5f31 + _0x3628("0xd5")] |= i_TEaXzu["B"] : i_WmyDOu["gExtX" + _0x1d5f31 + "Key"] &= ~i_TEaXzu["B"]), _0x41273d["id"] == "X" + _0x1d5f31 + "C" && (_0x41273d["checked"] ? i_WmyDOu["gExtX" + _0x1d5f31 + _0x3628("0xd5")] |= i_TEaXzu["C"] : i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + _0x3628("0xd5")] &= ~i_TEaXzu["C"]), _0x41273d["id"] == "X" + _0x1d5f31 + "D" && (_0x41273d[_0x3628("0x206")] ? i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + "Key"] |= i_TEaXzu["L"] : i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + _0x3628("0xd5")] &= ~i_TEaXzu["L"]), _0x41273d["id"] == "X" + _0x1d5f31 + "E" && (_0x41273d["checked"] ? i_WmyDOu["gExtX" + _0x1d5f31 + _0x3628("0xd5")] |= i_TEaXzu["E"] : i_WmyDOu["gExtX" + _0x1d5f31 + _0x3628("0xd5")] &= ~i_TEaXzu["E"]), _0x41273d["id"] == "X" + _0x1d5f31 + "F" && (_0x41273d["checked"] ? i_WmyDOu[_0x3628("0x19f") + _0x1d5f31 + _0x3628("0xd5")] |= i_TEaXzu["q"] : i_WmyDOu["gExtX" + _0x1d5f31 + _0x3628("0xd5")] &= ~i_TEaXzu["q"]);
    });
  });
}
function i_NXeiEn() {
  i_tXrZr_(_0x3628("0xcf"), _0x3628("0x2fd"), function (_0x3e94e3) {
    i_WmyDOu["gDisHoldLR"] = this[_0x3628("0x206")];
  });
}
function i_fkKwAn() {
  document[_0x3628("0x566")]("gamepadbind")["forEach"](function (_0x24a5e7) {
    _0x24a5e7["value"] = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x24a5e7)];
  });
  var _0x455e04 = document["getElementById"]("gamepadlag"),
    _0x21ba0b = document["getElementById"](_0x3628("0x3d2"));
  _0x455e04[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0x2a3")];
  var _0x1b51ef = 0x8 + 1.3 * Number(0x64 * (_0x455e04[_0x3628("0x32d")] - _0x455e04[_0x3628("0x359")]) / (_0x455e04[_0x3628("0x1d1")] - _0x455e04[_0x3628("0x359")]));
  _0x21ba0b[_0x3628("0x24d")] = _0x3628("0x146") + _0x455e04[_0x3628("0x32d")] + _0x3628("0x69"), _0x21ba0b[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x24") + _0x1b51ef + "px)";
}
function i_NazaSn(_0x1b2d2e) {
  document["getElementsByName"](_0x3628("0x25"))["forEach"](function (_0x14abd5) {
    _0x3628("0xf5") != i_Brhwo_(_0x14abd5) && _0x3628("0x488") != i_Brhwo_(_0x14abd5) && _0x3628("0x491") != i_Brhwo_(_0x14abd5) && _0x3628("0x74") != i_Brhwo_(_0x14abd5) && _0x3628("0x4b1") != i_Brhwo_(_0x14abd5) && i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x14abd5)] == _0x1b2d2e && (i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x14abd5)] = -0x1, _0x14abd5[_0x3628("0x32d")] = -0x1);
  }), document[_0x3628("0x566")](_0x3628("0x15a"))[_0x3628("0x64")](function (_0x113d40) {
    (0x0 <= i_Brhwo_(_0x113d40)[_0x3628("0x2f3")](_0x3628("0x58")) || 0x0 <= i_Brhwo_(_0x113d40)[_0x3628("0x2f3")]("KeyS")) && i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x113d40)] == _0x1b2d2e && (i_WmyDOu["GPSetx"][i_Brhwo_(_0x113d40)] = -0x1, _0x113d40["value"] = -0x1);
  });
}
function i_FtZiCn() {
  document[_0x3628("0x566")]("gamepadbind")[_0x3628("0x64")](function (_0x1f7a5b) {
    var _0x8e9495 = null,
      _0x3d7e69 = {
        buttons: []
      };
    _0x1f7a5b[_0x3628("0x314")](_0x3628("0x30e"), function () {
      var _0x3d8e74 = i_HHPb_i();
      if (null == _0x3d8e74) return _0x1f7a5b[_0x3628("0x1a9")](), void i_iZhRvt(_0x3628("0x227"));
      for (var _0x5ade2a in _0x3d8e74[_0x3628("0x479")]) _0x3d7e69["buttons"][_0x5ade2a] = _0x3d8e74["buttons"][_0x5ade2a][_0x3628("0xc0")];
      _0x3d7e69[_0x3628("0x0")] = _0x3d8e74[_0x3628("0x0")], _0x8e9495 = setTimeout(function _0x5c0e93() {
        if (_0x3628("0x47e") != i_sxfci_(_0x3628("0x2b8"))["style"][_0x3628("0x3b5")]) {
          for (var _0x1c3f8e = i_HHPb_i(), _0x2332d2 = 0x0; _0x2332d2 < _0x1c3f8e["buttons"][_0x3628("0x288")]; _0x2332d2++) if (_0x1c3f8e[_0x3628("0x479")][_0x2332d2][_0x3628("0xc0")] && 0x0 == _0x3d7e69["buttons"][_0x2332d2]) return i_NazaSn(_0x2332d2), i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x1f7a5b)] = _0x2332d2, _0x3628("0x74") == i_Brhwo_(_0x1f7a5b) && (i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x0), _0x1f7a5b[_0x3628("0x1a9")](), void setTimeout(function () {
            $(_0x1f7a5b)["next"]()[_0x3628("0x30e")]();
          }, 0xc8);
          for (_0x2332d2 = 0x0; _0x2332d2 < _0x1c3f8e[_0x3628("0x0")][_0x3628("0x288")]; _0x2332d2++) if (0.5 < Math[_0x3628("0x2f6")](_0x1c3f8e[_0x3628("0x0")][_0x2332d2]) && _0x1c3f8e["axes"][_0x2332d2] != _0x3d7e69[_0x3628("0x0")][_0x2332d2]) return 0x9 == _0x2332d2 ? (i_rrYppt(_0x3628("0x30f"), !0x0), i_rrYppt("识别到POV摇杆，已自动设置好所有方向😁", !0x0), _0x1f7a5b["blur"](), i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x1, i_WmyDOu["GPSetx"]["KeyUp"] = 0x9, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")] = 0x9, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] = 0x9, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] = 0x9, void i_dhBiu_(_0x3628("0x25"), function (_0x4bbf13) {
            _0x3628("0x488") != i_Brhwo_(_0x4bbf13) && _0x3628("0x491") != i_Brhwo_(_0x4bbf13) && _0x3628("0x74") != i_Brhwo_(_0x4bbf13) && _0x3628("0x4b1") != i_Brhwo_(_0x4bbf13) || (_0x4bbf13[_0x3628("0x32d")] = 0x9), _0x3628("0x28a") == i_Brhwo_(_0x4bbf13) && setTimeout(function () {
              _0x4bbf13["focus"]();
            }, 0xc8);
          })) : (i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x1, i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x1f7a5b)] = _0x2332d2, _0x1f7a5b[_0x3628("0x1a9")](), void setTimeout(function () {
            $(_0x1f7a5b)[_0x3628("0x29a")]()["focus"]();
          }, 0xc8));
          _0x8e9495 = setTimeout(_0x5c0e93, 0x14);
        } else _0x1f7a5b[_0x3628("0x1a9")]();
      }, 0x14);
    }), _0x1f7a5b[_0x3628("0x314")](_0x3628("0x1a9"), function () {
      _0x1f7a5b[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x1f7a5b)], clearInterval(_0x8e9495);
    });
  });
}
function i_iNXiKn() {
  var _0x3d27b8 = document["getElementById"](_0x3628("0x362")),
    _0x414994 = document["getElementById"](_0x3628("0x3d2"));
  _0x3d27b8[_0x3628("0x314")](_0x3628("0x440"), function () {
    var _0xbbba49 = 0x8 + 1.3 * Number(0x64 * (_0x3d27b8[_0x3628("0x32d")] - _0x3d27b8[_0x3628("0x359")]) / (_0x3d27b8[_0x3628("0x1d1")] - _0x3d27b8[_0x3628("0x359")]));
    _0x414994["innerHTML"] = _0x3628("0x146") + _0x3d27b8[_0x3628("0x32d")] + _0x3628("0x69"), _0x414994[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x24") + _0xbbba49 + _0x3628("0x3e9"), i_WmyDOu[_0x3628("0x2a3")] = _0x3d27b8[_0x3628("0x32d")];
  });
}
function i_mKJEOn() {
  i_tMMkpa(), i_yQQtma();
}
var i_QnKMXn = null;
function i_ijxeRn() {
  i_ZKQCf_("AutoData", "focus", function (_0x153d8b) {
    i_QnKMXn = this, i_jzyY__(_0x3628("0x173"), _0x3628("0x7f")), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x7f"));
  }), document[_0x3628("0x566")](_0x3628("0x14d"))["forEach"](function (_0x375819) {
    _0x375819[_0x3628("0x456")][_0x3628("0x314")](_0x3628("0x4ab"), function (_0x480c78) {
      if (null != i_QnKMXn) {
        var _0x49384a = this[_0x3628("0x431")];
        switch (i_Brhwo_(_0x49384a)) {
          case "清":
            i_QnKMXn[_0x3628("0x32d")] = "";
            break;
          case "删":
            break;
          case "等":
          default:
            i_QnKMXn[_0x3628("0x32d")] += i_Brhwo_(_0x49384a);
        }
        i_QnKMXn[_0x3628("0x30e")](), i_QnKMXn["setSelectionRange"](i_QnKMXn[_0x3628("0x32d")][_0x3628("0x288")], i_QnKMXn[_0x3628("0x32d")]["length"]), i_HtHnda("g" + i_QnKMXn["id"], i_QnKMXn[_0x3628("0x32d")]), i_yQQtma();
      } else i_iZhRvt("请先选择要设置的技能");
    });
  });
}
function i_SxJbzn() {
  i_tXrZr_(_0x3628("0x33a"), _0x3628("0x2fd"), function () {
    i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")] = this["checked"];
  }), i_tXrZr_(_0x3628("0x4e4"), "change", function () {
    if (_0x3628("0x27f") === _0x3628("0x20b")) {
      i_zXJHnu(i_cZwcUl["Vn"], 0x0, 0x0);
    } else {
      i_WmyDOu[_0x3628("0xe1")][_0x3628("0x33f")] = this[_0x3628("0x206")];
    }
  });
}
function i_iTBeIn() {
  i_sxfci_("SP_A")[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x303")], i_sxfci_(_0x3628("0x529"))[_0x3628("0x206")] = i_WmyDOu["gBSuper"], i_sxfci_(_0x3628("0x477"))["checked"] = i_WmyDOu[_0x3628("0x247")], i_sxfci_("SP_D")[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x447")], i_sxfci_(_0x3628("0x540"))[_0x3628("0x206")] = i_WmyDOu["gESuper"], i_sxfci_(_0x3628("0x38"))[_0x3628("0x206")] = i_WmyDOu["gFSuper"], i_sxfci_(_0x3628("0x2d7"))[_0x3628("0x27e")] = i_WmyDOu[_0x3628("0x7e")], i_sxfci_(_0x3628("0x41d"))[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0x7e")];
}
function i_bNCXNn() {
  i_sxfci_(_0x3628("0xcf"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x192")];
}
function i_AeGJUn() {
  i_ZKQCf_(_0x3628("0x571"), _0x3628("0x2fd"), function () {
    switch (this["id"]) {
      case _0x3628("0x207"):
        i_WmyDOu[_0x3628("0x303")] = this[_0x3628("0x206")];
        break;
      case _0x3628("0x529"):
        i_WmyDOu[_0x3628("0x2ed")] = this["checked"];
        break;
      case _0x3628("0x477"):
        i_WmyDOu["gCSuper"] = this[_0x3628("0x206")];
        break;
      case _0x3628("0x39b"):
        i_WmyDOu[_0x3628("0x447")] = this[_0x3628("0x206")];
        break;
      case _0x3628("0x540"):
        i_WmyDOu["gESuper"] = this["checked"];
        break;
      case _0x3628("0x38"):
        i_WmyDOu["gFSuper"] = this[_0x3628("0x206")];
    }
  }), i_KiSwBn();
}
function i_DByKDn() {
  i_sxfci_(_0x3628("0x523"))[_0x3628("0x27e")] = i_WmyDOu[_0x3628("0x2ba")];
}
function i_BCXi$n() {
  i_tXrZr_(_0x3628("0x86"), _0x3628("0x440"), function (_0x457093) {
    i_sxfci_(_0x3628("0x523"))[_0x3628("0x27e")] = this["value"], i_WmyDOu[_0x3628("0x2ba")] = parseInt(this[_0x3628("0x32d")]);
  });
}
function i_KiSwBn() {
  i_tXrZr_("AutoSpeed", "input", function (_0x211154) {
    i_sxfci_(_0x3628("0x2d7"))[_0x3628("0x27e")] = this[_0x3628("0x32d")], i_WmyDOu[_0x3628("0x7e")] = parseInt(this[_0x3628("0x32d")]), i_XFSipu(i_WmyDOu[_0x3628("0x7e")]);
  });
}
function i_YQtSGn() {
  i_Hfbes_(_0x3628("0x511"), function () {
    if ("nuINK" === _0x3628("0x3")) {
      "sgqbws_xs" == i_wNEMe["id"] && (i_TxyTOe = i_wNEMe[_0x3628("0x206")]), "sgqbws_xst" == i_wNEMe["id"] && (i_stXiNe = i_wNEMe["checked"]), _0x3628("0x4fe") == i_wNEMe["id"] && (i_ctdwXe = i_wNEMe["checked"]), _0x3628("0x35e") == i_wNEMe["id"] && (i_KbnfRe = i_wNEMe[_0x3628("0x206")]), _0x3628("0x25c") == i_wNEMe["id"] && (i_fEyRze = i_wNEMe[_0x3628("0x206")]), _0x3628("0x6f") == i_wNEMe["id"] && (i_DJxhIe = i_wNEMe[_0x3628("0x206")]), "sgqbws_rankwj" == i_wNEMe["id"] && i_Kbzklo == i_emcHro && i_wNEMe["checked"] && (cyc_queryID(_0x3628("0x30"))[_0x3628("0x306")] = (0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")])) : (i_hnXPpo("message", "【随机武将开启】"), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)), !0x0)), _0x3628("0x23e") == i_wNEMe["id"] && i_Kbzklo == i_emcHro && i_wNEMe[_0x3628("0x206")] && (cyc_queryID(_0x3628("0x23e"))[_0x3628("0x306")] = (0x0 == i_iDpHde ? (i_kJQyVe[0x3] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe["buffer"])) : (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x144")), i_kJQyVe[0x3] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)), !0x0)), "sgqbws_study" == i_wNEMe["id"] && i_Kbzklo == i_emcHro && i_wNEMe["checked"] && (cyc_queryID(_0x3628("0x391"))["disabled"] = (0x1 == i_iDpHde ? (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x8")), i_kJQyVe[0x1] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)) : (i_kJQyVe[0x1] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe[_0x3628("0x37a")])), !0x0)), "sgqbws_dead" == i_wNEMe["id"] && i_Kbzklo == i_emcHro && i_wNEMe[_0x3628("0x206")] && (cyc_queryID(_0x3628("0x46"))[_0x3628("0x306")] = (0x1 == i_iDpHde ? (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x60")), i_kJQyVe[0x2] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe)) : (i_kJQyVe[0x2] = 0xb4, i_DYNBTe["postMessage"](i_kJQyVe[_0x3628("0x37a")])), !0x0)), _0x3628("0x25b") == i_wNEMe["id"] && i_Kbzklo == i_emcHro && i_wNEMe[_0x3628("0x206")] && (cyc_queryID(_0x3628("0x25b"))["disabled"] = (0x0 == i_iDpHde ? (i_kJQyVe[0x0] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")])) : (i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x183")), i_kJQyVe[0x0] = i_EHEBeo + 0xb4, i_sCrNao["send"](i_kJQyVe)), !0x0)), "sgqbqx_xs" == i_wNEMe["id"] && (i_YrMEUe = i_wNEMe["checked"]), _0x3628("0x106") == i_wNEMe["id"] && (i_wemjDe = i_wNEMe["checked"]), _0x3628("0x345") == i_wNEMe["id"] && (i_MkXN$e = i_wNEMe[_0x3628("0x206")]), _0x3628("0x177") == i_wNEMe["id"] && (i_SGZcBe = i_wNEMe[_0x3628("0x206")]), "sgqbpl_xs" == i_wNEMe["id"] && (i_QcpMGe = i_wNEMe[_0x3628("0x206")]), _0x3628("0x4aa") == i_wNEMe["id"] && (i_rRKjFe = i_wNEMe["checked"]), _0x3628("0x3af") == i_wNEMe["id"] && (i_yhsMLe = i_wNEMe[_0x3628("0x206")]), _0x3628("0x4c7") == i_wNEMe["id"] && (i_Rwcaqe = i_wNEMe[_0x3628("0x206")]);
    } else {
      i_WwiRv_(_0x3628("0xdb"), _0x3628("0x8c"));
    }
  }), i_tXrZr_(_0x3628("0x3ea"), _0x3628("0x111"), function () {
    i_sxfci_("reversalBox")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
  }), i_Hfbes_(_0x3628("0xe"), function (_0xa0c93) {
    $(this)[_0x3628("0xd3")](_0x3628("0x30a"))[_0x3628("0xe0")](_0x3628("0x117")), $(this)["addClass"](_0x3628("0x117")), $(_0x3628("0x273"))[_0x3628("0x12")](_0x3628("0x3b5"), "flex"), $("#keybind_gamepad")[_0x3628("0x12")]("display", _0x3628("0x47e")), i_cJzsbn = !0x1, i_Bckxkn();
  }), i_Hfbes_(_0x3628("0x160"), function (_0x3fdcc1) {
    $(this)["siblings"](_0x3628("0x30a"))[_0x3628("0xe0")](_0x3628("0x117")), $(this)["addClass"](_0x3628("0x117")), $(_0x3628("0x2b9"))["css"](_0x3628("0x3b5"), _0x3628("0x3dd")), $(_0x3628("0x273"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e")), i_cJzsbn = !0x0, i_Bckxkn();
  }), i_Hfbes_("tab_keyext", function (_0x1bd476) {
    $(this)[_0x3628("0xd3")](_0x3628("0x30a"))[_0x3628("0xe0")](_0x3628("0x195")), $(this)["addClass"](_0x3628("0x195")), $(_0x3628("0x223"))[_0x3628("0x12")]("display", "block"), $(_0x3628("0x8f"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e")), $("#keybind_etc")["css"](_0x3628("0x3b5"), "none");
  }), i_Hfbes_(_0x3628("0xa5"), function (_0x1b466f) {
    $(this)[_0x3628("0xd3")](_0x3628("0x30a"))[_0x3628("0xe0")](_0x3628("0x195")), $(this)[_0x3628("0x32")]("active-kbdext"), $(_0x3628("0x223"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e")), $("#keybind_auto")[_0x3628("0x12")]("display", _0x3628("0x8c")), $(_0x3628("0x2a9"))["css"](_0x3628("0x3b5"), _0x3628("0x47e"));
  }), i_Hfbes_(_0x3628("0x16a"), function (_0x58eefc) {
    if (_0x3628("0x1b3") === _0x3628("0x79")) {
      var _0x140b4d = i_sxfci_(_0x3628("0x128")),
        _0x389cb2 = document[_0x3628("0x3fe")]("div"),
        _0x12c8a9 = i_eWxTd[_0x3628("0x16d")](_0x3628("0x55b"), i_scDZc)[_0x3628("0x16d")](_0x3628("0x99"), "cg" + i_scDZc)[_0x3628("0x16d")]("{{id}}", "cg" + i_scDZc),
        _0x4f05b5 = "";
      for (var _0x4b5a4 in i_Qkwrs) _0x4f05b5 += _0x3628("0x3c9") + _0x4b5a4 + "\x22>" + _0x4b5a4 + _0x3628("0x39e");
      _0x12c8a9 = _0x12c8a9[_0x3628("0x16d")]("{{someopt}}", _0x4f05b5), _0x389cb2[_0x3628("0x24d")] = _0x12c8a9, _0x140b4d[_0x3628("0xd6")](_0x389cb2), i_tXrZr_("cg" + i_scDZc, _0x3628("0x2fd"), function (_0x1418de) {
        if (null != i_rzpKf[i_scDZc]) for (var _0x284bc5 in i_Qkwrs[i_rzpKf[i_scDZc]][_0x3628("0x2e7")]) {
          for (var _0x26ce22 = i_Qkwrs[i_rzpKf[i_scDZc]][_0x3628("0x2e7")][_0x284bc5], _0x1b2c0d = i_Qkwrs[i_rzpKf[i_scDZc]]["ID"], _0x1c6976 = _0x26ce22, _0x506cbf = 0x0; _0x506cbf < _0x1c6976[_0x3628("0xe6")]; _0x506cbf++) i_iXBb_[_0x506cbf] = parseInt(_0x1c6976[_0x3628("0x32d")]["substr"](0x2 * _0x506cbf, 0x2), 0x10);
          i_aZwsau({
            type: _0x3628("0x2f2"),
            onoff: 0x0,
            cheatID: _0x1b2c0d,
            cheatAddr: parseInt(_0x1c6976[_0x3628("0x38c")], 0x10),
            cheatSize: _0x1c6976[_0x3628("0xe6")],
            cheatData: i_iXBb_
          });
        }
        for (var _0x284bc5 in i_rzpKf[i_scDZc] = this[_0x3628("0x32d")], i_Qkwrs[this[_0x3628("0x32d")]]["data"]) {
          for (_0x26ce22 = i_Qkwrs[this["value"]][_0x3628("0x2e7")][_0x284bc5], _0x1b2c0d = i_Qkwrs[this[_0x3628("0x32d")]]["ID"], _0x1c6976 = _0x26ce22, _0x506cbf = 0x0; _0x506cbf < _0x1c6976[_0x3628("0xe6")]; _0x506cbf++) i_iXBb_[_0x506cbf] = parseInt(_0x1c6976[_0x3628("0x32d")][_0x3628("0x26a")](0x2 * _0x506cbf, 0x2), 0x10);
          i_aZwsau({
            type: _0x3628("0x2f2"),
            onoff: 0x1,
            cheatID: _0x1b2c0d,
            cheatAddr: parseInt(_0x1c6976[_0x3628("0x38c")], 0x10),
            cheatSize: _0x1c6976[_0x3628("0xe6")],
            cheatData: i_iXBb_
          });
        }
      });
    } else {
      $(this)[_0x3628("0xd3")](_0x3628("0x30a"))["removeClass"]("active-kbdext"), $(this)[_0x3628("0x32")]("active-kbdext"), $(_0x3628("0x223"))[_0x3628("0x12")]("display", "none"), $(_0x3628("0x8f"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e")), $(_0x3628("0x2a9"))[_0x3628("0x12")](_0x3628("0x3b5"), "block");
    }
  }), i_FTQZMn(), i_CmAEPn(0x1), i_CmAEPn(0x2), i_CmAEPn(0x3), i_CmAEPn(0x4), i_CmAEPn(0x5), i_CmAEPn(0x6), i_AeGJUn(), i_BCXi$n(), i_NXeiEn(), i_FtZiCn(), i_iNXiKn(), i_SxJbzn(), i_ysQfgn();
}
var i_cbdGFn = null;
function i_JtyhLn(_0x19d41c, _0x431aff) {
  var _0x182f6a = i_sxfci_("p" + _0x19d41c + _0x3628("0x1b1"));
  null != _0x182f6a && (_0x182f6a[_0x3628("0x284")] = _0x431aff + "ms", _0x182f6a[_0x3628("0x3e")]["color"] = _0x431aff < 0x32 ? _0x3628("0x10a") : _0x431aff < 0x50 ? _0x3628("0x12a") : _0x3628("0x55d"));
}
function i_zkHZqn() {
  0x0 < i_aMrpot[_0x3628("0x2f3")](_0x3628("0x1da")) ? i_sxfci_(_0x3628("0x499"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c") : i_iZhRvt("联机已掉线，本场已变为单机模式，请重新连接");
}
function i_SCfmVn(_0x177515, _0x52c92f, _0x574630) {
  for (var _0x434977 in _0x177515[_0x3628("0x24d")] = "", _0x574630[_0x3628("0x35a")]) {
    var _0x5d7eea = document[_0x3628("0x3fe")](_0x3628("0x30a"));
    _0x5d7eea[_0x3628("0x107")]("tooltip", _0x574630["Badge"][_0x434977][_0x3628("0xbf")]), _0x5d7eea[_0x3628("0x3e")][_0x3628("0x9")] = _0x3628("0x49"), _0x5d7eea["style"]["marginLeft"] = _0x3628("0x1dd");
    var _0x1de5e5 = document[_0x3628("0x3fe")](_0x3628("0x265"));
    _0x1de5e5[_0x3628("0x1c5")] = _0x574630[_0x3628("0x35a")][_0x434977][_0x3628("0x3bd")], _0x5d7eea[_0x3628("0xd6")](_0x1de5e5), _0x177515[_0x3628("0xd6")](_0x5d7eea);
  }
  (_0x52c92f[_0x3628("0x1c5")] = "" == _0x574630[_0x3628("0x3c1")][_0x3628("0x3bd")]) ? _0x52c92f[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e") : (_0x52c92f[_0x3628("0x1c5")] = _0x574630[_0x3628("0x3c1")][_0x3628("0x3bd")], _0x52c92f[_0x3628("0x3e")][_0x3628("0x3b5")] = "block");
}
function i_QNbFjn(_0x3e1017) {
  if (null != _0x3e1017) {
    i_cbdGFn = _0x3e1017, i_sxfci_("p1img")["src"] = _0x3e1017[_0x3628("0x4af")], i_sxfci_(_0x3628("0x262"))[_0x3628("0x1c5")] = _0x3e1017[_0x3628("0x34e")], i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = _0x3e1017[_0x3628("0x4e7")], i_sxfci_(_0x3628("0x45a"))[_0x3628("0x1c5")] = _0x3e1017[_0x3628("0x3a5")], i_sxfci_(_0x3628("0x2cb"))[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x3a4")], i_sxfci_("p2nick")[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x331")], i_sxfci_(_0x3628("0x521"))[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x2de")], i_sxfci_(_0x3628("0x54e"))[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x4cd")], 0x0 < _0x3e1017[_0x3628("0x3a4")][_0x3628("0x2f3")]("关闭") && (i_sxfci_("p1img")[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < _0x3e1017["Nick2"][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x262"))["src"] = _0x3628("0x3ac")), 0x0 < _0x3e1017[_0x3628("0x2de")][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < _0x3e1017[_0x3628("0x4cd")][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x45a"))["src"] = _0x3628("0x3ac")), _0x3e1017[_0x3628("0x2a1")] ? (i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0xc2")), i_sxfci_("p1devtip")["setAttribute"](_0x3628("0x142"), "手机玩家")) : (i_sxfci_(_0x3628("0x2a4"))["classList"][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")]["remove"](_0x3628("0xc2")), i_sxfci_("p1devtip")[_0x3628("0x107")](_0x3628("0x142"), _0x3628("0x542"))), _0x3e1017[_0x3628("0x20e")] ? (i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x47a")]("igwi-diannao-copy"), i_sxfci_(_0x3628("0x149"))["classList"][_0x3628("0x405")](_0x3628("0xc2")), i_sxfci_(_0x3628("0x526"))["setAttribute"](_0x3628("0x142"), _0x3628("0x17d"))) : (i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xc2")), i_sxfci_("p2devtip")[_0x3628("0x107")](_0x3628("0x142"), _0x3628("0x542")));
    var _0x33a3e4 = _0x3e1017[_0x3628("0x76")],
      _0x32f130 = i_sxfci_(_0x3628("0x23b"));
    0x0 == _0x33a3e4 ? (_0x32f130[_0x3628("0x3e")]["left"] = _0x3628("0xc"), _0x32f130[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x3e8")) : 0x1 == _0x33a3e4 ? (_0x32f130["style"][_0x3628("0x282")] = _0x3628("0x1ad"), _0x32f130[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x3e8")) : 0x2 == _0x33a3e4 ? (_0x32f130[_0x3628("0x3e")]["left"] = "90px", _0x32f130[_0x3628("0x3e")]["top"] = _0x3628("0x465")) : 0x3 == _0x33a3e4 && (_0x32f130[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x1ad"), _0x32f130["style"][_0x3628("0x270")] = "107px"), i_sxfci_("lookcount")[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x41b")];
    var _0x1ddf66 = i_sxfci_(_0x3628("0x3b"));
    for (var _0x1c9918 in _0x1ddf66["innerHTML"] = "", _0x3e1017[_0x3628("0x24a")]) {
      var _0x5c7c37 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
      _0x5c7c37[_0x3628("0x107")]("key", _0x3e1017[_0x3628("0x397")][_0x1c9918]), _0x5c7c37[_0x3628("0x27e")] = _0x3e1017[_0x3628("0x24a")][_0x1c9918], _0x1ddf66["appendChild"](_0x5c7c37);
    }
    null != _0x3e1017["UUID"] && ("" != _0x3e1017["UUID"][0x0] ? i_TrjcCu(-0x1, _0x3e1017[_0x3628("0x307")][0x0], function (_0x3fb87) {
      i_sxfci_(_0x3628("0x50b"))[_0x3628("0x284")] = "Lv." + _0x3fb87["LevelInfo"][_0x3628("0x48f")], i_SCfmVn(i_sxfci_("p1badge"), i_sxfci_(_0x3628("0xf8")), _0x3fb87[_0x3628("0xd4")]);
    }) : (i_sxfci_(_0x3628("0x50b"))[_0x3628("0x284")] = _0x3628("0x4a9"), i_sxfci_(_0x3628("0xf8"))[_0x3628("0x3e")]["display"] = _0x3628("0x47e"), i_sxfci_(_0x3628("0x2a"))[_0x3628("0x24d")] = "", i_sxfci_("p1nick")[_0x3628("0x4a8")] = "touxiangname"), "" != _0x3e1017["UUID"][0x1] ? i_TrjcCu(-0x1, _0x3e1017["UUID"][0x1], function (_0x31ef50) {
      if (_0x3628("0x67") !== _0x3628("0x67")) {
        if (-0x1 == i_eRzm$s) {
          "NO" == (_0x139212 = (_0x139212 = (_0x139212 = i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(i_Ydpze)][_0x3628("0x16d")](_0x3628("0xd5"), ""))[_0x3628("0x16d")](_0x3628("0x1e2"), ""))[_0x3628("0x16d")](_0x3628("0x2f8"), "")) && (_0x139212 = "空"), i_Ydpze[_0x3628("0x161")][0x1][_0x3628("0x27e")] = _0x139212;
        } else {
          var _0x139212 = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(i_Ydpze)];
          i_Ydpze[_0x3628("0x161")][0x1][_0x3628("0x27e")] = _0x139212;
        }
      } else {
        i_sxfci_(_0x3628("0x51"))[_0x3628("0x284")] = "Lv." + _0x31ef50[_0x3628("0x31")]["Level"], i_SCfmVn(i_sxfci_(_0x3628("0x224")), i_sxfci_(_0x3628("0x433")), _0x31ef50[_0x3628("0xd4")]);
      }
    }) : (i_sxfci_(_0x3628("0x51"))[_0x3628("0x284")] = _0x3628("0x4a9"), i_sxfci_(_0x3628("0x433"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_sxfci_(_0x3628("0x224"))[_0x3628("0x24d")] = "", i_sxfci_("p2nick")["className"] = "touxiangname"), "" != _0x3e1017[_0x3628("0x307")][0x2] ? i_TrjcCu(-0x1, _0x3e1017["UUID"][0x2], function (_0x2e65c2) {
      i_sxfci_(_0x3628("0x198"))[_0x3628("0x284")] = _0x3628("0x109") + _0x2e65c2[_0x3628("0x31")][_0x3628("0x48f")], i_SCfmVn(i_sxfci_(_0x3628("0x573")), i_sxfci_(_0x3628("0x33b")), _0x2e65c2[_0x3628("0xd4")]);
    }) : (i_sxfci_(_0x3628("0x198"))[_0x3628("0x284")] = _0x3628("0x4a9"), i_sxfci_(_0x3628("0x33b"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_sxfci_("p3badge")[_0x3628("0x24d")] = "", i_sxfci_(_0x3628("0x521"))[_0x3628("0x4a8")] = "touxiangname"), "" != _0x3e1017["UUID"][0x3] ? i_TrjcCu(-0x1, _0x3e1017[_0x3628("0x307")][0x3], function (_0x25c598) {
      i_sxfci_(_0x3628("0x51d"))["textContent"] = _0x3628("0x109") + _0x25c598[_0x3628("0x31")]["Level"], i_SCfmVn(i_sxfci_(_0x3628("0x1ed")), i_sxfci_(_0x3628("0xb5")), _0x25c598[_0x3628("0xd4")]);
    }) : (i_sxfci_(_0x3628("0x51d"))[_0x3628("0x284")] = _0x3628("0x4a9"), i_sxfci_(_0x3628("0xb5"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_sxfci_(_0x3628("0x1ed"))[_0x3628("0x24d")] = "", i_sxfci_(_0x3628("0x54e"))[_0x3628("0x4a8")] = _0x3628("0x145")));
  }
}
function i_eGBAYn(_0xbdafbe) {}
function i_DDAeHn(_0x40f986) {
  var _0x2890e2 = i_sxfci_(_0x3628("0x212"));
  switch (_0x40f986) {
    case _0x3628("0x5"):
      _0x2890e2["childNodes"][0x0][_0x3628("0xea")][_0x3628("0x16d")]("igwi-zanting", _0x3628("0x423")), _0x2890e2[_0x3628("0x3e")][_0x3628("0x45f")] = _0x3628("0x2ce"), _0x2890e2[_0x3628("0x161")][0x1][_0x3628("0x284")] = _0x3628("0x135");
      break;
    case _0x3628("0x537"):
      _0x2890e2[_0x3628("0x161")][0x0]["classList"][_0x3628("0x16d")](_0x3628("0x423"), "igwi-zanting"), _0x2890e2[_0x3628("0x3e")]["background"] = _0x3628("0x66"), _0x2890e2[_0x3628("0x161")][0x1][_0x3628("0x284")] = _0x3628("0x48b");
  }
}
var i_NrsAWn = _0x3628("0x1"),
  i_idKiJn = _0x3628("0x234"),
  i_fJJeQn = _0x3628("0x3f9"),
  i_wpCDZn = _0x3628("0x176"),
  i_QEbyet = "\x20\x20<div\x20class=\x22chatmessage\x20mymsg\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22msgtime\x22>{{time}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20class=\x22chatnick\x22>{{nick}}</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span\x20class=\x22prefix\x22></span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22msgtext\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{text}}\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>",
  i_rynWnt = _0x3628("0x33c"),
  i_JsQStt = "\x20\x20<div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20id=\x27{{lookerid}}\x27>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{nick}}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>",
  i_xMcxat = !0x0,
  i_KXdcit = null,
  i_aMrpot = "";
function i_Hrkkct() {
  "1" == i_tdBda_("look") || 0x1 != i_iDpHde ? i_sxfci_(_0x3628("0x3ef"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e") : i_Hfbes_(_0x3628("0x3ef"), function () {
    if (_0x3628("0x563") !== _0x3628("0x1d4")) {
      window["location"][_0x3628("0x16d")]("https://play.wo1wan.com/fcnext/play?&look=1&id=" + gid + _0x3628("0x360") + i_wesTbe + _0x3628("0x18f") + i_ZmCwpe);
    } else {
      i_iFrkZo = !0x1, i_SkYMmc();
    }
  });
}
function i_xjiCst() {
  null == (i_KXdcit = i_sxfci_("messagelist"))[_0x3628("0x4db")] && (i_KXdcit[_0x3628("0x4db")] = function (_0x40f482, _0x177d2c) {
    i_KXdcit[_0x3628("0x373")] = _0x177d2c, i_KXdcit[_0x3628("0xcc")] = _0x40f482;
  }), i_Hfbes_(_0x3628("0x4f1"), i_kJmZht), i_tXrZr_(_0x3628("0x3b"), "mouseup", i_TxBwlt), i_tXrZr_(_0x3628("0x4a0"), _0x3628("0x111"), i_aQWcut), i_cirYl_(_0x3628("0x138"), i_iJHfft), i_Hfbes_(_0x3628("0x3e5"), function () {
    if (_0x3628("0x301") !== _0x3628("0x4f0")) {
      i_xMcxat = !0x1, i_sxfci_(_0x3628("0x4b0"))[_0x3628("0x3e")]["opacity"] = _0x3628("0x1ac");
    } else {
      var _0x5ceb67 = i_hChie[_0x3628("0x3cd")]();
      if (i_hChie[_0x3628("0x100")](_0x5ceb67, i_srnhn), i_hChie[_0x3628("0x100")](_0x5ceb67, i_Nccrt), i_hChie[_0x3628("0x543")](_0x5ceb67), !i_hChie[_0x3628("0x3d5")](_0x5ceb67, i_hChie["LINK_STATUS"])) throw new Error("program\x20filed\x20to\x20link:" + i_hChie[_0x3628("0x11d")](_0x5ceb67));
      return _0x5ceb67;
    }
  }), i_Hrkkct(), setTimeout(function () {
    if ("yTEli" !== _0x3628("0x18d")) {
      this["style"][_0x3628("0x1c8")] = this["value"] + "%\x20100%", i_sxfci_("layoutsizetxt")[_0x3628("0x27e")] = (0x32 + 0x96 * this[_0x3628("0x32d")] / 0x64)[_0x3628("0x249")](0x0) + "%", i_CByehs((0x32 + 0x96 * this[_0x3628("0x32d")] / 0x64) / 0x64);
    } else {
      i_rrYppt(_0x3628("0x2"), !0x0);
    }
  }, 0x3e8);
}
var i_aemzrt = null;
function i_TxBwlt(_0x14e848) {
  if (0x2 == _0x14e848[_0x3628("0x305")] && 0x1 == i_iDpHde) {
    if (_0x3628("0x1f2") === "SBiix") {
      var _0x36bf07 = i_sxfci_(_0x3628("0x4a0"));
      i_aemzrt = _0x14e848[_0x3628("0x2f9")], _0x36bf07["style"][_0x3628("0x118")] = 0x1, _0x36bf07[_0x3628("0x3e")][_0x3628("0x282")] = _0x14e848[_0x3628("0x325")] - 0xf + "px", _0x36bf07[_0x3628("0x3e")][_0x3628("0x270")] = _0x14e848["clientY"] - 0xf + "px";
    } else {
      var _0xb59c46 = i_EbKSe[_0x3628("0x44b")](i_sxJpt);
      if (i_EbKSe[_0x3628("0x55")](_0xb59c46, i_nFjXn), i_EbKSe[_0x3628("0x4f2")](_0xb59c46), !i_EbKSe[_0x3628("0x1d3")](_0xb59c46, i_EbKSe["COMPILE_STATUS"])) throw new Error(_0x3628("0x254") + i_EbKSe["getShaderInfoLog"](_0xb59c46));
      return _0xb59c46;
    }
  }
}
function i_aQWcut() {
  var _0x5ee7c1 = i_sxfci_("messagemenu");
  _0x5ee7c1[_0x3628("0x3e")]["opacity"] = 0x0, setTimeout(function () {
    _0x5ee7c1[_0x3628("0x3e")][_0x3628("0x270")] = "-200px";
  }, 0x64);
}
function i_iJHfft(_0x10f50b) {
  var _0x46b133 = i_sxfci_(_0x3628("0x4a0")),
    _0x285ea3 = i_Brhwo_(i_aemzrt);
  switch (this["id"]) {
    case _0x3628("0x352"):
      i_TrjcCu(-0x1, _0x285ea3, function (_0x34a812) {
        i_rrYppt(_0x34a812[_0x3628("0x289")] + "\x20的UID是：" + _0x34a812[_0x3628("0x187")]), i_sxfci_(_0x3628("0x136"))[_0x3628("0x4ab")]();
      });
      break;
    case _0x3628("0x334"):
      i_pjQNyo() ? $[_0x3628("0x341")](_0x3628("0x369"), {
        who: _0x285ea3,
        where: "jjin_" + gid + "_" + i_ZmCwpe,
        say: ""
      }, function (_0x569485) {
        if ("cvyyO" === "cvyyO") {
          "ok" == _0x569485[_0x3628("0x446")] ? i_hnXPpo(_0x3628("0x2a5"), i_aemzrt[_0x3628("0x27e")] + _0x3628("0xfe")) : i_iZhRvt(_0x3628("0x16b"));
        } else {
          if (this[_0x3628("0x206")]) {
            if (i_Kbzklo != i_emcHro) return i_iZhRvt(_0x3628("0x5a")), void (this[_0x3628("0x206")] = !0x1);
            0x1 == i_iDpHde ? (i_hnXPpo("message", "【敌人不动开启】"), i_kJQyVe[0x2] = i_EHEBeo + 0xb4, i_sCrNao[_0x3628("0x559")](i_kJQyVe), i_sxfci_(_0x3628("0x320"))[_0x3628("0x306")] = !0x0) : (i_kJQyVe[0x2] = 0xb4, i_DYNBTe[_0x3628("0x3ab")](i_kJQyVe[_0x3628("0x37a")]), i_sxfci_(_0x3628("0x320"))[_0x3628("0x306")] = !0x0, i_GJQKdt(_0x3628("0x60")));
          }
        }
      }) : i_iZhRvt(_0x3628("0xf9")), i_sxfci_(_0x3628("0x136"))["click"]();
      break;
    case _0x3628("0x3d3"):
      i_hnXPpo(_0x3628("0x237"), _0x285ea3);
  }
  _0x46b133[_0x3628("0x3e")][_0x3628("0x118")] = 0x0, setTimeout(function () {
    _0x46b133[_0x3628("0x3e")]["top"] = _0x3628("0x49c");
  }, 0x64);
}
function i_HsQF_t(_0x285f60) {
  _0x285f60[_0x3628("0x373")] + _0x285f60["clientHeight"] + 0x96 > _0x285f60["scrollHeight"] && _0x285f60[_0x3628("0x4db")](0x0, _0x285f60["scrollHeight"]);
}
function i_iZhRvt(_0x5d1d88) {
  var _0xf74e97 = document["createElement"](_0x3628("0x30a"));
  _0xf74e97["innerHTML"] = i_NrsAWn[_0x3628("0x16d")](_0x3628("0x10"), _0x5d1d88), i_KXdcit["appendChild"](_0xf74e97), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit[_0x3628("0xb9")]), i_aMrpot = _0x5d1d88;
}
function i_GJQKdt(_0x46b925) {
  _0x46b925 = _0x46b925 + "\x20" + new Date()[_0x3628("0x1fb")]();
  var _0x460608 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x460608[_0x3628("0x24d")] = i_idKiJn[_0x3628("0x16d")]("{{text}}", _0x46b925), i_KXdcit[_0x3628("0xd6")](_0x460608), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit[_0x3628("0xb9")]), i_aMrpot = _0x46b925;
}
function i_rrYppt(_0x499565, _0x21e18e) {
  !0x0 !== _0x21e18e && (_0x499565 = _0x499565 + "\x20" + new Date()[_0x3628("0x1fb")]());
  var _0x42725 = document["createElement"](_0x3628("0x30a"));
  _0x42725[_0x3628("0x24d")] = i_fJJeQn[_0x3628("0x16d")](_0x3628("0x10"), _0x499565), i_KXdcit["appendChild"](_0x42725), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit[_0x3628("0xb9")]), i_aMrpot = _0x499565;
}
function i_jpPbmt(_0x502a81) {
  var _0x20c5d7 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x20c5d7[_0x3628("0x24d")] = i_QEbyet[_0x3628("0x16d")]("{{text}}", _0x502a81)[_0x3628("0x16d")](_0x3628("0xeb"), i_DdTaQf[_0x3628("0x289")])[_0x3628("0x16d")](_0x3628("0x19"), new Date()[_0x3628("0x1fb")]()), i_KXdcit[_0x3628("0xd6")](_0x20c5d7), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit["scrollHeight"]);
}
function i_fEncgt(_0x1ff8fb, _0x490b68, _0x3fabd1, _0x5a922a) {
  var _0x34ecdc = !0x1;
  if (i_AZdwyt(), null == _0x1ff8fb && null != _0x3fabd1) {
    var _0x4a75cd = _0x3fabd1[_0x3628("0x2f3")](_0x3628("0x3e0")) + 0x6,
      _0x5c1fc4 = _0x3fabd1[_0x3628("0x2f3")](_0x3628("0x291")) - _0x4a75cd;
    _0x1ff8fb = _0x3fabd1["substr"](_0x4a75cd, _0x5c1fc4), _0x490b68 = _0x3fabd1[_0x3628("0x26a")](_0x3fabd1["indexOf"](_0x3628("0x155")) + 0x7)["replace"]("<br/>", "");
  }
  if (null != _0x3fabd1 && 0x0 < _0x3fabd1["indexOf"]("观战者") && (_0x34ecdc = !0x0), 0x2 == _0x5a922a && (_0x34ecdc = !0x0), _0x1ff8fb == i_DdTaQf[_0x3628("0x289")]) return i_jpPbmt(_0x490b68);
  if (_0x3628("0x528") == _0x1ff8fb) return i_GJQKdt(_0x490b68);
  var _0x1292b3 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x1292b3[_0x3628("0x24d")] = _0x34ecdc ? i_rynWnt[_0x3628("0x16d")]("{{text}}", _0x490b68)[_0x3628("0x16d")]("{{nick}}", _0x1ff8fb)["replace"](_0x3628("0x19"), new Date()["toLocaleTimeString"]()) : i_wpCDZn[_0x3628("0x16d")](_0x3628("0x10"), _0x490b68)[_0x3628("0x16d")](_0x3628("0xeb"), _0x1ff8fb)[_0x3628("0x16d")](_0x3628("0x19"), new Date()["toLocaleTimeString"]()), i_KXdcit[_0x3628("0xd6")](_0x1292b3), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit[_0x3628("0xb9")]);
}
function i_kJmZht() {
  i_CmTmwt();
}
var i_YfmGbt = 0x0;
function i_AZdwyt() {
  0x0 < i_JhpKTt && i_xMcxat && (i_sxfci_(_0x3628("0x4b0"))[_0x3628("0x3e")][_0x3628("0x118")] = _0x3628("0x1b0"), i_YfmGbt = performance[_0x3628("0x30b")]() + 0x1324, setTimeout(function () {
    performance[_0x3628("0x30b")]() > i_YfmGbt && 0x0 < i_JhpKTt && (i_sxfci_(_0x3628("0x4b0"))[_0x3628("0x3e")][_0x3628("0x118")] = _0x3628("0x1ac"));
  }, 0x1388));
}
function i_CmTmwt() {
  if (i_xMcxat = !0x0, i_AZdwyt(), "" != i_sxfci_(_0x3628("0x16c"))["value"]) {
    if (0x0 == i_iDpHde) i_sxfci_(_0x3628("0x16c"))["value"] = "", i_jpPbmt(_0x3628("0xbe"));else {
      if (_0x3628("0x19a") === _0x3628("0x19a")) {
        var _0x4770c1 = i_sxfci_("messageinput")["value"];
        "" != _0x4770c1 && i_hnXPpo(_0x3628("0x2a5"), _0x4770c1), i_sxfci_("messageinput")["value"] = "";
      } else {
        !0x0 !== i_PGtmn && (i_SRkie = i_SRkie + "\x20" + new Date()[_0x3628("0x1fb")]());
        var _0x3869c4 = document["createElement"]("div");
        _0x3869c4[_0x3628("0x24d")] = i_fJJeQn["replace"](_0x3628("0x10"), i_SRkie), i_KXdcit[_0x3628("0xd6")](_0x3869c4), i_KXdcit[_0x3628("0x4db")](0x0, i_KXdcit[_0x3628("0xb9")]), i_aMrpot = i_SRkie;
      }
    }
    i_sxfci_("messageinput")[_0x3628("0x1a9")]();
  } else document[_0x3628("0x279")] == i_sxfci_(_0x3628("0x16c")) ? (i_sxfci_("messageinput")[_0x3628("0x1a9")](), 0x0 < i_JhpKTt && (i_sxfci_("chatpad")[_0x3628("0x3e")]["opacity"] = _0x3628("0x1ac"))) : i_sxfci_(_0x3628("0x16c"))["focus"]();
}
var i_FGtjkt = !0x1,
  i_JhpKTt = 0x0,
  i_JDzaxt = null,
  i_FJfZMt = !0x0,
  i_YWkhPt = 0x1;
function i_KJGWEt() {
  $(_0x3628("0x43f"))["RangeSlider"]({
    min: 0x0,
    max: 0x64,
    step: 0.1,
    i: function (_0x4bde7c) {
      i_rHShLf(_0x4bde7c[_0x3628("0x32d")] / 0x64), i_WmyDOu[_0x3628("0x40d")] = _0x4bde7c[_0x3628("0x32d")];
    }
  }), i_sxfci_(_0x3628("0x37"))[_0x3628("0x314")](_0x3628("0x1a9"), function () {
    i_JcAsUu();
  });
}
function i_NmbbAt() {
  var _0x3cc70e = document[_0x3628("0x148")];
  null == document[_0x3628("0x42e")] ? _0x3cc70e[_0x3628("0x3a9")] ? _0x3cc70e["requestFullscreen"]() : _0x3cc70e[_0x3628("0x4e2")] ? _0x3cc70e[_0x3628("0x4e2")]() : _0x3cc70e[_0x3628("0x10b")] && _0x3cc70e[_0x3628("0x10b")]() : document["exitFullscreen"] ? document["exitFullscreen"]() : document[_0x3628("0x4a7")] ? document[_0x3628("0x4a7")]() : document[_0x3628("0x15d")] && document["webkitExitFullscreen"]();
}
function i_crWxSt() {
  0x2 == i_ZHidMe && (i_sxfci_("p3card")[_0x3628("0x3e")][_0x3628("0x118")] = _0x3628("0x156"), i_sxfci_(_0x3628("0x50d"))["style"][_0x3628("0x118")] = _0x3628("0x156"), i_sxfci_("p3img")[_0x3628("0x1c5")] = "https://static.wo1wan.com/game/close.png", i_sxfci_("p4img")[_0x3628("0x1c5")] = _0x3628("0x23d"), i_sxfci_(_0x3628("0x521"))[_0x3628("0x27e")] = _0x3628("0x169"), i_sxfci_("p4nick")["innerText"] = "不可选", i_sxfci_(_0x3628("0x525"))["addEventListener"]("mouseup", function (_0x1be00c) {
    if (_0x3628("0x544") === _0x3628("0x3d")) {
      i_tXrZr_(_0x3628("0xcf"), _0x3628("0x2fd"), function (_0xbdb61b) {
        i_WmyDOu[_0x3628("0x192")] = this[_0x3628("0x206")];
      });
    } else {
      _0x1be00c[_0x3628("0x3ed")](), _0x1be00c["preventDefault"]();
    }
  }, !0x0), i_sxfci_(_0x3628("0x525"))["addEventListener"](_0x3628("0x4ab"), function (_0x5edf7d) {
    _0x5edf7d[_0x3628("0x3ed")](), _0x5edf7d[_0x3628("0x23")]();
  }, !0x0), i_sxfci_(_0x3628("0x50d"))["addEventListener"](_0x3628("0x275"), function (_0x2e6857) {
    _0x2e6857["stopPropagation"](), _0x2e6857[_0x3628("0x23")]();
  }, !0x0), i_sxfci_(_0x3628("0x50d"))[_0x3628("0x314")](_0x3628("0x4ab"), function (_0x493117) {
    _0x493117[_0x3628("0x3ed")](), _0x493117[_0x3628("0x23")]();
  }, !0x0));
}
function i_WyfZCt() {
  if (i_iDQBme) {
    var _0x45307e = i_sxfci_("gamescr");
    i_sxfci_(_0x3628("0x3e5"))[_0x3628("0x3e")]["display"] = 0x0 == i_JhpKTt ? (_0x45307e[_0x3628("0xea")][_0x3628("0x405")]("gamebigwin"), i_JhpKTt = 0x1, i_sxfci_(_0x3628("0x4b0"))["classList"][_0x3628("0x405")](_0x3628("0x16f")), i_sxfci_(_0x3628("0x4b0"))[_0x3628("0x3e")]["opacity"] = _0x3628("0x1ac"), _0x3628("0x11c")) : 0x1 == i_JhpKTt ? (_0x45307e["classList"][_0x3628("0x47a")](_0x3628("0x21d")), _0x45307e["classList"]["add"](_0x3628("0x520")), i_JhpKTt = 0x2, i_sxfci_("chatpad")[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x16f")), i_sxfci_("chatpad")["style"][_0x3628("0x118")] = _0x3628("0x1ac"), _0x3628("0x11c")) : (_0x45307e["classList"][_0x3628("0x47a")](_0x3628("0x21d")), _0x45307e["classList"]["remove"](_0x3628("0x520")), i_JhpKTt = 0x0, i_sxfci_(_0x3628("0x4b0"))["style"][_0x3628("0x118")] = _0x3628("0x154"), i_sxfci_("chatpad")[_0x3628("0xea")][_0x3628("0x47a")]("fullScreenMsgBox"), _0x3628("0x47e")), setTimeout(function () {
      i_TfQXnf(i_WmyDOu[_0x3628("0x1d8")]);
    }, 0x64);
  }
}
function i_XxahKt() {
  i_iDQBme && $(".popoverlay")["hide"]();
}
function i_EBycOt() {
  if (0x0 == i_iDpHde) i_sxfci_("popwin_savemanager")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_RNjHjt();else {
    if (!i_pjQNyo()) return void i_iZhRvt("只有房主允许使用存档");
    i_sxfci_(_0x3628("0x45b"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_RNjHjt();
  }
}
function i_hErJXt(_0x27f454) {
  var _0x446710 = 0x3e8 / 0x3c * _0x27f454 / 0x3e8,
    _0x3e0c41 = Math[_0x3628("0x560")](_0x446710 / 0xe10),
    _0x9a923a = Math["floor"](_0x446710 / 0x3c) % 0x3c,
    _0x2b68d4 = Math[_0x3628("0x560")](_0x446710 % 0x3c);
  _0x3e0c41 <= 0x9 && (_0x3e0c41 = "0" + _0x3e0c41), _0x9a923a <= 0x9 && (_0x9a923a = "0" + _0x9a923a), _0x2b68d4 <= 0x9 && (_0x2b68d4 = "0" + _0x2b68d4), i_JDzaxt["textContent"] = _0x3e0c41 + ":" + _0x9a923a + ":" + _0x2b68d4;
}
function i_NmhERt() {
  i_iZhRvt(_0x3628("0x478"));
}
function i_miJRzt() {
  if (!i_FGtjkt) {
    i_FGtjkt = !0x0, i_Hfbes_(_0x3628("0x136"), function (_0x2306b3) {
      if ("FxLlP" !== "vysFK") {
        $(this)["siblings"]("li")[_0x3628("0xe0")](_0x3628("0x2a6")), $(this)[_0x3628("0x32")](_0x3628("0x2a6")), $(_0x3628("0x25a"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e")), $(_0x3628("0x3bf"))["css"](_0x3628("0x3b5"), _0x3628("0x8c"));
      } else {
        if ("NO" == (_0x27e571 = (_0x27e571 = (_0x27e571 = i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(i_Wyjte)]["replace"](_0x3628("0xd5"), ""))["replace"](_0x3628("0x1e2"), ""))["replace"](_0x3628("0x2f8"), "")) && (_0x27e571 = "空"), i_Wyjte[_0x3628("0x32d")] = _0x27e571, i_cJzsbn && (0x0 <= i_Brhwo_(i_Wyjte)["indexOf"](_0x3628("0x58")) || 0x0 <= i_Brhwo_(i_Wyjte)[_0x3628("0x2f3")](_0x3628("0x200")))) {
          var _0x27e571 = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(i_Wyjte)];
          i_Wyjte[_0x3628("0x32d")] = _0x27e571;
        }
      }
    }), i_Hfbes_("lookertab", function (_0x38d0c0) {
      $(this)[_0x3628("0xd3")]("li")["removeClass"](_0x3628("0x2a6")), $(this)[_0x3628("0x32")]("active"), $(_0x3628("0x25a"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x8c")), $(_0x3628("0x3bf"))[_0x3628("0x12")](_0x3628("0x3b5"), _0x3628("0x47e"));
    }), i_Hfbes_(_0x3628("0x1e4"), function () {
      i_WwiRv_(_0x3628("0x3da"), _0x3628("0x3dd")) ? (i_sxfci_(_0x3628("0x541"))["style"][_0x3628("0x9")] = _0x3628("0x34"), i_sxfci_(_0x3628("0x34b"))[_0x3628("0x3e")][_0x3628("0x52f")] = "8px\x208px\x200\x200", i_sxfci_(_0x3628("0x1e4"))[_0x3628("0xea")][_0x3628("0x405")]("reserveTopDown")) : (i_sxfci_(_0x3628("0x541"))[_0x3628("0x3e")]["height"] = _0x3628("0x233"), i_sxfci_(_0x3628("0x34b"))[_0x3628("0x3e")][_0x3628("0x52f")] = "8px", i_sxfci_(_0x3628("0x1e4"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x52c")));
    }), i_Hfbes_(_0x3628("0xd1"), i_NmbbAt), i_Hfbes_(_0x3628("0x80"), i_GzppX), i_Hfbes_("btn_pause", i_GEMRR), i_Hfbes_(_0x3628("0x40c"), i_AfXQO), i_Hfbes_(_0x3628("0xcd"), i_EBycOt), i_Hfbes_(_0x3628("0x487"), i_NmhERt), i_Hfbes_(_0x3628("0x502"), function () {
      0x1 == i_iDpHde ? i_iZhRvt(_0x3628("0x199")) : 0x0 < i_DdTaQf[_0x3628("0x31")]["VipLevel"] || 0x0 < i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")] ? i_WwiRv_(_0x3628("0x47f"), _0x3628("0x8c")) : i_rrYppt("金手指目前仅对会员和黑暗骑士开放");
    }), i_Hfbes_("btn_setting", function () {
      i_WwiRv_(_0x3628("0x96"), _0x3628("0x8c"));
    }), i_Hfbes_("btn_keybind", function () {
      i_WwiRv_(_0x3628("0x2b8"), "block");
    }), i_Hfbes_(_0x3628("0x41c"), function () {
      0x1 == i_iDpHde ? i_rrYppt(_0x3628("0x420")) : (i_sxfci_("tx_gamespeed")[_0x3628("0x24d")] = i_YWkhPt < 0x8 ? (i_YWkhPt *= 0x2) + "倍" : (i_YWkhPt = 0x1, "加速"), i_bkNDhu(i_YWkhPt), i_rrYppt(_0x3628("0x182") + i_YWkhPt + "倍速"));
    }), i_Hfbes_(_0x3628("0x1eb"), function () {
      if (0x1 == i_iDpHde) i_WwiRv_(_0x3628("0xd9"), _0x3628("0x8c"));else if (-0x1 == i_HjtSei) i_iZhRvt(_0x3628("0x509"));else {
        if (null == (i_TBnkka = navigator[_0x3628("0xa")]()[i_HjtSei])) return void i_iZhRvt(_0x3628("0x509"));
        var _0x52e110 = i_TBnkka["id"][_0x3628("0x26a")](0x0, i_TBnkka["id"]["indexOf"](_0x3628("0x75")));
        switch ("" == _0x52e110 && (_0x52e110 = i_TBnkka["id"][_0x3628("0x26a")](0x0, i_TBnkka["id"][_0x3628("0x2f3")]("("))), "" == _0x52e110 && (_0x52e110 = i_TBnkka["id"]), 0x3 == ++i_DFBCti && (i_DFBCti = 0x0), i_DFBCti) {
          case 0x0:
            i_rrYppt(_0x3628("0x2e9"));
            break;
          case 0x1:
            i_rrYppt(_0x3628("0x31b"));
            break;
          case 0x2:
            i_rrYppt("进入【1P\x20手柄摇杆】\x20，【2P\x20手柄摇杆】多人模式");
        }
      }
    }), 0x0 == i_iDpHde && (i_sxfci_("btn_sharegame")[_0x3628("0x27e")] = _0x3628("0xec")), i_Hfbes_("btn_sendshareinfo", i_WBYHll), i_cirYl_(_0x3628("0x78"), function (_0x484b60) {
      if (_0x3628("0x14a") !== _0x3628("0x4a5")) {
        for (var _0x12a850 = _0x484b60[_0x3628("0x2f9")]["parentElement"]; _0x12a850["id"][_0x3628("0x2f3")](_0x3628("0x49e")) < 0x0;) _0x12a850 = _0x12a850[_0x3628("0x456")];
        _0x12a850[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
      } else {
        if (_0x3628("0x68") == i_GakWe[_0x3628("0x446")]) {
          var _0x69144 = Math["floor"](16.66666 * i_PBari[_0x3628("0x532")] / 0x3e8);
          i_hnXPpo(_0x3628("0x2a5"), "录像已保存到服务器\x20时长:" + (Math["floor"](_0x69144 / 0x3c) + "分" + (_0x69144 %= 0x3c) + "秒") + ("\x20<text\x20style=\x27color:\x20rgb(255,121,0)\x27>录像ID:" + i_GakWe["rid"] + "\x20</text>") + _0x3628("0x9d") + i_GakWe[_0x3628("0x1cc")] + _0x3628("0x567"));
        } else i_iZhRvt(_0x3628("0x38b"));
      }
    }), i_cirYl_(_0x3628("0x545"), function (_0x26293b) {
      for (var _0x55da29 = _0x26293b[_0x3628("0x2f9")][_0x3628("0x456")]; _0x55da29["id"][_0x3628("0x2f3")](_0x3628("0x49e")) < 0x0;) _0x55da29 = _0x55da29["parentElement"];
      _0x55da29[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
    }), i_cirYl_(_0x3628("0x229"), function (_0x2e06ec) {
      for (var _0x6f542f = _0x2e06ec[_0x3628("0x2f9")][_0x3628("0x456")]; _0x6f542f["id"][_0x3628("0x2f3")]("popwin_") < 0x0;) _0x6f542f = _0x6f542f[_0x3628("0x456")];
      _0x6f542f[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_JcAsUu();
    }), i_Hfbes_(_0x3628("0x1ea"), function () {
      i_cJzsbn ? (i_WmyDOu[_0x3628("0xe1")] = JSON[_0x3628("0x1aa")](JSON[_0x3628("0x2ae")](i_mACQRu)), i_fkKwAn()) : (i_WmyDOu[_0x3628("0x46f")] = JSON["parse"](JSON["stringify"](i_jwnmXu)), i_Bckxkn());
    });
    var _0x2ec91e = 0x0;
    i_tXrZr_("gamescr", _0x3628("0x4ab"), function (_0x4394a1) {
      _0x4394a1[_0x3628("0xc4")] - _0x2ec91e < 0x12c && 0x0 != _0x2ec91e ? i_WyfZCt() : _0x2ec91e = _0x4394a1[_0x3628("0xc4")];
    }), i_Hfbes_("vote_yes", i_icfsN), i_Hfbes_("vote_no", i_EpjGI), i_YQtSGn(), i_Zbrnra(), i_Hfbes_(_0x3628("0x54d"), function () {
      window[_0x3628("0x424")](), window[_0x3628("0x497")][_0x3628("0x3a")] = _0x3628("0xd");
    });
    i_Hfbes_("soundswitch", function () {
      i_FJfZMt = i_FJfZMt ? (this[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xdf")), this[_0x3628("0xea")]["add"](_0x3628("0x49b")), this[_0x3628("0x3e")][_0x3628("0x4d6")] = _0x3628("0xae"), i_rHShLf(0x0), !0x1) : (this[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x49b")), this[_0x3628("0xea")]["add"](_0x3628("0xdf")), this[_0x3628("0x3e")]["color"] = "#f9d342", i_rHShLf(i_WmyDOu["gSoundVol"] / 0x64), !0x0);
    });
  }
}
function i_SDeSIt() {
  i_Hfbes_(_0x3628("0x4b4"), i_ankaua["V"]), i_Hfbes_(_0x3628("0x393"), i_ankaua["j"]), i_Hfbes_("winmax", i_ankaua["H"]);
}
function i_MaRANt() {
  0x1 == i_iDpHde && (i_sxfci_(_0x3628("0x230"))[_0x3628("0x27e")] = _0x3628("0x53a") + i_ZmCwpe), i_JDzaxt = i_sxfci_("timespan"), i_SDeSIt(), i_KJGWEt(), i_miJRzt(), i_xjiCst(), i_AYSSqt(), i_kRWdvn();
}
function i_YyrnUt() {
  i_sxfci_("gamename")[_0x3628("0x27e")] = i_BThm_e;
}
function i_jypkDt() {
  i_sxfci_(_0x3628("0x2cb"))[_0x3628("0x27e")] = i_DdTaQf[_0x3628("0x289")], i_sxfci_("p1img")["src"] = i_DdTaQf[_0x3628("0x4f9")];
}
var i_kJKK$t = {
    sv1: 0x0,
    sv2: 0x0,
    sv3: 0x0,
    sv4: 0x0,
    st1: null,
    st2: null,
    st3: null,
    st4: null
  },
  i_cQmQBt = {
    d1: 0x0,
    d1i: null,
    d2: 0x0,
    d2i: null,
    d3: 0x0,
    d3i: null,
    d4: 0x0,
    d4i: null
  },
  i_NMMPGt = 0x0;
function i_pSswFt() {
  0x1 == i_kJKK$t["sv1"] ? ($("#sv1time")[_0x3628("0x24b")](i_kJKK$t["st1"]), null == i_cQmQBt[_0x3628("0x1cb")] && (i_cQmQBt[_0x3628("0x1cb")] = _0x3628("0x267") + gid), i_sxfci_("sv1img")[_0x3628("0x1c5")] = i_cQmQBt[_0x3628("0x1cb")]) : ($(_0x3628("0x44d"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x186"))["src"] = "https://static.wo1wan.com/game/empsv.png"), 0x1 == i_kJKK$t[_0x3628("0x3f8")] ? ($("#sv2time")[_0x3628("0x24b")](i_kJKK$t[_0x3628("0x213")]), null == i_cQmQBt[_0x3628("0x4d7")] && (i_cQmQBt[_0x3628("0x4d7")] = _0x3628("0x20d") + gid), i_sxfci_(_0x3628("0x451"))["src"] = i_cQmQBt[_0x3628("0x4d7")]) : ($(_0x3628("0x2e5"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x451"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["sv3"] ? ($(_0x3628("0x222"))["html"](i_kJKK$t[_0x3628("0x2c3")]), null == i_cQmQBt[_0x3628("0x256")] && (i_cQmQBt[_0x3628("0x256")] = _0x3628("0x29c") + gid), i_sxfci_(_0x3628("0x4"))["src"] = i_cQmQBt["d3i"]) : ($(_0x3628("0x222"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x4"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["sv4"] ? ($(_0x3628("0x2ee"))[_0x3628("0x24b")](i_kJKK$t[_0x3628("0x4d0")]), null == i_cQmQBt[_0x3628("0x35")] && (i_cQmQBt[_0x3628("0x35")] = _0x3628("0x27a") + gid), i_sxfci_(_0x3628("0x4c4"))[_0x3628("0x1c5")] = i_cQmQBt[_0x3628("0x35")]) : ($(_0x3628("0x2ee"))[_0x3628("0x24b")](""), i_sxfci_("sv4img")["src"] = _0x3628("0x116")), 0x1 == i_kJKK$t["W"] ? ($(_0x3628("0x524"))[_0x3628("0x24b")](i_kJKK$t["J"]), null == i_cQmQBt["Z"] && (i_cQmQBt["Z"] = _0x3628("0x18c") + gid), i_sxfci_("sv5img")[_0x3628("0x1c5")] = i_cQmQBt["Z"]) : ($(_0x3628("0x524"))["html"](""), i_sxfci_(_0x3628("0x133"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["ee"] ? ($(_0x3628("0x122"))[_0x3628("0x24b")](i_kJKK$t["ne"]), null == i_cQmQBt["te"] && (i_cQmQBt["te"] = _0x3628("0x389") + gid), i_sxfci_("sv6img")[_0x3628("0x1c5")] = i_cQmQBt["te"]) : ($("#sv6time")["html"](""), i_sxfci_(_0x3628("0x35c"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["ae"] ? ($(_0x3628("0x10f"))["html"](i_kJKK$t["ie"]), null == i_cQmQBt["oe"] && (i_cQmQBt["oe"] = _0x3628("0x208") + gid), i_sxfci_(_0x3628("0xcb"))[_0x3628("0x1c5")] = i_cQmQBt["oe"]) : ($(_0x3628("0x10f"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0xcb"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["ce"] ? ($(_0x3628("0x18"))[_0x3628("0x24b")](i_kJKK$t["re"]), null == i_cQmQBt["le"] && (i_cQmQBt["le"] = _0x3628("0x243") + gid), i_sxfci_("sv8img")["src"] = i_cQmQBt["le"]) : ($("#sv8time")[_0x3628("0x24b")](""), i_sxfci_("sv8img")[_0x3628("0x1c5")] = "https://static.wo1wan.com/game/empsv.png");
}
function i_TkwGLt() {
  var _0x491e41 = new XMLHttpRequest();
  _0x491e41[_0x3628("0x3d8")](_0x3628("0x4b"), _0x3628("0x3b0") + gid, !0x0), _0x491e41[_0x3628("0x59")]("Content-type", _0x3628("0x564")), _0x491e41[_0x3628("0x278")] = function () {
    _0x491e41[_0x3628("0xee")] == XMLHttpRequest[_0x3628("0x4e")] && _0x491e41["status"];
  }, _0x491e41[_0x3628("0x559")](JSON[_0x3628("0x2ae")](i_kJKK$t)), i_pSswFt();
}
function i_AYSSqt() {
  var _0x461eec = new XMLHttpRequest();
  _0x461eec[_0x3628("0x3d8")]("GET", "/fc/svload?game=" + gid, !0x0), _0x461eec[_0x3628("0x1c4")] = "json", _0x461eec[_0x3628("0x278")] = function () {
    if (_0x461eec["readyState"] == XMLHttpRequest[_0x3628("0x4e")] && 0xc8 == _0x461eec["status"]) {
      var _0x256867 = _0x461eec["response"];
      i_kJKK$t = _0x256867, i_pSswFt();
    }
  }, _0x461eec[_0x3628("0x559")](), i_EEHfVt();
}
function i_EEHfVt() {
  i_cirYl_(_0x3628("0x2c9"), i_TWSPWt), i_cirYl_(_0x3628("0x4f6"), i_NmMrea), i_cirYl_(_0x3628("0x43b"), i_StGeJt), i_cirYl_("btn_delsave", i_HjJwQt);
}
function i_RNjHjt() {
  i_sxfci_(_0x3628("0x402"))["disabled"] = !0x1, i_sxfci_(_0x3628("0x36d"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x48c"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x49d"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x42f"))[_0x3628("0x306")] = !0x1, i_sxfci_("sbtn6")[_0x3628("0x306")] = !0x1, i_sxfci_("sbtn7")["disabled"] = !0x1, i_sxfci_("sbtn8")[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x45"))[_0x3628("0x306")] = !0x1, i_sxfci_("lbtn2")[_0x3628("0x306")] = !0x1, i_sxfci_("lbtn3")[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0xd8"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x26d"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x264"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x239"))[_0x3628("0x306")] = !0x1, i_sxfci_(_0x3628("0x505"))[_0x3628("0x306")] = !0x1;
}
function i_smExYt(_0x20a5cb) {
  i_sxfci_("sbtn" + _0x20a5cb)[_0x3628("0x306")] = !0x0;
}
function i_MKbRHt(_0x250413) {
  i_sxfci_(_0x3628("0x2ff") + _0x250413)[_0x3628("0x306")] = !0x0;
}
function i_TWSPWt() {
  var _0x1232c1;
  0x6 < (_0x1232c1 = parseInt(this["id"]["replace"](_0x3628("0x4ec"), ""))) && i_DdTaQf["LevelInfo"][_0x3628("0x150")] <= 0x0 ? i_iZhRvt(_0x3628("0x7c")) : 0x4 < _0x1232c1 && i_DdTaQf["LevelInfo"][_0x3628("0x150")] <= 0x0 && i_DdTaQf[_0x3628("0x31")]["Vip"] <= 0x0 ? i_iZhRvt("请开通会员解锁该云存档位<br>会员到期后存档永久存在，可随时续费使用") : i_WmWryu(_0x1232c1);
}
function i_StGeJt() {
  i_iZhRvt(_0x3628("0x3fa"));
}
function i_HjJwQt() {
  var _0x3ae355;
  _0x3ae355 = parseInt(this["id"]["replace"](_0x3628("0x349"), "")), i_kJKK$t["sv" + _0x3ae355] = 0x0, i_kJKK$t["st" + _0x3ae355] = null, i_TkwGLt(), i_sxfci_("sbtn" + _0x3ae355)[_0x3628("0x306")] = !0x1;
}
var i_yGnJZt = -0x927c0;
function i_NmMrea() {
  if (0x1 == i_iDpHde) {
    if (0x1 < i_xjHeZi) return void i_iZhRvt("加载存档时，房间不可有其他人上位，请对方先退到观战位");
    if (performance[_0x3628("0x30b")]() - i_yGnJZt < 0xea60) return void i_iZhRvt("联机时一分钟内只能加载一次存档");
    i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x409")), i_yGnJZt = performance[_0x3628("0x30b")]();
  }
  i_ZjZkwu(parseInt(this["id"]["replace"](_0x3628("0x2ff"), "")));
}
var i_Tnctna = 0x1;
function i_fFmbta(_0x278f7b) {
  var _0x11b1fb = i_Tnctna;
  if (i_Tnctna = _0x278f7b, 0x0 == i_iDpHde) {
    i_sxfci_("p" + _0x11b1fb + "img")[_0x3628("0x1c5")] = _0x3628("0x23d"), i_sxfci_("p" + _0x11b1fb + "nick")["innerText"] = "空", i_sxfci_("p" + i_Tnctna + _0x3628("0x265"))[_0x3628("0x1c5")] = i_DdTaQf[_0x3628("0x4f9")], i_sxfci_("p" + i_Tnctna + _0x3628("0x308"))[_0x3628("0x27e")] = i_DdTaQf[_0x3628("0x289")], i_ktCsEu(i_Tnctna);
    var _0x566729 = i_sxfci_(_0x3628("0x23b"));
    0x0 == (i_Kbzklo = i_Tnctna - 0x1) ? (_0x566729["style"][_0x3628("0x282")] = "90px", _0x566729[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x3e8")) : 0x1 == i_Kbzklo ? (_0x566729[_0x3628("0x3e")][_0x3628("0x282")] = "280px", _0x566729[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x3e8")) : 0x2 == i_Kbzklo ? (_0x566729["style"][_0x3628("0x282")] = "90px", _0x566729["style"]["top"] = _0x3628("0x465")) : 0x3 == i_Kbzklo && (_0x566729[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x1ad"), _0x566729[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x465"));
  } else {
    if (i_nibwYi) return void (_0x3628("0x356") == i_sxfci_("p" + i_Tnctna + _0x3628("0x308"))[_0x3628("0x27e")] && i_hnXPpo("join", i_Tnctna + ""));
    if (_0x3628("0x364") != i_sxfci_("p" + i_Tnctna + _0x3628("0x308"))[_0x3628("0x27e")]) return;
    i_fdZfJi = !0x0, i_wsSfso[0x0] = i_emcHro, i_wsSfso[0x1] = 0x0, i_bYNtOo(), setTimeout(function () {
      i_hnXPpo("switch", i_Tnctna + "");
    }, 0xc8);
  }
}
var i_ieCCaa = "1";
function i_bTamia() {
  i_ieCCaa - 0x1 != i_emcHro ? ($("[name=usersubmenu]")["hide"](), _0x3628("0x364") == i_FZEFji[_0x3628("0x15c") + i_ieCCaa] ? i_sxfci_(_0x3628("0x4b5"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c") : "*关闭*" == i_FZEFji["Nick" + i_ieCCaa] ? i_sxfci_(_0x3628("0x24e"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "block" : (i_pjQNyo() && (i_sxfci_("usermenu_kick")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x4c0"))["style"]["display"] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x31d"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c")), i_sxfci_(_0x3628("0xc7"))["style"][_0x3628("0x3b5")] = "block", i_sxfci_(_0x3628("0x546"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x168"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"))) : i_sJmysa();
}
function i_wPiSoa(_0x49aa1f) {
  if (0x2 == _0x49aa1f[_0x3628("0x305")] && 0x1 == i_iDpHde) {
    var _0x3dfd62 = i_sxfci_(_0x3628("0x27"));
    _0x3dfd62[_0x3628("0x3e")][_0x3628("0x118")] = 0x1, _0x3dfd62[_0x3628("0x3e")][_0x3628("0x282")] = _0x49aa1f[_0x3628("0x325")] - 0xf + "px", _0x3dfd62[_0x3628("0x3e")][_0x3628("0x270")] = _0x49aa1f[_0x3628("0x16e")] - 0xf + "px", i_ieCCaa = _0x3628("0x562") == this["id"] ? "1" : "p2card" == this["id"] ? "2" : "p3card" == this["id"] ? "3" : "4", i_bTamia();
  }
}
function i_MDRyca() {
  var _0x5b561b = i_cbdGFn[_0x3628("0x307")][i_ieCCaa - 0x1];
  switch (this["id"]) {
    case _0x3628("0x168"):
      i_TrjcCu(-0x1, _0x5b561b, function (_0x2894ae) {
        i_rrYppt(_0x2894ae["NickName"] + _0x3628("0x3d7") + _0x2894ae[_0x3628("0x187")]), i_sxfci_(_0x3628("0x136"))[_0x3628("0x4ab")]();
      });
      break;
    case "usermenu_open":
      i_hnXPpo(_0x3628("0x3d8"), i_ieCCaa);
      break;
    case _0x3628("0x4b5"):
      i_hnXPpo(_0x3628("0x424"), i_ieCCaa);
      break;
    case _0x3628("0xa3"):
      i_hnXPpo(_0x3628("0x414"), i_ieCCaa - 0x1 + "");
      break;
    case _0x3628("0x4c0"):
      i_pjQNyo() ? $[_0x3628("0x341")]("/sayban", {
        who: _0x5b561b,
        where: "jjin_" + gid + "_" + i_ZmCwpe,
        say: ""
      }, function (_0x25b45b) {
        "ok" == _0x25b45b["status"] ? i_hnXPpo("message", i_cbdGFn[_0x3628("0x15c") + i_ieCCaa] + "\x20被房主在本房间禁言一小时") : i_iZhRvt(_0x3628("0x16b"));
      }) : i_iZhRvt(_0x3628("0xf9"));
      break;
    case _0x3628("0xc7"):
      i_TrjcCu(-0x1, _0x5b561b, function (_0x5471d4) {
        i_ankaua["ue"](_0x3628("0x313"), "add", _0x5471d4[_0x3628("0x187")]) ? i_rrYppt(_0x3628("0x37f")) : i_iZhRvt(_0x3628("0x358"));
      });
      break;
    case "usermenu_report":
      i_GJQKdt(_0x3628("0x45d"));
      break;
    case _0x3628("0x31d"):
      i_pjQNyo() && i_hnXPpo(_0x3628("0x48a"), i_ieCCaa - 0x1 + "");
  }
  var _0x55f86f = i_sxfci_("usermenu");
  _0x55f86f[_0x3628("0x3e")]["opacity"] = 0x0, setTimeout(function () {
    _0x55f86f[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x49c");
  }, 0x64);
}
function i_sJmysa() {
  var _0x4a4490 = i_sxfci_("usermenu");
  _0x4a4490[_0x3628("0x3e")]["opacity"] = 0x0, setTimeout(function () {
    _0x4a4490["style"][_0x3628("0x270")] = _0x3628("0x49c");
  }, 0x64);
}
function i_Zbrnra() {
  i_Hfbes_("p1card", function () {
    i_fFmbta(0x1);
  }), i_Hfbes_(_0x3628("0x90"), function () {
    if ("GbOuI" !== _0x3628("0x39d")) {
      var _0x42d1c4 = i_pecNn[_0x3628("0x3f")][_0x3628("0x2dd")];
      try {
        i_etBeu = JSON[_0x3628("0x1aa")](_0x42d1c4), i_FPWhm(), null != i_etBeu && setTimeout(function () {
          i_iDQBme ? i_rrYppt(_0x3628("0x218")) : i_xmzByr("本游戏金手指可用");
        }, 0x7d0);
      } catch (_0x207d2f) {
        return;
      }
    } else {
      i_fFmbta(0x2);
    }
  }), i_Hfbes_("p3card", function () {
    i_fFmbta(0x3);
  }), i_Hfbes_(_0x3628("0x50d"), function () {
    if (_0x3628("0x25d") === _0x3628("0x25d")) {
      i_fFmbta(0x4);
    } else {
      i_xWZnqi(i_iChyAi = i_mDiGLi(i_iChyAi, i_ytwce, i_sbMdn));
    }
  }), i_ZKQCf_(_0x3628("0x38f"), _0x3628("0x275"), i_wPiSoa), i_cirYl_(_0x3628("0x251"), i_MDRyca), i_tXrZr_(_0x3628("0x27"), _0x3628("0x111"), i_sJmysa);
}
function i_hGaila(_0x110ba3) {
  if (0x0 != _0x110ba3["indexOf"](_0x3628("0x425"))) return "";
  var _0x42d680 = _0x110ba3[_0x3628("0x16d")]("igw:cmd:", "")[_0x3628("0x11b")]("=");
  if (_0x42d680[_0x3628("0x288")] < 0x2) return "";
  switch (_0x42d680[0x0]) {
    case _0x3628("0x56c"):
      return "1" == _0x42d680[0x1] ? (i_EBCZln["P"][_0x3628("0x32d")] = !0x0, _0x3628("0x2ec")) : (i_EBCZln["P"][_0x3628("0x32d")] = !0x1, _0x3628("0x83"));
  }
  return "";
}
var i_ankaua = {
    fe: null,
    path: null,
    IGWorld: !0x1,
    _e: {
      ve: "",
      de: 0x578,
      pe: 0x2d0,
      me: 0x64,
      ge: 0x64,
      he: !0x1,
      be: 0x5a0,
      ye: 0x384,
      we: 0x64,
      ke: 0x64,
      Te: !0x0
    },
    g: function () {
      return !0x0 === window[_0x3628("0x4e1")] && null === i_ankaua["fe"] && (i_ankaua[_0x3628("0x4e1")] = !0x0, i_ankaua["fe"] = eval("require('electron')['ipcRenderer'];"), i_ankaua[_0x3628("0x82")] = eval("if ('kbOPg' !== _0x3628('0x3e7')) {\n    var _0x3ef490 = i_sxfci_(_0x3628('0x23b')), _0x18295c = i_sxfci_('Broomownerflag');\n    0 == i_Ebjce ? (_0x3ef490['style'][_0x3628('0x282')] = _0x3628('0x1cd'), _0x3ef490[_0x3628('0x3e')][_0x3628('0x270')] = _0x3628('0x22e'), _0x18295c['style'][_0x3628('0x270')] = _0x3628('0x3e3')) : 1 == i_Ebjce ? (_0x3ef490['style']['left'] = _0x3628('0x1cd'), _0x3ef490['style'][_0x3628('0x270')] = _0x3628('0x1f6'), _0x18295c[_0x3628('0x3e')][_0x3628('0x270')] = _0x3628('0xc8')) : 2 == i_Ebjce ? (_0x3ef490['style']['left'] = '128px', _0x3ef490[_0x3628('0x3e')]['top'] = _0x3628('0x54'), _0x18295c[_0x3628('0x3e')][_0x3628('0x270')] = _0x3628('0x1f')) : 3 == i_Ebjce && (_0x3ef490['style'][_0x3628('0x282')] = '128px', _0x3ef490[_0x3628('0x3e')][_0x3628('0x270')] = _0x3628('0xe5'), _0x18295c[_0x3628('0x3e')][_0x3628('0x270')] = _0x3628('0x4a6'));\n} else {\n    require(_0x3628('0x82'));\n}"), i_ankaua["fe"]["on"]("syncconfig", function (_0x282905, _0x2cd389) {
        i_ankaua["_e"] = _0x2cd389;
      }), i_ankaua["_e"] = i_ankaua["fe"][_0x3628("0x1a3")](_0x3628("0x4d8"))), setTimeout(this["xe"], 0x7d0), window[_0x3628("0x314")](_0x3628("0x30e"), this["xe"]), window[_0x3628("0x314")]("beforeunload", this["Me"]), i_ankaua[_0x3628("0x4e1")];
    },
    Pe: function (_0x60d1f4) {
      0x0 < navigator[_0x3628("0x209")]["indexOf"](_0x3628("0x241")) && _0x3628("0xf7") == typeof window[_0x3628("0x3e4")] && (null == i_ankaua["Ee"] && (i_ankaua["Ee"] = new window[_0x3628("0x3e4")]()), null != i_ankaua["Ee"] && i_ankaua["Ee"][_0x3628("0x388")](_0x3628("0x22c") + _0x60d1f4 + "}"));
    },
    Ae: function (_0x579b4b, _0x490cd8) {
      null != i_ankaua["fe"] && i_ankaua["fe"][_0x3628("0x559")](_0x579b4b, _0x490cd8);
    },
    Se: function (_0x5c1ed0, _0x74ae3c) {
      null != i_ankaua["fe"] && i_ankaua["fe"][_0x3628("0x559")](_0x3628("0x3ec"), _0x5c1ed0, _0x74ae3c);
    },
    Ce: function (_0x8ac8b8, _0x43a9b3, _0x81460c) {
      null != i_ankaua["fe"] && i_ankaua["fe"][_0x3628("0x559")]("wincmd", _0x8ac8b8, _0x43a9b3, _0x81460c);
    },
    Ke: function (_0x5a3a5e, _0x46321b) {
      if (null != this["fe"]) return this["fe"][_0x3628("0x1a3")]("syncrun", _0x5a3a5e, _0x46321b);
    },
    Oe: function (_0x25244c, _0x10ade9) {
      null == i_ankaua["fe"] ? window[_0x3628("0x3d8")](_0x25244c) : i_ankaua["fe"][_0x3628("0x559")](_0x3628("0x12e"), _0x10ade9, _0x25244c, winargs);
    },
    j: function () {
      i_ankaua["Ce"]("game", "self.minimize();");
    },
    H: function () {
      i_ankaua["Ce"]("game", _0x3628("0x1e3"));
    },
    V: function () {
      i_ankaua["Ce"](_0x3628("0x14c"), "self.close();");
    },
    Xe: function (_0x400494, _0x3360d6) {},
    Re: function () {
      i_ankaua["Ce"](_0x3628("0x14c"), _0x3628("0x4ac"));
    },
    xe: function () {
      i_ankaua["ue"](_0x3628("0x382"), _0x3628("0x15e"), JSON[_0x3628("0x2ae")]({
        gametype: "fc",
        roomid: i_ZmCwpe,
        gamename: i_BThm_e,
        gameid: gid,
        server: i_wesTbe
      }));
    },
    Me: function () {
      i_ankaua["ue"](_0x3628("0x382"), "lobby", "");
    },
    ue: function (_0x3752be, _0x89cd2f, _0x2129b7) {
      return null != i_ankaua["fe"] ? (i_ankaua["Ce"](_0x3628("0x9b"), _0x3628("0xb4") + JSON[_0x3628("0x2ae")]({
        event: _0x3752be,
        type: _0x89cd2f,
        data: _0x2129b7
      }) + ")"), !0x0) : null == window["opener"] ? null != window[_0x3628("0x2e8")] && (window[_0x3628("0x2e8")]["postMessage"]({
        event: _0x3752be,
        type: _0x89cd2f,
        data: _0x2129b7
      }, "*"), !0x0) : (window["opener"]["postMessage"]({
        event: _0x3752be,
        type: _0x89cd2f,
        data: _0x2129b7
      }, "*"), !0x0);
    },
    ze: function (_0x36a8c4) {
      void 0x0 === _0x36a8c4 && (_0x36a8c4 = _0x3628("0x406")), i_ankaua["ue"](_0x3628("0x44e"), _0x36a8c4, ""), i_mMmFe_(_0x3628("0x554"), "ShowVIP", [gid + ""]);
    }
  },
  i_wpdPfa = [],
  i_iYai_a = 0x0,
  i_HaCAva = {
    Ie: [],
    Ne: [],
    Ue: [],
    De: []
  };
function i_HtHnda(_0x157e28, _0x237caa) {
  for (var _0x50996c = "", _0x4d1604 = 0x0; _0x4d1604 < _0x237caa[_0x3628("0x288")]; _0x4d1604++) "←" == _0x237caa[_0x4d1604] && (_0x50996c += "1\x20"), "→" == _0x237caa[_0x4d1604] && (_0x50996c += "2\x20"), "↑" == _0x237caa[_0x4d1604] && (_0x50996c += "3\x20"), "↓" == _0x237caa[_0x4d1604] && (_0x50996c += "4\x20"), "↖" == _0x237caa[_0x4d1604] && (_0x50996c += "5\x20"), "↗" == _0x237caa[_0x4d1604] && (_0x50996c += "6\x20"), "↙" == _0x237caa[_0x4d1604] && (_0x50996c += "7\x20"), "↘" == _0x237caa[_0x4d1604] && (_0x50996c += "8\x20"), "A" == _0x237caa[_0x4d1604] && (_0x50996c += "A\x20"), "B" == _0x237caa[_0x4d1604] && (_0x50996c += "B\x20"), "C" == _0x237caa[_0x4d1604] && (_0x50996c += "C\x20"), "D" == _0x237caa[_0x4d1604] && (_0x50996c += "D\x20"), "E" == _0x237caa[_0x4d1604] && (_0x50996c += "E\x20"), "F" == _0x237caa[_0x4d1604] && (_0x50996c += "F\x20"), "等" == _0x237caa[_0x4d1604] && (_0x50996c += "等\x20"), "正" == _0x237caa[_0x4d1604] && (_0x50996c += "正\x20"), "反" == _0x237caa[_0x4d1604] && (_0x50996c += "反\x20");
  i_WmyDOu[_0x157e28] = _0x50996c;
}
function i_tMMkpa() {
  var _0x129ee2 = i_WmyDOu["gGes1"][_0x3628("0x11b")]("1")["join"]("←")[_0x3628("0x11b")]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")[_0x3628("0x37c")]("↑")[_0x3628("0x11b")]("4")["join"]("↓");
  _0x129ee2 = _0x129ee2["split"]("5")[_0x3628("0x37c")]("↖")[_0x3628("0x11b")]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")["join"]("↙")[_0x3628("0x11b")]("8")[_0x3628("0x37c")]("↘"), document[_0x3628("0x416")](_0x3628("0x1f7"))[_0x3628("0x32d")] = _0x129ee2, _0x129ee2 = (_0x129ee2 = i_WmyDOu["gGes2"][_0x3628("0x11b")]("1")[_0x3628("0x37c")]("←")[_0x3628("0x11b")]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")[_0x3628("0x37c")]("↑")[_0x3628("0x11b")]("4")[_0x3628("0x37c")]("↓"))["split"]("5")["join"]("↖")["split"]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")[_0x3628("0x37c")]("↙")[_0x3628("0x11b")]("8")["join"]("↘"), document["getElementById"]("Ges2")[_0x3628("0x32d")] = _0x129ee2, _0x129ee2 = (_0x129ee2 = i_WmyDOu["gGes3"][_0x3628("0x11b")]("1")[_0x3628("0x37c")]("←")["split"]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")[_0x3628("0x37c")]("↑")[_0x3628("0x11b")]("4")[_0x3628("0x37c")]("↓"))["split"]("5")[_0x3628("0x37c")]("↖")[_0x3628("0x11b")]("6")[_0x3628("0x37c")]("↗")["split"]("7")[_0x3628("0x37c")]("↙")[_0x3628("0x11b")]("8")["join"]("↘"), document[_0x3628("0x416")]("Ges3")[_0x3628("0x32d")] = _0x129ee2, _0x129ee2 = (_0x129ee2 = i_WmyDOu[_0x3628("0x4d5")]["split"]("1")[_0x3628("0x37c")]("←")[_0x3628("0x11b")]("2")[_0x3628("0x37c")]("→")[_0x3628("0x11b")]("3")[_0x3628("0x37c")]("↑")[_0x3628("0x11b")]("4")["join"]("↓"))[_0x3628("0x11b")]("5")[_0x3628("0x37c")]("↖")[_0x3628("0x11b")]("6")[_0x3628("0x37c")]("↗")[_0x3628("0x11b")]("7")["join"]("↙")["split"]("8")[_0x3628("0x37c")]("↘"), document[_0x3628("0x416")](_0x3628("0x386"))[_0x3628("0x32d")] = _0x129ee2, document["getElementById"](_0x3628("0x86"))["value"] = i_WmyDOu["gGesSpeed"];
}
function i_yQQtma() {
  i_HaCAva["Ie"] = [], i_HaCAva["Ne"] = [], i_HaCAva["Ue"] = [], i_HaCAva["De"] = [];
  for (var _0x4d00bb = 0x0; _0x4d00bb < i_WmyDOu[_0x3628("0x315")][_0x3628("0x288")]; _0x4d00bb++) switch (i_WmyDOu[_0x3628("0x315")][_0x4d00bb]) {
    case "1":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_ipNXXi);
      break;
    case "2":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_xcfpRi);
      break;
    case "3":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_QmFZzi);
      break;
    case "4":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_ZSDiIi);
      break;
    case "5":
      i_HaCAva["Ie"]["push"](i_QmFZzi | i_ipNXXi);
      break;
    case "6":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_QmFZzi | i_xcfpRi);
      break;
    case "7":
      i_HaCAva["Ie"][_0x3628("0x35d")](i_ZSDiIi | i_ipNXXi);
      break;
    case "8":
      i_HaCAva["Ie"]["push"](i_ZSDiIi | i_xcfpRi);
      break;
    case "A":
      i_HaCAva["Ie"][_0x3628("0x35d")](mask_fire1);
      break;
    case "B":
      i_HaCAva["Ie"]["push"](mask_fire2);
      break;
    case "C":
      i_HaCAva["Ie"]["push"](mask_fire3);
      break;
    case "D":
      i_HaCAva["Ie"][_0x3628("0x35d")](mask_fire4);
      break;
    case "E":
      i_HaCAva["Ie"][_0x3628("0x35d")](mask_fire5);
      break;
    case "F":
      i_HaCAva["Ie"][_0x3628("0x35d")](mask_fire6);
      break;
    case "等":
      i_HaCAva["Ie"][_0x3628("0x35d")](0x0);
  }
  for (_0x4d00bb = 0x0; _0x4d00bb < i_WmyDOu["gGes2"][_0x3628("0x288")]; _0x4d00bb++) switch (i_WmyDOu[_0x3628("0xa1")][_0x4d00bb]) {
    case "1":
      i_HaCAva["Ne"]["push"](i_ipNXXi);
      break;
    case "2":
      i_HaCAva["Ne"][_0x3628("0x35d")](i_xcfpRi);
      break;
    case "3":
      i_HaCAva["Ne"]["push"](i_QmFZzi);
      break;
    case "4":
      i_HaCAva["Ne"]["push"](i_ZSDiIi);
      break;
    case "5":
      i_HaCAva["Ne"]["push"](i_QmFZzi | i_ipNXXi);
      break;
    case "6":
      i_HaCAva["Ne"]["push"](i_QmFZzi | i_xcfpRi);
      break;
    case "7":
      i_HaCAva["Ne"][_0x3628("0x35d")](i_ZSDiIi | i_ipNXXi);
      break;
    case "8":
      i_HaCAva["Ne"][_0x3628("0x35d")](i_ZSDiIi | i_xcfpRi);
      break;
    case "A":
      i_HaCAva["Ne"][_0x3628("0x35d")](mask_fire1);
      break;
    case "B":
      i_HaCAva["Ne"]["push"](mask_fire2);
      break;
    case "C":
      i_HaCAva["Ne"][_0x3628("0x35d")](mask_fire3);
      break;
    case "D":
      i_HaCAva["Ne"]["push"](mask_fire4);
      break;
    case "E":
      i_HaCAva["Ne"][_0x3628("0x35d")](mask_fire5);
      break;
    case "F":
      i_HaCAva["Ne"][_0x3628("0x35d")](mask_fire6);
      break;
    case "等":
      i_HaCAva["Ne"][_0x3628("0x35d")](0x0);
  }
  for (_0x4d00bb = 0x0; _0x4d00bb < i_WmyDOu[_0x3628("0x363")]["length"]; _0x4d00bb++) switch (i_WmyDOu[_0x3628("0x363")][_0x4d00bb]) {
    case "1":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_ipNXXi);
      break;
    case "2":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_xcfpRi);
      break;
    case "3":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_QmFZzi);
      break;
    case "4":
      i_HaCAva["Ue"]["push"](i_ZSDiIi);
      break;
    case "5":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_QmFZzi | i_ipNXXi);
      break;
    case "6":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_QmFZzi | i_xcfpRi);
      break;
    case "7":
      i_HaCAva["Ue"][_0x3628("0x35d")](i_ZSDiIi | i_ipNXXi);
      break;
    case "8":
      i_HaCAva["Ue"]["push"](i_ZSDiIi | i_xcfpRi);
      break;
    case "A":
      i_HaCAva["Ue"]["push"](mask_fire1);
      break;
    case "B":
      i_HaCAva["Ue"]["push"](mask_fire2);
      break;
    case "C":
      i_HaCAva["Ue"][_0x3628("0x35d")](mask_fire3);
      break;
    case "D":
      i_HaCAva["Ue"]["push"](mask_fire4);
      break;
    case "E":
      i_HaCAva["Ue"][_0x3628("0x35d")](mask_fire5);
      break;
    case "F":
      i_HaCAva["Ue"][_0x3628("0x35d")](mask_fire6);
      break;
    case "等":
      i_HaCAva["Ue"]["push"](0x0);
  }
  for (_0x4d00bb = 0x0; _0x4d00bb < i_WmyDOu[_0x3628("0x4d5")]["length"]; _0x4d00bb++) switch (i_WmyDOu["gGes4"][_0x4d00bb]) {
    case "1":
      i_HaCAva["De"][_0x3628("0x35d")](i_ipNXXi);
      break;
    case "2":
      i_HaCAva["De"][_0x3628("0x35d")](i_xcfpRi);
      break;
    case "3":
      i_HaCAva["De"][_0x3628("0x35d")](i_QmFZzi);
      break;
    case "4":
      i_HaCAva["De"][_0x3628("0x35d")](i_ZSDiIi);
      break;
    case "5":
      i_HaCAva["De"][_0x3628("0x35d")](i_QmFZzi | i_ipNXXi);
      break;
    case "6":
      i_HaCAva["De"][_0x3628("0x35d")](i_QmFZzi | i_xcfpRi);
      break;
    case "7":
      i_HaCAva["De"][_0x3628("0x35d")](i_ZSDiIi | i_ipNXXi);
      break;
    case "8":
      i_HaCAva["De"]["push"](i_ZSDiIi | i_xcfpRi);
      break;
    case "A":
      i_HaCAva["De"][_0x3628("0x35d")](mask_fire1);
      break;
    case "B":
      i_HaCAva["De"][_0x3628("0x35d")](mask_fire2);
      break;
    case "C":
      i_HaCAva["De"][_0x3628("0x35d")](mask_fire3);
      break;
    case "D":
      i_HaCAva["De"][_0x3628("0x35d")](mask_fire4);
      break;
    case "E":
      i_HaCAva["De"]["push"](mask_fire5);
      break;
    case "F":
      i_HaCAva["De"][_0x3628("0x35d")](mask_fire6);
      break;
    case "等":
      i_HaCAva["De"][_0x3628("0x35d")](0x0);
  }
}
function i_JHFkga() {
  if (0x0 < i_wpdPfa[_0x3628("0x288")]) {
    var _0x2dbcfe = i_wpdPfa[_0x3628("0x338")]();
    return 0x1 == i_iYai_a && (_0x2dbcfe & i_ipNXXi ? (_0x2dbcfe &= ~i_ipNXXi, _0x2dbcfe |= i_xcfpRi) : _0x2dbcfe & i_xcfpRi && (_0x2dbcfe &= ~i_xcfpRi, _0x2dbcfe |= i_ipNXXi)), _0x2dbcfe;
  }
  return null;
}
function i_pAJwha(_0x19d8bc) {
  i_wpdPfa[_0x3628("0x35d")](0x0), 0x0 == _0x19d8bc ? i_wpdPfa = i_wpdPfa[_0x3628("0x157")](i_HaCAva["Ie"]) : 0x1 == _0x19d8bc ? i_wpdPfa = i_wpdPfa["concat"](i_HaCAva["Ne"]) : 0x2 == _0x19d8bc ? i_wpdPfa = i_wpdPfa[_0x3628("0x157")](i_HaCAva["Ue"]) : 0x3 == _0x19d8bc && (i_wpdPfa = i_wpdPfa[_0x3628("0x157")](i_HaCAva["De"])), i_wpdPfa["push"](0x0);
}
var i_MQwnba = navigator[_0x3628("0x209")],
  i_GHKWya = {
    $e: 0x0,
    Be: 0x1,
    Ge: 0x2,
    Fe: 0x3,
    Le: 0x7,
    qe: 0x8,
    Ve: 0x9,
    je: 0xa,
    Ye: 0xd,
    o: 0xe,
    He: function () {
      i_WmyDOu[_0x3628("0x3bc")] && null != navigator[_0x3628("0x236")] && navigator["vibrate"](0x20);
    },
    We: function () {
      i_jPzhfu(this["Be"], 0x1);
    },
    Je: function () {
      i_jPzhfu(this["Be"], 0x0);
    },
    Qe: function () {
      i_jPzhfu(this["Ge"], 0x1);
    },
    Ze: function () {
      i_jPzhfu(this["Ge"], 0x0);
    },
    en: function () {
      i_WmyDOu["gASuper"] ? i_jPzhfu(this["Le"], 0x2) : i_jPzhfu(this["Le"], 0x1);
    },
    nn: function () {
      i_jPzhfu(this["Le"], 0x0);
    },
    tn: function () {
      i_WmyDOu[_0x3628("0x2ed")] ? i_jPzhfu(this["qe"], 0x2) : i_jPzhfu(this["qe"], 0x1);
    },
    an: function () {
      i_jPzhfu(this["qe"], 0x0);
    },
    in: function () {
      i_jPzhfu(this["Ve"], 0x1);
    },
    cn: function () {
      i_jPzhfu(this["Ve"], 0x0);
    },
    sn: function () {
      i_jPzhfu(this["je"], 0x1);
    },
    rn: function () {
      i_jPzhfu(this["je"], 0x0);
    },
    ln: function () {
      i_jPzhfu(this["Ye"], i_WmyDOu["gExtX1Key"]);
    },
    un: function () {
      i_jPzhfu(this["Ye"], 0x0);
    },
    _n: function (_0x3b4bca) {
      i_jPzhfu(this["Fe"], _0x3b4bca);
    }
  },
  i_ZSfTwa = !0x1,
  i_TBnkka = null,
  i_KbwzTa = 0x0,
  i_dwBdxa = 0x1,
  i_rtsAMa = 0x2,
  i_KAhWPa = 0x3,
  i_yETWEa = 0x4,
  i_XyPCAa = 0x5,
  i_ckjFSa = 0x6,
  i_DMWdCa = 0x7,
  i_DzADKa = 0x8,
  i_hKeKOa = 0x9,
  i_meTjXa = -0x1,
  i_QAaTRa = -0x1,
  i_wDhTza = -0x1,
  i_XNHXIa = -0x1,
  i_QCGsNa = -0x1,
  i_QkDjUa = -0x1,
  i_yjKJDa = -0x1,
  i_FAcs$a = -0x1,
  i_cdHwBa = 0x0,
  i_XGpsGa = !0x1,
  i_NjznFa = !0x1,
  i_kxmzLa = !0x1,
  i_Hzkdqa = !0x1,
  i_DhizVa = !0x1,
  i_WBwdja = !0x1,
  i_hEZSYa = !0x1,
  i_mHHaHa = !0x1,
  i_TJprWa = !0x1,
  i_JzAAJa = !0x1,
  i_bDYGQa = !0x1,
  i_GRxtZa = !0x1,
  i_HjtSei = -0x1,
  i_Mssmni = null,
  i_DFBCti = 0x0,
  i_Mhjhai = 0x0,
  i_CBwzii = 0x0;
function i_FpYmoi() {
  window[_0x3628("0x314")]("gamepadconnected", function (_0xdd4916) {
    i_iDQBme ? i_StRmhn(navigator[_0x3628("0xa")]()) : i_tCYnQs(navigator[_0x3628("0xa")]()), null == i_Mssmni && (i_Mssmni = setInterval(i_QTEksi, 0x4)), i_ZSfTwa = !0x0, null != i_MjAfV && (i_MjAfV[i_ektDj] = 0x1);
    var _0x4901f0 = _0xdd4916["gamepad"]["id"][_0x3628("0x26a")](0x0, _0xdd4916[_0x3628("0x61")]["id"][_0x3628("0x2f3")]("(Ven"));
    "" == _0x4901f0 && (_0x4901f0 = _0xdd4916[_0x3628("0x61")]["id"][_0x3628("0x26a")](0x0, _0xdd4916[_0x3628("0x61")]["id"][_0x3628("0x2f3")]("("))), "" == _0x4901f0 && (_0x4901f0 = _0xdd4916[_0x3628("0x61")]["id"]), i_rrYppt(_0x3628("0x28") + _0x4901f0 + _0x3628("0x1ae"), !0x0);
  }), window[_0x3628("0x314")](_0x3628("0x374"), function (_0x551d23) {
    if (i_iDQBme) var _0x3cdee7 = i_StRmhn(navigator[_0x3628("0xa")]());else _0x3cdee7 = i_tCYnQs(navigator[_0x3628("0xa")]());
    _0x3cdee7 <= 0x0 && (i_ZSfTwa = !0x1, null != i_MjAfV && (i_MjAfV[i_ektDj] = 0x0)), i_Mssmni && (clearInterval(i_Mssmni), i_Mssmni = null);
    var _0x4378a9 = _0x551d23["gamepad"]["id"]["substr"](0x0, _0x551d23[_0x3628("0x61")]["id"]["indexOf"](_0x3628("0x75")));
    "" == _0x4378a9 && (_0x4378a9 = _0x551d23[_0x3628("0x61")]["id"][_0x3628("0x26a")](0x0, _0x551d23["gamepad"]["id"][_0x3628("0x2f3")]("("))), "" == _0x4378a9 && (_0x4378a9 = _0x551d23[_0x3628("0x61")]["id"]), i_iZhRvt(_0x3628("0x71") + _0x4378a9 + _0x3628("0xfa"), !0x0);
  });
}
var i_djkPci = 0x0;
function i_QTEksi() {
  if (null != i_MjAfV) return clearInterval(i_Mssmni), void (i_Mssmni = null);
  i_TBswui();
}
function i_PxXari(_0x1d02a5) {
  var _0x9bfa13 = navigator["getGamepads"]()[_0x1d02a5],
    _0x2fb6d9 = 0x0;
  if (null == _0x9bfa13) return _0x2fb6d9;
  if (-0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x28a")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu["GPSetx"][_0x3628("0x28a")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_wXysKi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x22a")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x22a")]]["pressed"] && (_0x2fb6d9 |= i_PzdYOi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x12b")] && _0x9bfa13["buttons"][i_WmyDOu["GPSetx"][_0x3628("0x12b")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_PZFcNi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x38d")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")]["KeyFire2"]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_TMPKUi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x1a")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x1a")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_PZFcNi, _0x2fb6d9 |= 0x100 * i_HSeGX_ + i_HKAKDi), -0x1 != i_WmyDOu["GPSetx"][_0x3628("0x2fa")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2fa")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_TMPKUi, _0x2fb6d9 |= 0x100 * i_HSeGX_ + i_wQXx$i), 0x1 == i_WmyDOu["GPSetx"][_0x3628("0xf5")]) {
    if (0x9 == i_WmyDOu["GPSetx"][_0x3628("0x74")] || 0x9 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] || 0x9 == i_WmyDOu["GPSetx"][_0x3628("0x488")] || 0x9 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]) {
      var _0x2c33ee = 0x0 | 0xa * i_TBnkka[_0x3628("0x0")][0x9];
      if (i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")]) switch (_0x2c33ee) {
        case -0xa:
          _0x2c33ee = 0x7;
          break;
        case 0x7:
          _0x2c33ee = 0x1;
          break;
        case 0x1:
          _0x2c33ee = -0x4;
          break;
        case -0x4:
          _0x2c33ee = -0xa;
          break;
        case 0xa:
          _0x2c33ee = 0x4;
          break;
        case 0x4:
          _0x2c33ee = -0x1;
          break;
        case -0x1:
          _0x2c33ee = -0x7;
          break;
        case -0x7:
          _0x2c33ee = 0xa;
      }
      switch (_0x2c33ee) {
        case -0xa:
          _0x2fb6d9 |= i_QmFZzi;
          break;
        case 0x7:
          _0x2fb6d9 |= i_ipNXXi;
          break;
        case 0x1:
          _0x2fb6d9 |= i_ZSDiIi;
          break;
        case -0x4:
          _0x2fb6d9 |= i_xcfpRi;
          break;
        case 0xa:
          _0x2fb6d9 |= i_ipNXXi | i_QmFZzi;
          break;
        case 0x4:
          _0x2fb6d9 |= i_ipNXXi | i_ZSDiIi;
          break;
        case -0x1:
          _0x2fb6d9 |= i_xcfpRi | i_ZSDiIi;
          break;
        case -0x7:
          _0x2fb6d9 |= i_xcfpRi | i_QmFZzi;
      }
      i_WmyDOu[_0x3628("0xe1")]["gKeyRevLR"] && _0x2fb6d9 & mask_leftright && (_0x2fb6d9 ^= mask_leftright), i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")] && (_0x2fb6d9 & mask_leftright && (_0x2fb6d9 ^= mask_leftright), _0x2fb6d9 & mask_updown && (_0x2fb6d9 ^= mask_updown));
    } else i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")] ? (i_WmyDOu["GPSetx"][_0x3628("0x33f")] ? (-0x1 != i_WmyDOu["GPSetx"][_0x3628("0x74")] && _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] < -0.5 && (_0x2fb6d9 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyRight"] && 0.5 < _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] && (_0x2fb6d9 |= i_xcfpRi)) : (-0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] && 0.5 < _0x9bfa13[_0x3628("0x0")][i_WmyDOu["GPSetx"][_0x3628("0x74")]] && (_0x2fb6d9 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && _0x9bfa13["axes"][i_WmyDOu[_0x3628("0xe1")]["KeyRight"]] < -0.5 && (_0x2fb6d9 |= i_xcfpRi)), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] && 0.5 < _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")]] && (_0x2fb6d9 |= i_QmFZzi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")] && _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]] < -0.5 && (_0x2fb6d9 |= i_ZSDiIi)) : (i_WmyDOu[_0x3628("0xe1")]["gKeyRevLR"] ? (-0x1 != i_WmyDOu["GPSetx"][_0x3628("0x74")] && 0.5 < _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] && (_0x2fb6d9 |= i_ipNXXi), -0x1 != i_WmyDOu["GPSetx"][_0x3628("0x4b1")] && _0x9bfa13["axes"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] < -0.5 && (_0x2fb6d9 |= i_xcfpRi)) : (-0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyLeft"] && _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] < -0.5 && (_0x2fb6d9 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && 0.5 < _0x9bfa13["axes"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] && (_0x2fb6d9 |= i_xcfpRi)), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] && _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")]] < -0.5 && (_0x2fb6d9 |= i_QmFZzi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")] && 0.5 < _0x9bfa13[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")]["KeyDown"]] && (_0x2fb6d9 |= i_ZSDiIi));
  } else -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] && -0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyDown"] && -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] && -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && (_0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_QmFZzi), _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_ZSDiIi), _0x9bfa13["buttons"][i_WmyDOu["GPSetx"][_0x3628("0x74")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_ipNXXi), _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_xcfpRi));
  return -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x41e")] && _0x9bfa13[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x41e")]][_0x3628("0xc0")] && (_0x2fb6d9 |= i_PZFcNi | i_TMPKUi), _0x2fb6d9;
}
function i_Gjbpli() {
  var _0x7266c6 = i_PxXari(0x0),
    _0x3f1c60 = i_PxXari(0x1);
  _0x7266c6 != i_Mhjhai && (i_Mhjhai = _0x7266c6, i_jPzhfu(i_GHKWya["$e"], i_Mhjhai)), _0x3f1c60 != i_CBwzii && (i_CBwzii = _0x3f1c60, i_aCbGuu(i_GHKWya["$e"], i_CBwzii)), null != i_MjAfV && (i_MjAfV[i_SWfJY] = 0x1, Atomics[_0x3628("0x3f4")](i_MjAfV, i_SWfJY, 0x1));
}
function i_TBswui() {
  if (0x2 != i_DFBCti) {
    if ("wixMS" !== _0x3628("0x287")) {
      var _0x597831 = 0x0;
      if (null != (i_TBnkka = navigator[_0x3628("0xa")]()[i_HjtSei])) {
        if (-0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyCoin"] && i_TBnkka["buttons"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x28a")]][_0x3628("0xc0")] && (_0x597831 |= i_wXysKi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x22a")] && i_TBnkka["buttons"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x22a")]][_0x3628("0xc0")] && (_0x597831 |= i_PzdYOi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x12b")] && i_TBnkka[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x12b")]][_0x3628("0xc0")] && (_0x597831 |= i_PZFcNi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x38d")] && i_TBnkka["buttons"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x38d")]][_0x3628("0xc0")] && (_0x597831 |= i_TMPKUi), -0x1 != i_WmyDOu["GPSetx"]["KeyFire3"] && i_TBnkka[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x1a")]]["pressed"] && (_0x597831 |= 0x100 * i_HSeGX_ + i_HKAKDi), -0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyFire4"] && i_TBnkka["buttons"][i_WmyDOu[_0x3628("0xe1")]["KeyFire4"]][_0x3628("0xc0")] && (_0x597831 |= 0x100 * i_HSeGX_ + i_wQXx$i), 0x1 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")]) {
          if (0x9 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] || 0x9 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] || 0x9 == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] || 0x9 == i_WmyDOu["GPSetx"][_0x3628("0x491")]) {
            if (_0x3628("0x132") !== _0x3628("0x132")) {
              var _0x4cca99 = i_sxfci_(_0x3628("0x5d")),
                _0x4fda1b = i_sxfci_(_0x3628("0x4d3"));
              i_Hfbes_(_0x3628("0x399"), function () {
                _0x3628("0x4d3") === _0x4fda1b[_0x3628("0x4a8")] ? (_0x4cca99["style"]["display"] = "block", _0x4fda1b[_0x3628("0xea")][_0x3628("0x405")]("optionDownRev"), _0x4fda1b[_0x3628("0xea")]["remove"](_0x3628("0x4d3"))) : (_0x4cca99[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x4fda1b[_0x3628("0xea")]["add"](_0x3628("0x4d3")), _0x4fda1b[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2fc")));
              }), i_bTXGHs();
            } else {
              var _0x2ddab0 = 0x0 | 0xa * i_TBnkka[_0x3628("0x0")][0x9];
              if (i_WmyDOu["GPSetx"][_0x3628("0x2cd")]) switch (_0x2ddab0) {
                case -0xa:
                  _0x2ddab0 = 0x7;
                  break;
                case 0x7:
                  _0x2ddab0 = 0x1;
                  break;
                case 0x1:
                  _0x2ddab0 = -0x4;
                  break;
                case -0x4:
                  _0x2ddab0 = -0xa;
                  break;
                case 0xa:
                  _0x2ddab0 = 0x4;
                  break;
                case 0x4:
                  _0x2ddab0 = -0x1;
                  break;
                case -0x1:
                  _0x2ddab0 = -0x7;
                  break;
                case -0x7:
                  _0x2ddab0 = 0xa;
              }
              switch (_0x2ddab0) {
                case -0xa:
                  _0x597831 |= i_QmFZzi;
                  break;
                case 0x7:
                  _0x597831 |= i_ipNXXi;
                  break;
                case 0x1:
                  _0x597831 |= i_ZSDiIi;
                  break;
                case -0x4:
                  _0x597831 |= i_xcfpRi;
                  break;
                case 0xa:
                  _0x597831 |= i_ipNXXi | i_QmFZzi;
                  break;
                case 0x4:
                  _0x597831 |= i_ipNXXi | i_ZSDiIi;
                  break;
                case -0x1:
                  _0x597831 |= i_xcfpRi | i_ZSDiIi;
                  break;
                case -0x7:
                  _0x597831 |= i_xcfpRi | i_QmFZzi;
              }
              i_WmyDOu[_0x3628("0xe1")]["gKeyRevLR"] && _0x597831 & mask_leftright && (_0x597831 ^= mask_leftright), i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")] && (_0x597831 & mask_leftright && (_0x597831 ^= mask_leftright), _0x597831 & mask_updown && (_0x597831 ^= mask_updown));
            }
          } else i_WmyDOu["GPSetx"][_0x3628("0x2cd")] ? (i_WmyDOu["GPSetx"][_0x3628("0x33f")] ? (-0x1 != i_WmyDOu["GPSetx"]["KeyLeft"] && i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] < -0.5 && (_0x597831 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] && (_0x597831 |= i_xcfpRi)) : (-0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")]["KeyLeft"]] && (_0x597831 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && i_TBnkka[_0x3628("0x0")][i_WmyDOu["GPSetx"][_0x3628("0x4b1")]] < -0.5 && (_0x597831 |= i_xcfpRi)), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")]] && (_0x597831 |= i_QmFZzi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")] && i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]] < -0.5 && (_0x597831 |= i_ZSDiIi)) : (i_WmyDOu[_0x3628("0xe1")][_0x3628("0x33f")] ? (-0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyLeft"] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] && (_0x597831 |= i_ipNXXi), -0x1 != i_WmyDOu["GPSetx"][_0x3628("0x4b1")] && i_TBnkka["axes"][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] < -0.5 && (_0x597831 |= i_xcfpRi)) : (-0x1 != i_WmyDOu["GPSetx"]["KeyLeft"] && i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")]] < -0.5 && (_0x597831 |= i_ipNXXi), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]] && (_0x597831 |= i_xcfpRi)), -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] && i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")]] < -0.5 && (_0x597831 |= i_QmFZzi), -0x1 != i_WmyDOu["GPSetx"][_0x3628("0x491")] && 0.5 < i_TBnkka[_0x3628("0x0")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]] && (_0x597831 |= i_ZSDiIi));
        } else -0x1 != i_WmyDOu[_0x3628("0xe1")]["KeyUp"] && -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")] && -0x1 != i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] && -0x1 != i_WmyDOu["GPSetx"]["KeyRight"] && (i_TBnkka["buttons"][i_WmyDOu["GPSetx"]["KeyUp"]][_0x3628("0xc0")] && (_0x597831 |= i_QmFZzi), i_TBnkka[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x491")]]["pressed"] && (_0x597831 |= i_ZSDiIi), i_TBnkka[_0x3628("0x479")][i_WmyDOu["GPSetx"][_0x3628("0x74")]][_0x3628("0xc0")] && (_0x597831 |= i_ipNXXi), i_TBnkka[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")]][_0x3628("0xc0")] && (_0x597831 |= i_xcfpRi));
        -0x1 != i_WmyDOu["GPSetx"][_0x3628("0x41e")] && i_TBnkka[_0x3628("0x479")][i_WmyDOu[_0x3628("0xe1")][_0x3628("0x41e")]][_0x3628("0xc0")] && (_0x597831 |= i_PZFcNi | i_TMPKUi), _0x597831 != i_cdHwBa && (i_cdHwBa = _0x597831, 0x1 == i_DFBCti ? i_aCbGuu(i_GHKWya["$e"], i_cdHwBa) : i_jPzhfu(i_GHKWya["$e"], i_cdHwBa)), null != i_MjAfV && (i_MjAfV[i_SWfJY] = 0x1, Atomics[_0x3628("0x3f4")](i_MjAfV, i_SWfJY, 0x1));
      }
    } else {
      "ok" == i_yXYse["status"] ? i_hnXPpo("message", i_cbdGFn[_0x3628("0x15c") + i_BDeZzr] + "\x20被房主在本房间禁言一小时") : i_iZhRvt("禁言失败");
    }
  } else i_Gjbpli();
}
function i_AGybfi(_0x4ea29e) {
  var _0x226851 = Array[_0x3628("0x427")](_0x4ea29e) ? [] : {};
  for (var _0x4ac04d in _0x4ea29e) _0x4ea29e[_0x3628("0x2a8")](_0x4ac04d) && (_0x3628("0x266") == typeof _0x4ea29e[_0x4ac04d] && null !== _0x4ea29e[_0x4ac04d] ? _0x226851[_0x4ac04d] = i_AGybfi(_0x4ea29e[_0x4ac04d]) : _0x226851[_0x4ac04d] = _0x4ea29e[_0x4ac04d]);
  return _0x226851;
}
function i_HHPb_i() {
  return navigator[_0x3628("0xa")]()[i_HjtSei];
}
var i_FsSwvi = !0x1,
  i_pSGadi = {
    buttons: []
  };
function i_dcwFpi(_0x508491) {
  var _0x95a21e = navigator[_0x3628("0xa")]()[i_HjtSei];
  for (var _0x2a58e4 in _0x95a21e[_0x3628("0x479")]) i_pSGadi[_0x3628("0x479")][_0x2a58e4] = _0x95a21e["buttons"][_0x2a58e4][_0x3628("0xc0")];
  i_pSGadi[_0x3628("0x0")] = _0x95a21e[_0x3628("0x0")], bindNow = _0x508491, 0x1 < bindNow[_0x3628("0x27e")][_0x3628("0x2f3")]("(") ? bindNow[_0x3628("0x27e")] = bindNow[_0x3628("0x27e")][_0x3628("0x26a")](0x0, 0x2) + _0x3628("0x164") : bindNow[_0x3628("0x27e")] = bindNow[_0x3628("0x27e")][_0x3628("0x26a")](0x0, 0x1) + _0x3628("0x164"), i_FsSwvi = !0x0;
}
function i_GiDGmi(_0x3810fe) {
  var _0x324823 = _0x3810fe;
  for (var _0x3323ae in bindNow[_0x3628("0x27e")] = bindNow[_0x3628("0x27e")][_0x3628("0x16d")]("?", _0x324823), i_WmyDOu[_0x3628("0xe1")][bindNow[_0x3628("0x32d")]] = _0x3810fe, _0x3628("0x74") == bindNow[_0x3628("0x32d")] && (i_WmyDOu[_0x3628("0xe1")]["KeyJoy"] = 0x0), i_WmyDOu["GPSetx"]) if (i_WmyDOu[_0x3628("0xe1")][_0x3323ae] == _0x3810fe && _0x3323ae != bindNow[_0x3628("0x32d")]) {
    if (_0x3628("0xf5") == _0x3323ae || "KeyUp" == _0x3323ae || _0x3628("0x491") == _0x3323ae || _0x3628("0x74") == _0x3323ae || "KeyRight" == _0x3323ae) continue;
    i_WmyDOu[_0x3628("0xe1")][_0x3323ae] = -0x1, i_ZyAphi();
  }
  i_FsSwvi = !0x1;
}
function i_JMnbgi(_0x37a6de) {
  var _0x1f3f6f = _0x37a6de;
  bindNow[_0x3628("0x27e")] = bindNow["innerText"][_0x3628("0x16d")]("?", _0x1f3f6f), i_WmyDOu[_0x3628("0xe1")][bindNow["value"]] = _0x37a6de, i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x1, i_FsSwvi = !0x1;
}
function i_ZyAphi() {
  document[_0x3628("0x566")]("joybind")[_0x3628("0x64")](function (_0x269bda) {
    var _0x525468 = i_WmyDOu[_0x3628("0xe1")][_0x269bda[_0x3628("0x17c")][_0x3628("0x32d")]];
    _0x525468 = "(" + _0x525468 + ")", 0x0 < _0x269bda[_0x3628("0x17c")][_0x3628("0x27e")][_0x3628("0x2f3")]("(") && (_0x269bda["firstChild"][_0x3628("0x27e")] = _0x269bda["firstChild"][_0x3628("0x27e")][_0x3628("0x26a")](0x0, _0x269bda["firstChild"][_0x3628("0x27e")]["indexOf"]("("))), 0x1 < _0x269bda[_0x3628("0x17c")][_0x3628("0x27e")][_0x3628("0x288")] ? _0x269bda[_0x3628("0x17c")][_0x3628("0x27e")] = _0x269bda[_0x3628("0x17c")][_0x3628("0x27e")][_0x3628("0x26a")](0x0, 0x2) + _0x525468 : _0x269bda[_0x3628("0x17c")][_0x3628("0x27e")] = _0x269bda[_0x3628("0x17c")]["innerText"][_0x3628("0x26a")](0x0, 0x1) + _0x525468;
  });
}
function i_nDJmbi() {
  if (i_ZSfTwa && i_FsSwvi) {
    for (var _0x592d54 = navigator[_0x3628("0xa")]()[0x0], _0x1432c1 = 0x0; _0x1432c1 < _0x592d54[_0x3628("0x479")][_0x3628("0x288")]; _0x1432c1++) if (_0x592d54[_0x3628("0x479")][_0x1432c1][_0x3628("0xc0")] && 0x0 == i_pSGadi[_0x3628("0x479")][_0x1432c1]) {
      i_GiDGmi(_0x1432c1);
      break;
    }
    for (_0x1432c1 = 0x0; _0x1432c1 < _0x592d54[_0x3628("0x0")][_0x3628("0x288")]; _0x1432c1++) if (0.5 < Math[_0x3628("0x2f6")](_0x592d54[_0x3628("0x0")][_0x1432c1]) && _0x592d54[_0x3628("0x0")][_0x1432c1] != i_pSGadi[_0x3628("0x0")][_0x1432c1]) {
      i_JMnbgi(_0x1432c1);
      break;
    }
  }
}
function i_Hbmkyi() {
  i_RPdSEi(), i_FpYmoi();
}
var i_FjNPwi = 0x0,
  i_cPWAki = 0x0,
  i_JGprTi = 0xffff,
  i_tnMExi = 0xffff,
  i_PByhMi = !0x1,
  i_EikbPi = !0x1;
function i_RPdSEi() {
  document[_0x3628("0x314")](_0x3628("0x36e"), function (_0x57c98a) {
    if (i_EikbPi = _0x57c98a[_0x3628("0x263")], !_0x57c98a["repeat"] && !i_PByhMi && _0x3628("0x396") != _0x57c98a["key"]) {
      if (_0x57c98a[_0x3628("0x311")] && 0x0 == i_iDpHde && i_iDQBme && ("z" == _0x57c98a[_0x3628("0x376")] && (i_WmWryu(0x1), i_GJQKdt(_0x3628("0x105"))), "x" == _0x57c98a["key"] && (i_ZjZkwu(0x1), i_GJQKdt(_0x3628("0x309")))), _0x3628("0x2bf") == _0x57c98a[_0x3628("0x376")]) return i_WyfZCt(), void _0x57c98a["preventDefault"]();
      if (_0x3628("0x39a") != _0x57c98a["code"]) {
        if (_0x3628("0xc6") === _0x3628("0xc6")) {
          if (!("password" == _0x57c98a[_0x3628("0x2f9")][_0x3628("0x316")] && _0x3628("0x3eb") == _0x57c98a[_0x3628("0x2f9")]["nodeName"] || _0x3628("0x1a2") == _0x57c98a[_0x3628("0x2f9")][_0x3628("0x316")] && _0x3628("0x3eb") == _0x57c98a[_0x3628("0x2f9")][_0x3628("0x2b1")] || _0x3628("0x20a") == _0x57c98a[_0x3628("0x2f9")]["nodeName"])) if ("`" != _0x57c98a["key"]) {
            if (_0x3628("0xf6") === _0x3628("0x40b")) {
              (i_NWETLo = document[_0x3628("0x3fe")]("canvas"))["width"] = 0x3a * i_cffPrs["AB"][_0x3628("0x51f")], i_NWETLo[_0x3628("0x9")] = 0x3a * i_cffPrs["AB"][_0x3628("0x51f")], i_CwRAHo = i_NWETLo["cloneNode"](!0x0);
              var _0x53ac78 = i_NWETLo[_0x3628("0x158")]("2d");
              _0x53ac78[_0x3628("0x170")](i_cffPrs["AB"]["Size"], i_cffPrs["AB"][_0x3628("0x51f")]), _0x53ac78["globalAlpha"] = i_cffPrs["AB"][_0x3628("0x3a3")], i_HnBSkc(_0x53ac78, 0x0, 0x0, 0x3a, "AB", _0x3628("0x4e8"), _0x3628("0x14b")), (_0x53ac78 = i_CwRAHo[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["AB"][_0x3628("0x51f")], i_cffPrs["AB"][_0x3628("0x51f")]), _0x53ac78[_0x3628("0x4b7")] = i_cffPrs["AB"]["Alpha"], i_RdFdTc(_0x53ac78, 0x0, 0x0, 0x3a, "AB"), i_cffPrs["AB"][_0x3628("0x1fc")] = i_NWETLo, i_cffPrs["AB"]["Reinit"] = i_WSaRoc;
            } else {
              switch (_0x57c98a[_0x3628("0x26f")]) {
                case i_WmyDOu[_0x3628("0x46f")]["KeyUp"]:
                  i_cPWAki = -0x3 & (i_FjNPwi |= 0x1) & i_JGprTi, i_tnMExi = -0x3, i_GHKWya["_n"](i_cPWAki);
                  break;
                case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x74")]:
                  if (i_cPWAki = -0x9 & (i_FjNPwi |= 0x4), i_JGprTi = -0x9, 0xc != (0xc & i_FjNPwi) || i_WmyDOu[_0x3628("0x192")]) i_GHKWya["_n"](i_FjNPwi);else {
                    if ("Unqra" === "nqHbs") {
                      var _0x1cbd2a = i > 0x0;
                      switch (_0x1cbd2a) {
                        case !![]:
                          return this[_0x3628("0x28b")] + "_" + this["value"] + "_" + i;
                        default:
                          this[_0x3628("0x28b")] + "_" + this[_0x3628("0x32d")];
                      }
                    } else {
                      var _0x542122 = i_cPWAki;
                      i_GHKWya["_n"](_0x542122 | i_cPWAki);
                    }
                  }
                  break;
                case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x491")]:
                  i_cPWAki = (i_tnMExi = -0x2) & (i_FjNPwi |= 0x2) & i_JGprTi, i_GHKWya["_n"](i_cPWAki);
                  break;
                case i_WmyDOu[_0x3628("0x46f")]["KeyRight"]:
                  if (i_cPWAki = (i_JGprTi = -0x5) & (i_FjNPwi |= 0x8), 0xc != (0xc & i_FjNPwi) || i_WmyDOu[_0x3628("0x192")]) i_GHKWya["_n"](i_FjNPwi);else {
                    _0x542122 = i_cPWAki;
                    i_GHKWya["_n"](_0x542122 | i_cPWAki);
                  }
                  break;
                case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x12b")]:
                  i_GHKWya["en"]();
                  break;
                case i_WmyDOu["PCSet"]["KeyFire2"]:
                  i_GHKWya["tn"]();
                  break;
                case i_WmyDOu[_0x3628("0x46f")]["KeyFire3"]:
                  i_GHKWya["in"]();
                  break;
                case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x2fa")]:
                  i_GHKWya["sn"]();
                  break;
                case i_WmyDOu[_0x3628("0x46f")]["KeyStart"]:
                  i_GHKWya["We"]();
                  break;
                case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x28a")]:
                  i_GHKWya["Qe"]();
                  break;
                case i_WmyDOu["PCSet"][_0x3628("0x41e")]:
                  i_GHKWya["ln"]();
              }
              _0x57c98a[_0x3628("0x23")]();
            }
          } else i_GEMRR();
        } else {
          return i_pwBFUs(i_jGmZe, i_ieCMn, i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"], i_ChZTqo[_0x3628("0x191")], i_ChZTqo[_0x3628("0x9")]) ? (null != i_STtQt && i_STtQt != i_jhfWfc && i_STtQt(), i_TDCkWo = !0x0, i_bpztuc(), i_jhfWfc) : i_pwBFUs(i_jGmZe, i_ieCMn, i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"], i_jWfRVo["width"], i_jWfRVo[_0x3628("0x9")]) ? (null != i_STtQt && i_STtQt != i_fZAEvc && i_STtQt(), i_aJaaJo = !0x0, i_JFkE_c(), i_fZAEvc) : i_pwBFUs(i_jGmZe, i_ieCMn, i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"], i_iwDejo[_0x3628("0x191")], i_iwDejo[_0x3628("0x9")]) ? (null != i_STtQt && i_STtQt != i_JhCYpc && i_STtQt(), i_zneYQo = !0x0, i_WncXdc(), i_JhCYpc) : i_pwBFUs(i_jGmZe, i_ieCMn, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo[_0x3628("0x9")]) ? (null != i_STtQt && i_STtQt != i_TKwQgc && i_STtQt(), i_iFrkZo = !0x0, i_SkYMmc(), i_TKwQgc) : i_pwBFUs(i_jGmZe, i_ieCMn, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo["width"], i_CwRAHo[_0x3628("0x9")]) ? (null != i_STtQt && i_STtQt != i_MjRsbc && i_STtQt(), i_aZfEec = !0x0, i_ZHQnhc(), i_MjRsbc) : i_STtQt;
        }
      } else i_XxahKt();
    }
  }, !0x1), document["addEventListener"](_0x3628("0x3a7"), function (_0x1b4f1d) {
    if (i_EikbPi = _0x1b4f1d[_0x3628("0x263")], _0x3628("0x396") != _0x1b4f1d[_0x3628("0x376")]) {
      if (!_0x1b4f1d[_0x3628("0x400")]) {
        switch (_0x1b4f1d["code"]) {
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x488")]:
            i_cPWAki = (i_FjNPwi &= -0x2) & i_JGprTi, i_GHKWya["_n"](i_cPWAki);
            break;
          case i_WmyDOu[_0x3628("0x46f")]["KeyLeft"]:
            i_FjNPwi &= -0x5, i_JGprTi = -0x5, i_GHKWya["_n"](i_FjNPwi);
            break;
          case i_WmyDOu["PCSet"][_0x3628("0x491")]:
            i_cPWAki = (i_FjNPwi &= -0x3) & i_JGprTi, i_GHKWya["_n"](i_cPWAki);
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x4b1")]:
            i_FjNPwi &= -0x9, i_JGprTi = -0x9, i_GHKWya["_n"](i_FjNPwi);
            break;
          case i_WmyDOu["PCSet"][_0x3628("0x12b")]:
            i_GHKWya["nn"]();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x38d")]:
            i_GHKWya["an"]();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x1a")]:
            i_GHKWya["cn"]();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x2fa")]:
            i_GHKWya["rn"]();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x22a")]:
            i_GHKWya["Je"](), !i_CEeRff && i_iDQBme && i_kkffJf();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x28a")]:
            i_GHKWya["Ze"]();
            break;
          case i_WmyDOu[_0x3628("0x46f")][_0x3628("0x41e")]:
            i_GHKWya["un"]();
        }
        _0x1b4f1d[_0x3628("0x23")]();
      }
    } else i_CmTmwt();
  }, !0x1);
}
var i_iChyAi = 0x0,
  i_QSiQSi = 0x0;
function i_kcihCi(_0x4c07f6, _0x4b95dd) {
  i_xWZnqi(i_iChyAi = i_mDiGLi(i_iChyAi, _0x4c07f6, _0x4b95dd));
}
var i_wXysKi = 0x4,
  i_PzdYOi = 0x8,
  i_ipNXXi = 0x40,
  i_xcfpRi = 0x80,
  i_QmFZzi = 0x10,
  i_ZSDiIi = 0x20,
  i_PZFcNi = 0x1,
  i_TMPKUi = 0x2,
  i_HKAKDi = 0x4000,
  i_wQXx$i = 0x8000,
  i_TENtBi = 0xff,
  i_habnGi = 0x3f00,
  i_arFdFi = 0x100;
function i_mDiGLi(_0xe38575, _0x466b24, _0xc21f4f) {
  var _0x99f40c = _0xc21f4f;
  switch (_0x466b24) {
    case i_GHKWya["$e"]:
      _0xe38575 = _0x99f40c;
      break;
    case i_GHKWya["o"]:
      0x1 & _0x99f40c ? _0xe38575 |= i_arFdFi : _0xe38575 &= ~i_arFdFi;
      break;
    case i_GHKWya["Ye"]:
      0x1 & _0x99f40c ? _0xe38575 |= i_PZFcNi | i_TMPKUi : _0xe38575 &= ~(i_PZFcNi | i_TMPKUi);
      break;
    case i_GHKWya["Be"]:
      0x1 == _0x99f40c ? _0xe38575 |= i_PzdYOi : _0xe38575 &= ~i_PzdYOi;
      break;
    case i_GHKWya["Ge"]:
      0x1 == _0x99f40c ? _0xe38575 |= i_wXysKi : _0xe38575 &= ~i_wXysKi;
      break;
    case i_GHKWya["Le"]:
      0x0 < _0x99f40c ? (_0xe38575 |= i_PZFcNi, 0x2 == _0x99f40c && (i_QSiQSi |= i_PZFcNi, i_ebaBuo |= i_PZFcNi)) : (_0xe38575 &= ~i_PZFcNi, i_QSiQSi &= ~i_PZFcNi, i_ebaBuo &= ~i_PZFcNi);
      break;
    case i_GHKWya["qe"]:
      0x0 < _0x99f40c ? (_0xe38575 |= i_TMPKUi, 0x2 == _0x99f40c && (i_QSiQSi |= i_TMPKUi, i_ebaBuo |= i_TMPKUi)) : (_0xe38575 &= ~i_TMPKUi, i_QSiQSi &= ~i_TMPKUi, i_ebaBuo &= ~i_TMPKUi);
      break;
    case i_GHKWya["Ve"]:
      0x1 == _0x99f40c ? (_0xe38575 &= ~i_habnGi, _0xe38575 |= i_PZFcNi, _0xe38575 |= 0x100 * i_HSeGX_ + i_HKAKDi) : (_0xe38575 &= ~i_PZFcNi, _0xe38575 &= ~i_HKAKDi);
      break;
    case i_GHKWya["je"]:
      0x1 == _0x99f40c ? (_0xe38575 &= ~i_habnGi, _0xe38575 |= i_TMPKUi, _0xe38575 |= 0x100 * i_HSeGX_ + i_wQXx$i) : (_0xe38575 &= ~i_TMPKUi, _0xe38575 &= ~i_wQXx$i);
      break;
    case i_GHKWya["Fe"]:
      0x1 & _0x99f40c ? _0xe38575 |= i_QmFZzi : _0xe38575 &= ~i_QmFZzi, 0x2 & _0x99f40c ? _0xe38575 |= i_ZSDiIi : _0xe38575 &= ~i_ZSDiIi, 0x4 & _0x99f40c ? _0xe38575 |= i_ipNXXi : _0xe38575 &= ~i_ipNXXi, 0x8 & _0x99f40c ? _0xe38575 |= i_xcfpRi : _0xe38575 &= ~i_xcfpRi;
  }
  return _0xe38575;
}
function i_xWZnqi(_0x237dd1) {
  null != i_jwsYB ? i_jwsYB[0x1] = _0x237dd1 : i_zXJHnu(i_cZwcUl["vn"], 0xff & _0x237dd1, _0x237dd1 / 0x100);
}
function i_aGfyVi(_0x1e54f9) {
  i_zXJHnu(i_cZwcUl["dn"], 0xff & _0x1e54f9, _0x1e54f9 / 0x100);
}
var i_FZEFji = null,
  i_nibwYi = !0x1,
  i_dnyXHi = !0x1,
  i_thhFWi = !0x1,
  i_fdZfJi = !0x1,
  i_bPrAQi = !0x1,
  i_xjHeZi = 0x0,
  i_EHEBeo = 0x0,
  i_CfBYno = 0x0,
  i_SkHato = 0x0,
  i_sCrNao = null,
  i_MyGmio = new ArrayBuffer(0x2),
  i_Pmzpoo = new Uint16Array(i_MyGmio),
  i_aPjBco = new ArrayBuffer(0x4),
  i_wsSfso = new Uint16Array(i_aPjBco),
  i_emcHro = 0x0,
  i_Kbzklo = 0x0,
  i_ebaBuo = 0x0,
  i_fHfdfo = 0x0,
  i_hkNr_o = 0x0;
function i_fJfXvo(_0x3673ed, _0x2ac3e3) {
  0x1 == i_xjHeZi && i_XaCat(), i_dnyXHi && !i_fdZfJi && (i_sCrNao["readyState"] != WebSocket[_0x3628("0x371")] && 0x0 == i_bPrAQi || (i_Pmzpoo[0x0] = i_mDiGLi(i_Pmzpoo[0x0], _0x3673ed, _0x2ac3e3), i_wsSfso[0x0] = i_emcHro, i_wsSfso[0x1] = i_Pmzpoo[0x0], null != i_jwsYB ? (i_jwsYB[0x0] = i_wsSfso[0x0], i_jwsYB[0x1] = i_wsSfso[0x1]) : i_bYNtOo()));
}
function i_hnXPpo(_0x453313, _0x5ebce6) {
  if (_0x3628("0x2a5") == _0x453313) $[_0x3628("0x341")]("/saycheck", {
    say: _0x5ebce6,
    where: _0x3628("0xbd") + gid + "_" + i_ZmCwpe
  }, function (_0x70bf9f) {
    var _0x56c636 = {
      Info: _0x453313,
      Message: _0x70bf9f[_0x3628("0xad")]
    };
    if (0x0 <= _0x70bf9f[_0x3628("0xad")][_0x3628("0x2f3")](_0x3628("0x576"))) i_iZhRvt(_0x70bf9f[_0x3628("0xad")]);else {
      var _0x5b1ffc = JSON[_0x3628("0x2ae")](_0x56c636);
      i_sCrNao[_0x3628("0x559")](_0x5b1ffc);
    }
  });else {
    var _0x434fbf = {
        Info: _0x453313,
        Message: _0x5ebce6
      },
      _0x1d10c1 = JSON[_0x3628("0x2ae")](_0x434fbf);
    i_sCrNao[_0x3628("0x559")](_0x1d10c1);
  }
}
function i_FXdWmo() {
  i_hnXPpo("ready", "no");
}
function i_ckezgo() {
  var _0xabda29 = i_tdBda_(_0x3628("0x3d4")),
    _0x580b17 = i_tdBda_(_0x3628("0x171")),
    _0x27be06 = i_tdBda_(_0x3628("0x13d")),
    _0x32225d = i_tdBda_("rj"),
    _0x479ec4 = i_tdBda_("rl"),
    _0x1a71a6 = i_tdBda_(_0x3628("0x248")),
    _0x38dcd6 = i_tdBda_(_0x3628("0x1a5"));
  i_hkNr_o = _0xabda29, null == _0x27be06 && (_0x27be06 = ""), null == _0x32225d && (_0x32225d = !0x0), null == _0x479ec4 && (_0x479ec4 = !0x0);
  var _0x5500bf = _0x3628("0x1c") + encodeURI(_0x580b17) + _0x3628("0x492") + encodeURI(_0x27be06) + _0x3628("0x55c") + _0x32225d + "&rl=" + _0x479ec4 + _0x3628("0x2b7") + _0x38dcd6,
    _0x1cc68c = "";
  i_iDQBme || (_0x1cc68c = _0x3628("0x15f")), "" != _0x580b17 && null != _0x580b17 && !i_iDQBme && i_wFwzh && (i_sxfci_(_0x3628("0x4ae"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), setTimeout(function () {
    i_sxfci_("sharedtip")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
  }, 0xbb8)), "1" == _0x1a71a6 ? (i_sCrNao = new WebSocket(_0x3628("0x1ef") + _0xabda29 + _0x3628("0xe4") + i_ZmCwpe + "&gid=" + gid + _0x5500bf + _0x1cc68c), i_nibwYi = !0x0, i_PYYZNo()) : i_sCrNao = new WebSocket(_0x3628("0x1ef") + _0xabda29 + _0x3628("0x426") + i_ZmCwpe + "&max=2&gid=" + gid + _0x5500bf + _0x1cc68c), i_sCrNao["binaryType"] = _0x3628("0x368"), i_sCrNao[_0x3628("0xa7")] = function (_0xffd461) {}, i_sCrNao["onclose"] = function (_0x502804) {
    i_iDQBme ? (i_zkHZqn(), i_bPrAQi = !0x0) : 0x1 < i_QYKJTo && i_ZQAWMu();
  }, i_sCrNao[_0x3628("0x286")] = "1" == _0x1a71a6 ? i_ixDmzo : i_GkDzho;
}
function i_GkDzho(_0x1348a9) {
  if ("string" != typeof _0x1348a9[_0x3628("0x2e7")]) {
    if (0xa != _0x1348a9["data"][_0x3628("0x22")]) {
      if (_0x3628("0x319") === "Eeuhe") {
        var _0x1caa08 = i_PxXari(0x0),
          _0x3e54ab = i_PxXari(0x1);
        _0x1caa08 != i_Mhjhai && (i_Mhjhai = _0x1caa08, i_jPzhfu(i_GHKWya["$e"], i_Mhjhai)), _0x3e54ab != i_CBwzii && (i_CBwzii = _0x3e54ab, i_aCbGuu(i_GHKWya["$e"], i_CBwzii)), null != i_MjAfV && (i_MjAfV[i_SWfJY] = 0x1, Atomics[_0x3628("0x3f4")](i_MjAfV, i_SWfJY, 0x1));
      } else {
        if (0x10 == _0x1348a9[_0x3628("0x2e7")][_0x3628("0x22")]) {
          var _0x3ee772 = new Uint32Array(_0x1348a9[_0x3628("0x2e7")]);
          i_EHEBeo = _0x3ee772[0x0];
        }
        i_DYNBTe[_0x3628("0x3ab")](_0x1348a9["data"], [_0x1348a9[_0x3628("0x2e7")]]);
      }
    } else i_WMSdCo(_0x1348a9[_0x3628("0x2e7")]);
  } else i_mCahDo(_0x1348a9);
}
function i_DKhZbo(_0x426665, _0xa94f8) {
  i_JtyhLn(_0x426665, _0xa94f8);
}
function i_pjQNyo() {
  return i_emcHro == i_Kbzklo;
}
function i_ePsnwo() {
  i_hnXPpo("sync", "ok");
}
function i_sCnmko() {
  i_hnXPpo(_0x3628("0x372"), _0x3628("0x253"));
}
var i_QYKJTo = 0x0,
  i_brpHxo = new Uint32Array(0x1),
  i_RBpcMo = null,
  i_jEbQPo = 0x0,
  i_SRzbEo = 0x0,
  i_JmBpAo = 0x0,
  i_ApFTSo = 0x0;
function i_WMSdCo(_0x570e3c) {
  i_sCrNao[_0x3628("0x559")](_0x570e3c);
  var _0x17af3c = new Uint16Array(_0x570e3c);
  0x5 < Math[_0x3628("0x2f6")](i_jEbQPo - _0x17af3c[0x1]) && (i_DKhZbo(0x1, _0x17af3c[0x1]), i_jEbQPo = _0x17af3c[0x1]), 0x5 < Math["abs"](i_SRzbEo - _0x17af3c[0x2]) && (i_DKhZbo(0x2, _0x17af3c[0x2]), i_SRzbEo = _0x17af3c[0x2]), 0x5 < Math[_0x3628("0x2f6")](i_JmBpAo - _0x17af3c[0x3]) && (i_DKhZbo(0x3, _0x17af3c[0x3]), i_JmBpAo = _0x17af3c[0x3]), 0x5 < Math[_0x3628("0x2f6")](i_ApFTSo - _0x17af3c[0x4]) && (i_DKhZbo(0x4, _0x17af3c[0x4]), i_ApFTSo = _0x17af3c[0x4]);
}
function i_hziCKo(_0x3e6d8e) {
  "string" != typeof _0x3e6d8e[_0x3628("0x2e7")] ? i_DYNBTe[_0x3628("0x3ab")](_0x3e6d8e[_0x3628("0x2e7")], [_0x3e6d8e[_0x3628("0x2e7")]]) : i_iDQBme ? PCStringProcess(_0x3e6d8e) : StringProcess(_0x3e6d8e);
}
function i_bYNtOo() {
  if (!i_nibwYi) {
    if (null != i_jwsYB) return i_jwsYB[0x0] = i_wsSfso[0x0], void (i_jwsYB[0x1] = i_wsSfso[0x1]);
    i_DYNBTe[_0x3628("0x3ab")](i_aPjBco);
  }
}
function i_KatGXo(_0x2a276d) {
  i_sCrNao[_0x3628("0xee")] == WebSocket[_0x3628("0x371")] && i_sCrNao[_0x3628("0x559")](_0x2a276d);
}
var i_CzixRo = "";
function i_ixDmzo(_0x1516b7) {
  if (_0x3628("0x1a4") == typeof _0x1516b7[_0x3628("0x2e7")]) {
    if (_0x3628("0x335") !== _0x3628("0x127")) {
      var _0x33ec7d = JSON[_0x3628("0x1aa")](_0x1516b7[_0x3628("0x2e7")]);
      switch (_0x33ec7d[_0x3628("0x41f")]) {
        case "init":
          break;
        case "player":
          (i_FZEFji = _0x33ec7d)[_0x3628("0x3a4")] = i_FZEFji[_0x3628("0x3a4")]["replace"]("等待连接", _0x3628("0x356")), i_FZEFji[_0x3628("0x331")] = i_FZEFji[_0x3628("0x331")][_0x3628("0x16d")]("等待连接", _0x3628("0x356")), i_FZEFji[_0x3628("0x2de")] = i_FZEFji[_0x3628("0x2de")]["replace"](_0x3628("0x364"), _0x3628("0x356")), i_FZEFji[_0x3628("0x4cd")] = i_FZEFji[_0x3628("0x4cd")][_0x3628("0x16d")](_0x3628("0x364"), _0x3628("0x356")), i_emcHro = 0x5, i_iDQBme ? i_QNbFjn(i_FZEFji) : i_nFFBer(i_FZEFji);
          break;
        case _0x3628("0x372"):
          i_nYekgu(0x0, _0x33ec7d["to"]);
          break;
        case "message":
          null == _0x33ec7d[_0x3628("0x4c6")] && (_0x33ec7d[_0x3628("0x4c6")] = _0x33ec7d[_0x3628("0x1a2")], _0x33ec7d[_0x3628("0x33")] = "系统消息", _0x33ec7d[_0x3628("0x316")] = 0x0), i_iDQBme ? i_fEncgt(_0x33ec7d[_0x3628("0x33")], _0x33ec7d[_0x3628("0x4c6")], _0x33ec7d[_0x3628("0x1a2")], _0x33ec7d[_0x3628("0x316")]) : i_fGppxr(_0x33ec7d[_0x3628("0x33")], _0x33ec7d[_0x3628("0x4c6")], _0x33ec7d["text"], _0x33ec7d[_0x3628("0x316")]);
          break;
        case _0x3628("0x37c"):
          var _0x2957ab = i_tdBda_("sev");
          window[_0x3628("0x497")]["replace"]("https://play.wo1wan.com/fcnext/play?id=" + gid + _0x3628("0x4c2") + i_CzixRo + _0x3628("0x467") + _0x2957ab + _0x3628("0x18f") + i_ZmCwpe);
          break;
        case _0x3628("0x244"):
          i_sxfci_("popwin_password")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x419"))[_0x3628("0x32d")] = "", i_kzkwc_("pswcheckbtn", i_pFRCIo), i_Hfbes_("pswcheckbtn", i_pFRCIo);
          break;
        case "pwderror":
          i_iZhRvt("密码错误");
      }
    } else {
      -0x1 == i_eRzm$s ? i_WmyDOu["PCSet"] = JSON["parse"](JSON[_0x3628("0x2ae")](i_jwnmXu)) : i_WmyDOu[_0x3628("0xe1")] = JSON["parse"](JSON["stringify"](i_mACQRu)), i_NQjGjs();
    }
  } else {
    if (_0x1516b7["data"][_0x3628("0x22")] % 0xc == 0x0 || 0x8 == _0x1516b7[_0x3628("0x2e7")][_0x3628("0x22")]) new Uint32Array(_0x1516b7["data"]);
    i_DYNBTe[_0x3628("0x3ab")](_0x1516b7[_0x3628("0x2e7")], [_0x1516b7[_0x3628("0x2e7")]]);
  }
}
function i_pFRCIo() {
  i_hnXPpo(_0x3628("0x244"), i_CzixRo = i_sxfci_("pswcheckval")["value"]), i_sxfci_(_0x3628("0x4b9"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
}
function i_PYYZNo() {
  i_njFT_l = !0x0, i_iDQBme || i_yGbxPs && (i_kCpK_s(), i_sFmzCs());
}
var i_AfQpUo = 0x0,
  i_emcHro = 0x0,
  i_FZEFji = null,
  i_xjHeZi = 0x0,
  i_fdZfJi = !0x1,
  i_Kbzklo = 0x0;
function i_mCahDo(_0x552d6f) {
  if (_0x3628("0x1a4") == typeof _0x552d6f[_0x3628("0x2e7")]) {
    var _0x2daf94 = JSON[_0x3628("0x1aa")](_0x552d6f["data"]);
    switch (_0x2daf94[_0x3628("0x41f")]) {
      case _0x3628("0x4ed"):
        i_emcHro = parseInt(_0x2daf94["Num"]) - 0x1, i_wsSfso[0x0] = i_emcHro, i_wsSfso[0x1] = i_Pmzpoo[0x0], i_bYNtOo(), i_fdZfJi = !0x1, i_SxTmQe();
        break;
      case _0x3628("0x50a"):
        i_FZEFji = _0x2daf94, i_iDQBme ? i_QNbFjn(i_FZEFji) : i_nFFBer(i_FZEFji);
        var _0x1ec2eb = i_xjHeZi;
        i_xjHeZi = 0x0, _0x3628("0x550") != _0x2daf94[_0x3628("0x4af")] && i_xjHeZi++, _0x3628("0x550") != _0x2daf94[_0x3628("0x34e")] && i_xjHeZi++, _0x3628("0x550") != _0x2daf94["Img3"] && i_xjHeZi++, _0x3628("0x550") != _0x2daf94["Img4"] && i_xjHeZi++, (0x2 <= i_xjHeZi || _0x1ec2eb != i_xjHeZi) && i_XaCat(), i_Kbzklo = _0x2daf94["boss"], i_FXdWmo();
        break;
      case _0x3628("0x2d2"):
        i_sCrNao[_0x3628("0x286")] = i_GkDzho, i_dnyXHi = !0x0, i_iZEzAl["l"]();
        break;
      case _0x3628("0x3ad"):
        i_sCrNao[_0x3628("0x286")] = i_hziCKo;
        break;
      case _0x3628("0x372"):
        _0x3628("0x448") == _0x2daf94[_0x3628("0x162")] ? i_dnyXHi && i_nYekgu(0x1, _0x2daf94["to"]) : (i_nYekgu(0x0, _0x2daf94["to"]), i_dnyXHi || (i_thhFWi = !0x0, i_sCrNao[_0x3628("0x286")] = i_GkDzho)), i_CCRpou = 0x0;
        break;
      case _0x3628("0x248"):
        i_sCrNao[_0x3628("0x286")] = i_hziCKo;
        break;
      case _0x3628("0x219"):
        i_eGBAYn(_0x2daf94[_0x3628("0x2d5")]);
        break;
      case _0x3628("0x2a5"):
        "系统消息" == _0x2daf94[_0x3628("0x33")] && (0x0 < _0x2daf94["msg"]["indexOf"](_0x3628("0x578")) && (i_AfQpUo = parseInt(_0x2daf94["msg"]["substr"](0x2, 0x1))), 0x0 <= _0x2daf94[_0x3628("0x4c6")][_0x3628("0x2f3")](_0x3628("0x3d0")) && (_0x2daf94[_0x3628("0x4c6")] = "房间人数已满，即将进入观战模式", setTimeout(function () {
          if (_0x3628("0x4cc") !== "gahZp") {
            i_rrYppt(_0x3628("0x3fc"));
          } else {
            i_YkxRQc(), i_sfNrcc(), i_rXBXDc(), i_sFmzCs();
          }
        }, 0x1f4), setTimeout(function () {
          window[_0x3628("0x497")][_0x3628("0x16d")](window[_0x3628("0x497")]["origin"] + window[_0x3628("0x497")][_0x3628("0x4e5")] + "?&look=1&id=" + gid + _0x3628("0x360") + i_wesTbe + _0x3628("0x18f") + i_ZmCwpe);
        }, 0x157c))), i_iDQBme ? i_fEncgt(_0x2daf94["from"], _0x2daf94[_0x3628("0x4c6")], _0x2daf94[_0x3628("0x1a2")], _0x2daf94["type"]) : i_fGppxr(_0x2daf94[_0x3628("0x33")], _0x2daf94[_0x3628("0x4c6")], _0x2daf94[_0x3628("0x1a2")], _0x2daf94[_0x3628("0x316")]);
        break;
      case _0x3628("0x366"):
        i_GxbGz(_0x2daf94[_0x3628("0x2a5")], _0x2daf94["act"]);
        break;
      case "act":
        i_iDQBme ? i_DDAeHn(_0x2daf94[_0x3628("0x162")]) : i_rpEWtr(_0x2daf94[_0x3628("0x162")]);
        break;
      case "savereplay":
        null != _0x2daf94[_0x3628("0x26b")] && "" != _0x2daf94[_0x3628("0x26b")] && $[_0x3628("0x276")](_0x3628("0x1fe"), {
          type: _0x3628("0x4b"),
          async: !0x0,
          data: {
            type: "fc",
            gid: gid,
            name: i_FZEFji[_0x3628("0x15c") + (i_emcHro + 0x1)] + _0x3628("0x3f1"),
            rlen: _0x2daf94[_0x3628("0x532")],
            rpath: _0x2daf94[_0x3628("0x26b")]
          },
          success: function (_0x4f81d1, _0x475bdc, _0xf585df) {
            if (_0x3628("0x68") == _0x4f81d1[_0x3628("0x446")]) {
              var _0x175411 = Math["floor"](16.66666 * _0x2daf94["len"] / 0x3e8);
              i_hnXPpo(_0x3628("0x2a5"), _0x3628("0x350") + (Math[_0x3628("0x560")](_0x175411 / 0x3c) + "分" + (_0x175411 %= 0x3c) + "秒") + (_0x3628("0x533") + _0x4f81d1[_0x3628("0x1cc")] + _0x3628("0x46b")) + _0x3628("0x9d") + _0x4f81d1[_0x3628("0x1cc")] + _0x3628("0x567"));
            } else i_iZhRvt("录像保存失败");
          }
        });
    }
  }
}
var i_eTaz$o = null,
  i_eJJiBo = null,
  i_RtFbGo = null,
  i_NQhmFo = null,
  i_NWETLo = null,
  i_ChZTqo = null,
  i_jWfRVo = null,
  i_iwDejo = null,
  i_cKFiYo = null,
  i_CwRAHo = null,
  i_TDCkWo = !0x1,
  i_aJaaJo = !0x1,
  i_zneYQo = !0x1,
  i_iFrkZo = !0x1,
  i_aZfEec = !0x1;
function i_GCDjnc() {
  (i_eTaz$o = document["createElement"](_0x3628("0xff")))[_0x3628("0x191")] = 0x50 * i_cffPrs["A"][_0x3628("0x51f")], i_eTaz$o["height"] = 0x50 * i_cffPrs["A"][_0x3628("0x51f")], i_ChZTqo = i_eTaz$o[_0x3628("0x281")](!0x0);
  var _0x3032f8 = i_eTaz$o[_0x3628("0x158")]("2d");
  _0x3032f8[_0x3628("0x170")](i_cffPrs["A"][_0x3628("0x51f")], i_cffPrs["A"]["Size"]), _0x3032f8[_0x3628("0x4b7")] = i_cffPrs["A"][_0x3628("0x3a3")], i_wwBtyc(_0x3032f8, 0x0, 0x0, 0x50, "A"), (_0x3032f8 = i_ChZTqo[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["A"][_0x3628("0x51f")], i_cffPrs["A"]["Size"]), _0x3032f8[_0x3628("0x4b7")] = i_cffPrs["A"]["Alpha"], i_mfytwc(_0x3032f8, 0x0, 0x0, 0x50, "A"), i_cffPrs["A"][_0x3628("0x1fc")] = i_ChZTqo, i_cffPrs["A"][_0x3628("0x4c1")] = i_GCDjnc;
}
function i_YaPptc() {
  (i_eJJiBo = document[_0x3628("0x3fe")]("canvas"))[_0x3628("0x191")] = 0x50 * i_cffPrs["B"][_0x3628("0x51f")], i_eJJiBo[_0x3628("0x9")] = 0x50 * i_cffPrs["B"][_0x3628("0x51f")], i_jWfRVo = i_eJJiBo["cloneNode"](!0x0);
  var _0x29698f = i_eJJiBo["getContext"]("2d");
  _0x29698f["scale"](i_cffPrs["B"][_0x3628("0x51f")], i_cffPrs["B"][_0x3628("0x51f")]), _0x29698f[_0x3628("0x4b7")] = i_cffPrs["B"][_0x3628("0x3a3")], i_wwBtyc(_0x29698f, 0x0, 0x0, 0x50, "B"), (_0x29698f = i_jWfRVo[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["B"]["Size"], i_cffPrs["B"][_0x3628("0x51f")]), _0x29698f[_0x3628("0x4b7")] = i_cffPrs["B"][_0x3628("0x3a3")], i_mfytwc(_0x29698f, 0x0, 0x0, 0x50, "B"), i_cffPrs["B"][_0x3628("0x1fc")] = i_jWfRVo, i_cffPrs["B"][_0x3628("0x4c1")] = i_YaPptc;
}
function i_nApzac() {
  (i_RtFbGo = document[_0x3628("0x3fe")](_0x3628("0xff")))[_0x3628("0x191")] = 0x44 * i_cffPrs["AA"][_0x3628("0x51f")], i_RtFbGo[_0x3628("0x9")] = 0x44 * i_cffPrs["AA"]["Size"], i_iwDejo = i_RtFbGo[_0x3628("0x281")](!0x0);
  var _0x249658 = i_RtFbGo[_0x3628("0x158")]("2d");
  _0x249658[_0x3628("0x170")](i_cffPrs["AA"][_0x3628("0x51f")], i_cffPrs["AA"][_0x3628("0x51f")]), _0x249658["globalAlpha"] = i_cffPrs["AA"]["Alpha"], i_HnBSkc(_0x249658, 0x0, 0x0, 0x44, "AA", _0x3628("0x4e8"), _0x3628("0x14b")), (_0x249658 = i_iwDejo["getContext"]("2d"))["scale"](i_cffPrs["AA"]["Size"], i_cffPrs["AA"]["Size"]), _0x249658[_0x3628("0x4b7")] = i_cffPrs["AA"][_0x3628("0x3a3")], i_RdFdTc(_0x249658, 0x0, 0x0, 0x44, "AA"), i_cffPrs["AA"][_0x3628("0x1fc")] = i_RtFbGo, i_cffPrs["AA"][_0x3628("0x4c1")] = i_nApzac;
}
function i_CMfwic() {
  (i_NQhmFo = document[_0x3628("0x3fe")](_0x3628("0xff")))["width"] = 0x44 * i_cffPrs["BB"][_0x3628("0x51f")], i_NQhmFo[_0x3628("0x9")] = 0x44 * i_cffPrs["BB"][_0x3628("0x51f")], i_cKFiYo = i_NQhmFo[_0x3628("0x281")](!0x0);
  var _0x11acde = i_NQhmFo[_0x3628("0x158")]("2d");
  _0x11acde["scale"](i_cffPrs["BB"][_0x3628("0x51f")], i_cffPrs["BB"][_0x3628("0x51f")]), _0x11acde["globalAlpha"] = i_cffPrs["BB"][_0x3628("0x3a3")], i_HnBSkc(_0x11acde, 0x0, 0x0, 0x44, "BB", _0x3628("0x4e8"), _0x3628("0x14b")), (_0x11acde = i_cKFiYo[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["BB"][_0x3628("0x51f")], i_cffPrs["BB"][_0x3628("0x51f")]), _0x11acde[_0x3628("0x4b7")] = i_cffPrs["BB"][_0x3628("0x3a3")], i_RdFdTc(_0x11acde, 0x0, 0x0, 0x44, "BB"), i_cffPrs["BB"][_0x3628("0x1fc")] = i_NQhmFo, i_cffPrs["BB"][_0x3628("0x4c1")] = i_CMfwic;
}
function i_WSaRoc() {
  (i_NWETLo = document["createElement"](_0x3628("0xff")))[_0x3628("0x191")] = 0x3a * i_cffPrs["AB"][_0x3628("0x51f")], i_NWETLo["height"] = 0x3a * i_cffPrs["AB"][_0x3628("0x51f")], i_CwRAHo = i_NWETLo[_0x3628("0x281")](!0x0);
  var _0x1ada67 = i_NWETLo["getContext"]("2d");
  _0x1ada67[_0x3628("0x170")](i_cffPrs["AB"]["Size"], i_cffPrs["AB"][_0x3628("0x51f")]), _0x1ada67[_0x3628("0x4b7")] = i_cffPrs["AB"][_0x3628("0x3a3")], i_HnBSkc(_0x1ada67, 0x0, 0x0, 0x3a, "AB", _0x3628("0x4e8"), "#ffffff"), (_0x1ada67 = i_CwRAHo[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["AB"][_0x3628("0x51f")], i_cffPrs["AB"][_0x3628("0x51f")]), _0x1ada67[_0x3628("0x4b7")] = i_cffPrs["AB"][_0x3628("0x3a3")], i_RdFdTc(_0x1ada67, 0x0, 0x0, 0x3a, "AB"), i_cffPrs["AB"][_0x3628("0x1fc")] = i_NWETLo, i_cffPrs["AB"]["Reinit"] = i_WSaRoc;
}
function i_sfNrcc() {
  i_GCDjnc(), i_YaPptc(), i_nApzac(), i_CMfwic(), i_WSaRoc();
}
function i_tTcysc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"], i_ChZTqo[_0x3628("0x191")], i_ChZTqo[_0x3628("0x9")]), i_Qyfwws[_0x3628("0x23c")](i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"], i_jWfRVo[_0x3628("0x191")], i_jWfRVo["height"]), i_Qyfwws["clearRect"](i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"], i_iwDejo["width"], i_iwDejo[_0x3628("0x9")]), i_Qyfwws[_0x3628("0x23c")](i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo[_0x3628("0x9")]), i_Qyfwws["clearRect"](i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo[_0x3628("0x191")], i_CwRAHo[_0x3628("0x9")]);
}
function i_ksaPrc() {
  i_bpztuc(), i_JFkE_c(), i_WncXdc(), i_SkYMmc(), i_ZHQnhc(), i_sJjkcs && (i_Qyfwws[_0x3628("0x25e")] = _0x3628("0x3d1"), i_Qyfwws[_0x3628("0x285")] = 0x2, i_Qyfwws["shadowOffsetX"] = 0x0, i_Qyfwws["shadowOffsetY"] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws[_0x3628("0x73")] = "rgba(0,0,0,1.0)", i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws["rect"](i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"], i_ChZTqo[_0x3628("0x191")], i_ChZTqo[_0x3628("0x9")]), i_Qyfwws["stroke"](), i_Qyfwws["rect"](i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"], i_jWfRVo[_0x3628("0x191")], i_jWfRVo[_0x3628("0x9")]), i_Qyfwws[_0x3628("0x1f1")](), i_Qyfwws["rect"](i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"], i_iwDejo[_0x3628("0x191")], i_iwDejo["height"]), i_Qyfwws[_0x3628("0x1f1")](), i_Qyfwws[_0x3628("0x190")](i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo[_0x3628("0x9")]), i_Qyfwws["stroke"](), i_Qyfwws[_0x3628("0x190")](i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo[_0x3628("0x191")], i_CwRAHo[_0x3628("0x9")]), i_Qyfwws["stroke"]());
}
function i_DCpHlc(_0x3d070d, _0x14ab3b, _0x5da8e8) {
  return i_pwBFUs(_0x3d070d, _0x14ab3b, i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"], i_ChZTqo["width"], i_ChZTqo[_0x3628("0x9")]) ? (null != _0x5da8e8 && _0x5da8e8 != i_jhfWfc && _0x5da8e8(), i_TDCkWo = !0x0, i_bpztuc(), i_jhfWfc) : i_pwBFUs(_0x3d070d, _0x14ab3b, i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"], i_jWfRVo[_0x3628("0x191")], i_jWfRVo[_0x3628("0x9")]) ? (null != _0x5da8e8 && _0x5da8e8 != i_fZAEvc && _0x5da8e8(), i_aJaaJo = !0x0, i_JFkE_c(), i_fZAEvc) : i_pwBFUs(_0x3d070d, _0x14ab3b, i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"], i_iwDejo[_0x3628("0x191")], i_iwDejo[_0x3628("0x9")]) ? (null != _0x5da8e8 && _0x5da8e8 != i_JhCYpc && _0x5da8e8(), i_zneYQo = !0x0, i_WncXdc(), i_JhCYpc) : i_pwBFUs(_0x3d070d, _0x14ab3b, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo[_0x3628("0x9")]) ? (null != _0x5da8e8 && _0x5da8e8 != i_TKwQgc && _0x5da8e8(), i_iFrkZo = !0x0, i_SkYMmc(), i_TKwQgc) : i_pwBFUs(_0x3d070d, _0x14ab3b, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo[_0x3628("0x191")], i_CwRAHo["height"]) ? (null != _0x5da8e8 && _0x5da8e8 != i_MjRsbc && _0x5da8e8(), i_aZfEec = !0x0, i_ZHQnhc(), i_MjRsbc) : _0x5da8e8;
}
function i_bpztuc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"], i_ChZTqo[_0x3628("0x191")], i_ChZTqo[_0x3628("0x9")]), i_TDCkWo ? (i_GHKWya["en"](), i_Qyfwws["drawImage"](i_ChZTqo, i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"])) : (i_GHKWya["nn"](), i_Qyfwws[_0x3628("0x250")](i_eTaz$o, i_cffPrs["A"]["X"], i_cffPrs["A"]["Y"]));
}
function i_jhfWfc() {
  i_TDCkWo = !0x1, i_bpztuc();
}
function i_JFkE_c() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"], i_jWfRVo["width"], i_jWfRVo[_0x3628("0x9")]), i_aJaaJo ? (i_GHKWya["tn"](), i_Qyfwws[_0x3628("0x250")](i_jWfRVo, i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"])) : (i_GHKWya["an"](), i_Qyfwws[_0x3628("0x250")](i_eJJiBo, i_cffPrs["B"]["X"], i_cffPrs["B"]["Y"]));
}
function i_fZAEvc() {
  i_aJaaJo = !0x1, i_JFkE_c();
}
function i_WncXdc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"], i_iwDejo["width"], i_iwDejo[_0x3628("0x9")]), i_zneYQo ? (i_GHKWya["in"](), i_Qyfwws["drawImage"](i_iwDejo, i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"])) : (i_GHKWya["cn"](), i_Qyfwws[_0x3628("0x250")](i_RtFbGo, i_cffPrs["AA"]["X"], i_cffPrs["AA"]["Y"]));
}
function i_JhCYpc() {
  i_zneYQo = !0x1, i_WncXdc();
}
function i_SkYMmc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo["height"]), i_iFrkZo ? (i_GHKWya["sn"](), i_Qyfwws[_0x3628("0x250")](i_cKFiYo, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"])) : (i_GHKWya["rn"](), i_Qyfwws[_0x3628("0x250")](i_NQhmFo, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"]));
}
function i_TKwQgc() {
  i_iFrkZo = !0x1, i_SkYMmc();
}
function i_ZHQnhc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"], i_CwRAHo[_0x3628("0x191")], i_CwRAHo[_0x3628("0x9")]), i_aZfEec ? (i_GHKWya["ln"](), i_Qyfwws["drawImage"](i_CwRAHo, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"])) : (i_GHKWya["un"](), i_Qyfwws["drawImage"](i_NWETLo, i_cffPrs["AB"]["X"], i_cffPrs["AB"]["Y"]));
}
function i_MjRsbc() {
  i_aZfEec = !0x1, i_ZHQnhc();
}
function i_wwBtyc(_0x35d98a, _0x291117, _0x414c7d, _0x527cbc, _0x19acf2) {
  _0x35d98a[_0x3628("0xfb")](), _0x35d98a[_0x3628("0x445")](_0x527cbc / 0x2, _0x527cbc / 0x2, _0x527cbc / 0x2 - 0x2, 0x0, 0x2 * Math["PI"], !0x1), _0x35d98a[_0x3628("0x25e")] = _0x3628("0xc9"), _0x35d98a[_0x3628("0x94")] = 0x0, _0x35d98a["shadowOffsetY"] = 0x1, _0x35d98a[_0x3628("0x321")] = 0x2, _0x35d98a[_0x3628("0x73")] = _0x3628("0x422"), _0x35d98a[_0x3628("0x46d")] = _0x3628("0x1df"), _0x35d98a[_0x3628("0x3d9")](), _0x35d98a[_0x3628("0x324")](), _0x35d98a["beginPath"](), _0x35d98a[_0x3628("0x445")](_0x527cbc / 0x2, _0x527cbc / 0x2, _0x527cbc / 0x2 - 0x8, 0x0, 0x2 * Math["PI"], !0x1), _0x35d98a[_0x3628("0x324")](), _0x35d98a[_0x3628("0x73")] = _0x3628("0x434"), _0x35d98a[_0x3628("0x94")] = 0x0, _0x35d98a[_0x3628("0x4be")] = 0x1, _0x35d98a[_0x3628("0x321")] = 0x4, _0x35d98a["fillStyle"] = _0x3628("0x383"), _0x35d98a[_0x3628("0x3d9")](), _0x35d98a[_0x3628("0x94")] = 0x0, _0x35d98a[_0x3628("0x4be")] = 0x0, _0x35d98a[_0x3628("0x321")] = 0x0, _0x35d98a[_0x3628("0x217")] = _0x3628("0x36f"), _0x35d98a[_0x3628("0x2da")] = _0x3628("0x3ba"), _0x35d98a[_0x3628("0x37b")] = _0x3628("0x21e"), _0x35d98a["fillStyle"] = _0x3628("0x522"), _0x35d98a[_0x3628("0x1c2")](_0x19acf2, _0x527cbc / 0x2, _0x527cbc / 0x2), _0x35d98a["fillText"](_0x19acf2, _0x527cbc / 0x2, _0x527cbc / 0x2);
}
function i_mfytwc(_0x3ab3c9, _0x4d6bdc, _0x483f69, _0x27716e, _0x3a8777) {
  _0x3ab3c9[_0x3628("0xfb")](), _0x3ab3c9[_0x3628("0x445")](_0x27716e / 0x2, _0x27716e / 0x2, _0x27716e / 0x2 - 0x2, 0x0, 0x2 * Math["PI"], !0x1), _0x3ab3c9[_0x3628("0x25e")] = _0x3628("0xc9"), _0x3ab3c9[_0x3628("0x94")] = 0x0, _0x3ab3c9[_0x3628("0x4be")] = 0x0, _0x3ab3c9["shadowBlur"] = 0x5, _0x3ab3c9["shadowColor"] = _0x3628("0x27d"), _0x3ab3c9["fillStyle"] = _0x3628("0x3b6"), _0x3ab3c9[_0x3628("0x3d9")](), _0x3ab3c9["closePath"](), _0x3ab3c9["beginPath"](), _0x3ab3c9[_0x3628("0x445")](_0x27716e / 0x2, _0x27716e / 0x2, _0x27716e / 0x2 - 0x8, 0x0, 0x2 * Math["PI"], !0x1), _0x3ab3c9[_0x3628("0x324")](), _0x3ab3c9[_0x3628("0x73")] = _0x3628("0x434"), _0x3ab3c9[_0x3628("0x94")] = 0x0, _0x3ab3c9[_0x3628("0x4be")] = 0x1, _0x3ab3c9[_0x3628("0x321")] = 0x4;
  var _0x112e0d = _0x3ab3c9[_0x3628("0x277")](_0x27716e / 0x2, _0x27716e / 0x2, _0x27716e / 0x2 / 0x2 - 0x4, _0x27716e / 0x2, _0x27716e / 0x2, _0x27716e / 0x2 - 0x8);
  _0x112e0d["addColorStop"](0x0, "#F9D342"), _0x112e0d[_0x3628("0x569")](0x1, _0x3628("0x92")), _0x3ab3c9["fillStyle"] = _0x112e0d, _0x3ab3c9[_0x3628("0x3d9")](), _0x3ab3c9[_0x3628("0x94")] = 0x0, _0x3ab3c9[_0x3628("0x4be")] = 0x0, _0x3ab3c9[_0x3628("0x321")] = 0x0, _0x3ab3c9[_0x3628("0x217")] = _0x3628("0x36f"), _0x3ab3c9[_0x3628("0x2da")] = _0x3628("0x3ba"), _0x3ab3c9[_0x3628("0x37b")] = "middle", _0x3ab3c9["fillStyle"] = _0x3628("0x522"), _0x3ab3c9[_0x3628("0x1c2")](_0x3a8777, _0x27716e / 0x2, _0x27716e / 0x2), _0x3ab3c9[_0x3628("0x1c2")](_0x3a8777, _0x27716e / 0x2, _0x27716e / 0x2);
}
function i_HnBSkc(_0x4188e6, _0x39934b, _0x54550f, _0x1f9e70, _0x1e2319, _0x120265, _0x59f0cb) {
  _0x4188e6["beginPath"](), _0x4188e6[_0x3628("0x445")](_0x1f9e70 / 0x2, _0x1f9e70 / 0x2, _0x1f9e70 / 0x2 - 0x2, 0x0, 0x2 * Math["PI"], !0x1), _0x4188e6["strokeStyle"] = _0x3628("0xc9"), _0x4188e6[_0x3628("0x94")] = 0x0, _0x4188e6[_0x3628("0x4be")] = 0x1, _0x4188e6[_0x3628("0x321")] = 0x2, _0x4188e6[_0x3628("0x73")] = "rgba(0,\x200,\x200,0.8)", _0x4188e6[_0x3628("0x46d")] = _0x3628("0x1df"), _0x4188e6[_0x3628("0x3d9")](), _0x4188e6[_0x3628("0x324")](), _0x4188e6["beginPath"](), _0x4188e6["arc"](_0x1f9e70 / 0x2, _0x1f9e70 / 0x2, _0x1f9e70 / 0x2 - 0x8, 0x0, 0x2 * Math["PI"], !0x1), _0x4188e6[_0x3628("0x324")](), _0x4188e6[_0x3628("0x73")] = _0x3628("0x434"), _0x4188e6[_0x3628("0x94")] = 0x0, _0x4188e6[_0x3628("0x4be")] = 0x1, _0x4188e6[_0x3628("0x321")] = 0x4, _0x4188e6["fillStyle"] = _0x120265, _0x4188e6["fill"](), _0x4188e6[_0x3628("0x94")] = 0x0, _0x4188e6[_0x3628("0x4be")] = 0x0, _0x4188e6[_0x3628("0x321")] = 0x0, _0x4188e6[_0x3628("0x217")] = _0x3628("0x36f"), _0x4188e6[_0x3628("0x2da")] = _0x3628("0x3ba"), _0x4188e6["textBaseline"] = _0x3628("0x21e"), _0x4188e6[_0x3628("0x46d")] = _0x59f0cb, _0x4188e6["fillText"](_0x1e2319, _0x1f9e70 / 0x2, _0x1f9e70 / 0x2), _0x4188e6[_0x3628("0x1c2")](_0x1e2319, _0x1f9e70 / 0x2, _0x1f9e70 / 0x2);
}
function i_RdFdTc(_0x1d1b9b, _0x43fdad, _0x263aec, _0xabf90c, _0x4bb337, _0x4b321e) {
  _0x1d1b9b["beginPath"](), _0x1d1b9b[_0x3628("0x445")](_0xabf90c / 0x2, _0xabf90c / 0x2, _0xabf90c / 0x2 - 0x2, 0x0, 0x2 * Math["PI"], !0x1), _0x1d1b9b[_0x3628("0x25e")] = _0x3628("0xc9"), _0x1d1b9b["shadowOffsetX"] = 0x0, _0x1d1b9b[_0x3628("0x4be")] = 0x0, _0x1d1b9b[_0x3628("0x321")] = 0x5, _0x1d1b9b[_0x3628("0x73")] = "#F9D342", _0x1d1b9b[_0x3628("0x46d")] = _0x3628("0x3b6"), _0x1d1b9b[_0x3628("0x3d9")](), _0x1d1b9b["closePath"](), _0x1d1b9b["beginPath"](), _0x1d1b9b[_0x3628("0x445")](_0xabf90c / 0x2, _0xabf90c / 0x2, _0xabf90c / 0x2 - 0x8, 0x0, 0x2 * Math["PI"], !0x1), _0x1d1b9b[_0x3628("0x324")](), _0x1d1b9b["shadowColor"] = "rgba(0,0,\x200,\x200.8)", _0x1d1b9b["shadowOffsetX"] = 0x0, _0x1d1b9b[_0x3628("0x4be")] = 0x1, _0x1d1b9b[_0x3628("0x321")] = 0x4;
  var _0x19c8ce = _0x1d1b9b["createRadialGradient"](_0xabf90c / 0x2, _0xabf90c / 0x2, _0xabf90c / 0x2 / 0x2 - 0x4, _0xabf90c / 0x2, _0xabf90c / 0x2, _0xabf90c / 0x2 - 0x8);
  _0x19c8ce[_0x3628("0x569")](0x0, _0x3628("0x27d")), _0x19c8ce[_0x3628("0x569")](0x1, "#F9A942"), _0x1d1b9b[_0x3628("0x46d")] = _0x19c8ce, _0x1d1b9b["fill"](), _0x1d1b9b["shadowOffsetX"] = 0x0, _0x1d1b9b[_0x3628("0x4be")] = 0x0, _0x1d1b9b[_0x3628("0x321")] = 0x0, _0x1d1b9b[_0x3628("0x217")] = "11px\x20zcool-gdh", _0x1d1b9b[_0x3628("0x2da")] = _0x3628("0x3ba"), _0x1d1b9b[_0x3628("0x37b")] = _0x3628("0x21e"), _0x1d1b9b[_0x3628("0x46d")] = _0x3628("0x522"), _0x1d1b9b["fillText"](_0x4bb337, _0xabf90c / 0x2, _0xabf90c / 0x2), _0x1d1b9b[_0x3628("0x1c2")](_0x4bb337, _0xabf90c / 0x2, _0xabf90c / 0x2);
}
var i_THGAxc = null,
  i_fWFkMc = null,
  i_YWwjPc = null,
  i_xrnsEc = null,
  i_fjHDAc = null,
  i_wZSQSc = null,
  i_BkJeCc = null,
  i_RpTkKc = null,
  i_xrSbOc = null,
  i_sHnMXc = 0x0,
  i_yPiERc = 0xa0;
function i_YDWfzc(_0xb20023, _0x41a933, _0x400fe4, _0x5653e5) {
  var _0x5c5894 = {
    x: _0x41a933 / 0x2,
    y: _0x41a933 / 0x2,
    r: _0x41a933 / 0x2 - 0xf
  };
  _0xb20023[_0x3628("0xfb")](), _0xb20023[_0x3628("0x285")] = 0.5;
  var _0x359e89 = 0.29,
    _0x43b285 = 0.82,
    _0x2b98b4 = 0.88;
  _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x359e89, _0x5c5894["y"] - _0x5c5894["r"] * _0x43b285), _0xb20023["lineTo"](_0x5c5894["x"] + -0.1 * _0x5c5894["r"], _0x5c5894["y"] - _0x5c5894["r"] * _0x2b98b4), _0xb20023["lineTo"](_0x5c5894["x"] + 0.1 * _0x5c5894["r"], _0x5c5894["y"] - _0x5c5894["r"] * _0x2b98b4), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x359e89, _0x5c5894["y"] - _0x5c5894["r"] * _0x43b285), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], 0.4 * _0x5c5894["r"], 1.7 * Math["PI"], 1.8 * Math["PI"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x43b285, _0x5c5894["y"] + _0x5c5894["r"] * -_0x359e89), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x2b98b4, _0x5c5894["y"] - 0.1 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x2b98b4, _0x5c5894["y"] - -0.1 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x43b285, _0x5c5894["y"] - _0x5c5894["r"] * -_0x359e89), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], 0.4 * _0x5c5894["r"], 0.2 * Math["PI"], 0.3 * Math["PI"]), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * _0x359e89, _0x5c5894["y"] - _0x5c5894["r"] * -_0x43b285), _0xb20023["lineTo"](_0x5c5894["x"] + 0.1 * _0x5c5894["r"], _0x5c5894["y"] - _0x5c5894["r"] * -_0x2b98b4), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + -0.1 * _0x5c5894["r"], _0x5c5894["y"] - _0x5c5894["r"] * -_0x2b98b4), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x359e89, _0x5c5894["y"] - _0x5c5894["r"] * -_0x43b285), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], 0.4 * _0x5c5894["r"], 0.7 * Math["PI"], 0.8 * Math["PI"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x43b285, _0x5c5894["y"] - _0x5c5894["r"] * -_0x359e89), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x2b98b4, _0x5c5894["y"] - -0.1 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x2b98b4, _0x5c5894["y"] - 0.1 * _0x5c5894["r"]), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x43b285, _0x5c5894["y"] + _0x5c5894["r"] * -_0x359e89), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], 0.4 * _0x5c5894["r"], 1.2 * Math["PI"], 1.3 * Math["PI"]), _0xb20023["closePath"]();
  var _0x12aeeb = _0xb20023[_0x3628("0xab")](0x0, 0x2 * _0x5c5894["r"], 0x0, 0x0);
  _0x12aeeb["addColorStop"](0x0, _0x3628("0x3f6")), _0x12aeeb["addColorStop"](0x1, _0x3628("0x557")), _0xb20023["fillStyle"] = _0x12aeeb, _0xb20023[_0x3628("0x73")] = _0x3628("0x165"), _0xb20023[_0x3628("0x321")] = 0x2, _0xb20023["shadowOffsetY"] = 0x5, _0xb20023[_0x3628("0x3d9")](), _0xb20023[_0x3628("0xfb")](), _0xb20023["strokeStyle"] = _0x3628("0x34a"), _0xb20023["lineWidth"] = 0x4;
  var _0x25db82 = 0.27,
    _0x1b4540 = 0.85;
  _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x25db82, _0x5c5894["y"] - _0x5c5894["r"] * _0x1b4540), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + -0.1 * _0x5c5894["r"], _0x5c5894["y"] - 0.9 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + 0.1 * _0x5c5894["r"], _0x5c5894["y"] - 0.9 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x25db82, _0x5c5894["y"] - _0x5c5894["r"] * _0x1b4540), _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * _0x1b4540, _0x5c5894["y"] + _0x5c5894["r"] * -_0x25db82), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + 0.9 * _0x5c5894["r"], _0x5c5894["y"] - 0.1 * _0x5c5894["r"]), _0xb20023["lineTo"](_0x5c5894["x"] + 0.9 * _0x5c5894["r"], _0x5c5894["y"] - -0.1 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x1b4540, _0x5c5894["y"] - _0x5c5894["r"] * -_0x25db82), _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * _0x25db82, _0x5c5894["y"] - _0x5c5894["r"] * -_0x1b4540), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + 0.1 * _0x5c5894["r"], _0x5c5894["y"] - -0.9 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + -0.1 * _0x5c5894["r"], _0x5c5894["y"] - -0.9 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x25db82, _0x5c5894["y"] - _0x5c5894["r"] * -_0x1b4540), _0xb20023["moveTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x1b4540, _0x5c5894["y"] - _0x5c5894["r"] * -_0x25db82), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + -0.9 * _0x5c5894["r"], _0x5c5894["y"] - -0.1 * _0x5c5894["r"]), _0xb20023["lineTo"](_0x5c5894["x"] + -0.9 * _0x5c5894["r"], _0x5c5894["y"] - 0.1 * _0x5c5894["r"]), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x1b4540, _0x5c5894["y"] + _0x5c5894["r"] * -_0x25db82), _0xb20023[_0x3628("0x4be")] = 0x0, _0xb20023[_0x3628("0x321")] = 0x0, _0xb20023["stroke"](), _0xb20023[_0x3628("0xfb")]();
  var _0x30931b = _0xb20023[_0x3628("0xab")](0x0, 0x2 * _0x5c5894["r"], 0x0, 0x0);
  _0x30931b[_0x3628("0x569")](0x0, _0x3628("0x123")), _0x30931b[_0x3628("0x569")](0x1, _0x3628("0x1f4")), _0xb20023[_0x3628("0x25e")] = _0x30931b, _0xb20023[_0x3628("0x285")] = 0x1;
  var _0x5c232b = 0.55,
    _0x211d45 = 0.67,
    _0x5950e8 = 0.66;
  _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5c232b, _0x5c5894["y"] - _0x5c5894["r"] * _0x211d45), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5950e8, _0x5c5894["y"] - _0x5c5894["r"] * _0x5950e8), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5950e8 + (_0x211d45 - _0x5950e8), _0x5c5894["y"] - _0x5c5894["r"] * _0x5c232b), _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5c232b, _0x5c5894["y"] - _0x5c5894["r"] * -_0x211d45), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5950e8, _0x5c5894["y"] - _0x5c5894["r"] * -_0x5950e8), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * -_0x5950e8 + (_0x211d45 - _0x5950e8), _0x5c5894["y"] - _0x5c5894["r"] * -_0x5c232b), _0xb20023[_0x3628("0x42a")](_0x5c5894["x"] + _0x5c5894["r"] * _0x5c232b, _0x5c5894["y"] - _0x5c5894["r"] * -_0x211d45), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x5950e8, _0x5c5894["y"] - _0x5c5894["r"] * -_0x5950e8), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x5950e8 + (_0x211d45 - _0x5950e8), _0x5c5894["y"] - _0x5c5894["r"] * -_0x5c232b), _0xb20023["moveTo"](_0x5c5894["x"] + _0x5c5894["r"] * _0x5c232b, _0x5c5894["y"] - _0x5c5894["r"] * _0x211d45), _0xb20023[_0x3628("0x2e4")](_0x5c5894["x"] + _0x5c5894["r"] * _0x5950e8, _0x5c5894["y"] - _0x5c5894["r"] * _0x5950e8), _0xb20023["lineTo"](_0x5c5894["x"] + _0x5c5894["r"] * _0x5950e8 + (_0x211d45 - _0x5950e8), _0x5c5894["y"] - _0x5c5894["r"] * _0x5c232b), _0xb20023[_0x3628("0x1f1")](), _0xb20023[_0x3628("0xfb")](), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], _0x5c5894["r"] + 0xa, 0x0, 0x2 * Math["PI"]);
  var _0x226a22 = _0xb20023[_0x3628("0xab")](0x0, 0x2 * _0x5c5894["r"] + 0x14, 0x0, -0xa);
  _0x226a22[_0x3628("0x569")](0x1, "rgba(102,\x20101,\x2099,\x200.6)"), _0x226a22[_0x3628("0x569")](0x0, _0x3628("0x444")), _0xb20023[_0x3628("0x46d")] = _0x226a22, _0xb20023["fill"](), _0xb20023[_0x3628("0xfb")](), _0xb20023[_0x3628("0x285")] = 1.5, _0xb20023["lineCap"] = _0x3628("0x2ef"), _0xb20023[_0x3628("0x445")](_0x5c5894["x"], _0x5c5894["y"], _0x5c5894["r"] + 0xa + 0.1, Math["PI"], 0x0);
  var _0x502fe3 = _0xb20023[_0x3628("0xab")](0x0, 0x2 * _0x5c5894["r"] + 0x14, 0x0, -0xa);
  _0x502fe3[_0x3628("0x569")](0x0, "rgba(102,\x20101,\x2099,\x200.5)"), _0x502fe3["addColorStop"](0.5, "rgba(102,\x20101,\x2099,\x20\x200.5)"), _0x502fe3[_0x3628("0x569")](0x1, _0x3628("0x380")), _0xb20023[_0x3628("0x25e")] = _0x502fe3, _0xb20023[_0x3628("0x1f1")]();
}
function i_mCpnIc(_0x3ae97c, _0x5a84fa, _0x11abf0, _0x11d383, _0x2ae438) {
  var _0x94e679 = {
      x: _0x5a84fa / 0x2,
      y: _0x5a84fa / 0x2,
      r: _0x5a84fa / 0x2 - 0xf
    },
    _0x8949a1 = 0x0,
    _0x420f5d = 0x0,
    _0x3a19c3 = 0x0,
    _0x4f23c0 = 0x0;
  switch (_0x2ae438) {
    case 0x1:
      _0x420f5d = -0.05, _0x4f23c0 = -3.5;
      break;
    case 1.5:
      _0x420f5d = _0x8949a1 = -0.05, _0x4f23c0 = -(_0x3a19c3 = 3.5);
      break;
    case 0x2:
      _0x8949a1 = -0.05, _0x3a19c3 = 3.5;
      break;
    case 2.5:
      _0x8949a1 = -0.05, _0x420f5d = 0.05, _0x4f23c0 = _0x3a19c3 = 3.5;
      break;
    case 0x3:
      _0x420f5d = 0.05, _0x4f23c0 = 3.5;
      break;
    case 3.5:
      _0x3a19c3 = -3.5, _0x420f5d = _0x8949a1 = 0.05, _0x4f23c0 = 3.5;
      break;
    case 0x4:
      _0x8949a1 = 0.05, _0x3a19c3 = -3.5;
      break;
    case 4.5:
      _0x420f5d = -(_0x8949a1 = 0.05), _0x4f23c0 = _0x3a19c3 = -3.5;
  }
  _0x3ae97c["beginPath"]();
  var _0x398155 = 0.29,
    _0x5dce1e = 0.82,
    _0x437ead = 0.88;
  _0x3ae97c["moveTo"](_0x94e679["x"] + _0x94e679["r"] * -(_0x398155 + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (_0x5dce1e - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(0.1 + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (_0x437ead - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * (0.1 - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (_0x437ead - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * (_0x398155 - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (_0x5dce1e - _0x420f5d)), _0x3ae97c[_0x3628("0x445")](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0, 0.4 * _0x94e679["r"], 1.7 * Math["PI"], 1.8 * Math["PI"]), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * (_0x5dce1e - _0x8949a1), _0x94e679["y"] + _0x94e679["r"] * -(_0x398155 - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * (_0x437ead - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (0.1 - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * (_0x437ead - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(0.1 + _0x420f5d)), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x94e679["r"] * (_0x5dce1e - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x398155 + _0x420f5d)), _0x3ae97c["arc"](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0, 0.4 * _0x94e679["r"], 0.2 * Math["PI"], 0.3 * Math["PI"]), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x94e679["r"] * (_0x398155 - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x5dce1e + _0x420f5d)), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x94e679["r"] * (0.1 - _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x437ead + _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(0.1 + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x437ead + _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(_0x398155 + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x5dce1e + _0x420f5d)), _0x3ae97c[_0x3628("0x445")](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0, 0.4 * _0x94e679["r"], 0.7 * Math["PI"], 0.8 * Math["PI"]), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x94e679["r"] * -(_0x5dce1e + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(_0x398155 + _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(_0x437ead + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * -(0.1 + _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(_0x437ead + _0x8949a1), _0x94e679["y"] - _0x94e679["r"] * (0.1 - _0x420f5d)), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x94e679["r"] * -(_0x5dce1e + _0x8949a1), _0x94e679["y"] + _0x94e679["r"] * -(_0x398155 - _0x420f5d)), _0x3ae97c[_0x3628("0x445")](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0, 0.4 * _0x94e679["r"], 1.2 * Math["PI"], 1.3 * Math["PI"]), _0x3ae97c[_0x3628("0x324")]();
  var _0x3a7558 = _0x3ae97c["createLinearGradient"](0x0, 0x0, 0x0, 0x2 * _0x94e679["r"]);
  _0x3a7558[_0x3628("0x569")](0x0, "rgba(70,\x2070,\x2070,\x201.0)"), _0x3a7558[_0x3628("0x569")](0x1, _0x3628("0x557")), _0x3ae97c["fillStyle"] = _0x3a7558, _0x3ae97c[_0x3628("0x73")] = _0x3628("0x3de"), _0x3ae97c[_0x3628("0x321")] = 0x2, _0x3ae97c[_0x3628("0x4be")] = 0x2;
  var _0x160a86 = _0x3ae97c["createLinearGradient"](0x0, 0x2 * _0x94e679["r"], 0x0, 0x0);
  _0x160a86[_0x3628("0x569")](0x0, "rgba(90,90,90,\x201.0)"), _0x160a86[_0x3628("0x569")](0x1, _0x3628("0x113")), _0x3ae97c[_0x3628("0x25e")] = _0x160a86, _0x3ae97c["lineJoin"] = "round", _0x3ae97c["lineWidth"] = 0x3, _0x3ae97c["shadowBlur"] = 0x0, _0x3ae97c[_0x3628("0x4be")] = 0x0, _0x3ae97c["stroke"](), _0x3ae97c[_0x3628("0x3d9")](), _0x3ae97c[_0x3628("0xfb")](), _0x3ae97c[_0x3628("0x445")](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0, 0.25 * _0x94e679["r"], 0x0, 0x2 * Math["PI"]);
  var _0x1f3806 = _0x3ae97c["createLinearGradient"](0xa, _0x5a84fa / 0x2 * 1.25, 0xa, _0x5a84fa / 0x2 / 1.25);
  _0x1f3806[_0x3628("0x569")](0x0, "rgba(84,\x2083,\x2081,\x201.0)"), _0x1f3806[_0x3628("0x569")](0x1, "rgba(23,21,21,1.0)"), _0x3ae97c["fillStyle"] = _0x1f3806, _0x3ae97c[_0x3628("0x321")] = 0x0, _0x3ae97c["shadowOffsetY"] = 0x0, _0x3ae97c[_0x3628("0x3d9")](), _0x3ae97c[_0x3628("0xfb")]();
  var _0x1b0a49 = 0.13,
    _0x2f9a36 = 0.65;
  _0x3ae97c["moveTo"](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0 - 0.8 * _0x94e679["r"]), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * _0x1b0a49, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * _0x2f9a36), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x3a19c3 - _0x94e679["r"] * _0x1b0a49, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * _0x2f9a36), _0x3ae97c[_0x3628("0x42a")](_0x94e679["x"] + _0x3a19c3, _0x94e679["y"] + _0x4f23c0 - -0.8 * _0x94e679["r"]), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * -_0x1b0a49, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * -_0x2f9a36), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 - _0x94e679["r"] * -_0x1b0a49, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * -_0x2f9a36), _0x3ae97c[_0x3628("0x42a")](_0x94e679["x"] + _0x3a19c3 + 0.8 * _0x94e679["r"], _0x94e679["y"] + _0x4f23c0), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * _0x2f9a36, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * -_0x1b0a49), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * _0x2f9a36, _0x94e679["y"] + _0x4f23c0 + _0x94e679["r"] * -_0x1b0a49), _0x3ae97c[_0x3628("0x42a")](_0x94e679["x"] + _0x3a19c3 + -0.8 * _0x94e679["r"], _0x94e679["y"] + _0x4f23c0), _0x3ae97c[_0x3628("0x2e4")](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * -_0x2f9a36, _0x94e679["y"] + _0x4f23c0 - _0x94e679["r"] * _0x1b0a49), _0x3ae97c["lineTo"](_0x94e679["x"] + _0x3a19c3 + _0x94e679["r"] * -_0x2f9a36, _0x94e679["y"] + _0x4f23c0 + _0x94e679["r"] * _0x1b0a49);
  var _0x1a42d6 = _0x3ae97c[_0x3628("0xab")](0x0, _0x94e679["r"], 0x0, 0x5 * _0x94e679["r"]);
  _0x1a42d6[_0x3628("0x569")](0x1, _0x3628("0x295")), _0x1a42d6["addColorStop"](0x0, _0x3628("0x4da")), _0x3ae97c["fillStyle"] = _0x1a42d6, _0x3ae97c[_0x3628("0x73")] = _0x3628("0x47c"), _0x3ae97c["shadowBlur"] = 0x0, _0x3ae97c[_0x3628("0x4be")] = -0x1, _0x3ae97c[_0x3628("0x324")](), _0x3ae97c[_0x3628("0x3d9")]();
}
function i_nkNQNc(_0x148bb8, _0xbeff29, _0x1850b0) {
  _0x148bb8[_0x3628("0xfb")]();
  var _0x3534a9 = {
      x: _0xbeff29 / 0x2,
      y: _0xbeff29 / 0x2,
      r: _0xbeff29 / 0x2 - 0xf
    },
    _0x20d0d6 = _0x3534a9["x"],
    _0x4f9cde = _0x3534a9["y"],
    _0x57bd73 = _0x3534a9["r"];
  _0x148bb8[_0x3628("0x94")] = 0x0, _0x148bb8[_0x3628("0x4be")] = 0x0, _0x148bb8[_0x3628("0x321")] = 0x5, _0x148bb8[_0x3628("0x73")] = _0x3628("0x126"), _0x148bb8[_0x3628("0x25e")] = _0x3628("0x408"), _0x148bb8[_0x3628("0x285")] = 0x6, _0x148bb8[_0x3628("0x3f0")] = "square", 0x1 === _0x1850b0 ? _0x148bb8[_0x3628("0x445")](_0x20d0d6, _0x4f9cde, _0x57bd73 + 0x7, 0x19 * Math["PI"] / 0x12, 0x1d * Math["PI"] / 0x12) : 1.5 === _0x1850b0 ? _0x148bb8["arc"](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 29.3 * Math["PI"] / 0x12, 33.7 * Math["PI"] / 0x12) : 0x2 === _0x1850b0 ? _0x148bb8["arc"](_0x3534a9["x"], _0x3534a9["y"], _0x57bd73 + 0x7, 0x22 * Math["PI"] / 0x12, 0x2 * Math["PI"] / 0x12) : 2.5 === _0x1850b0 ? _0x148bb8[_0x3628("0x445")](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 2.3 * Math["PI"] / 0x12, 6.7 * Math["PI"] / 0x12) : 0x3 === _0x1850b0 ? _0x148bb8[_0x3628("0x445")](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 0x7 * Math["PI"] / 0x12, 0xb * Math["PI"] / 0x12) : 3.5 === _0x1850b0 ? _0x148bb8[_0x3628("0x445")](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 11.3 * Math["PI"] / 0x12, 15.7 * Math["PI"] / 0x12) : 0x4 === _0x1850b0 ? _0x148bb8[_0x3628("0x445")](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 0x10 * Math["PI"] / 0x12, 0x14 * Math["PI"] / 0x12) : 4.5 === _0x1850b0 && (_0x148bb8["arc"](_0x3534a9["x"], _0x3534a9["y"], _0x3534a9["r"] + 0x7, 20.5 * Math["PI"] / 0x12, 24.5 * Math["PI"] / 0x12), _0x148bb8["strokeStyle"] = _0x3628("0x3d1")), _0x148bb8[_0x3628("0x1f1")](), _0x148bb8[_0x3628("0x324")](), _0x148bb8[_0x3628("0xfb")]();
}
function i_tJNMUc(_0x372d87, _0x303b6b, _0x81286e, _0x7bb54c, _0x192c52) {
  i_YDWfzc(_0x372d87, _0x303b6b, _0x81286e, _0x7bb54c), i_mCpnIc(_0x372d87, _0x303b6b, _0x81286e, _0x7bb54c, _0x192c52), i_nkNQNc(_0x372d87, _0x303b6b, _0x192c52);
}
function i_rXBXDc() {
  var _0x4e17fc = i_yPiERc,
    _0x4fd257 = i_yPiERc;
  (i_THGAxc = document[_0x3628("0x3fe")](_0x3628("0xff")))[_0x3628("0x191")] = _0x4e17fc * i_cffPrs[_0x3628("0x394")]["Size"], i_THGAxc[_0x3628("0x9")] = _0x4fd257 * i_cffPrs[_0x3628("0x394")]["Size"];
  var _0x26b484 = i_THGAxc[_0x3628("0x158")]("2d");
  _0x26b484[_0x3628("0x170")](i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")], i_cffPrs["Pad"]["Size"]), _0x26b484[_0x3628("0x4b7")] = i_cffPrs[_0x3628("0x394")]["Alpha"], i_tJNMUc(_0x26b484, _0x4e17fc, !0x1, !0x1, 0x0), (_0x26b484 = (i_fWFkMc = i_THGAxc["cloneNode"](!0x0))[_0x3628("0x158")]("2d"))["scale"](i_cffPrs[_0x3628("0x394")]["Size"], i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")]), _0x26b484[_0x3628("0x4b7")] = i_cffPrs["Pad"][_0x3628("0x3a3")], i_tJNMUc(_0x26b484, _0x4e17fc, !0x0, !0x1, 0x1), (_0x26b484 = (i_fjHDAc = i_THGAxc["cloneNode"](!0x0))["getContext"]("2d"))["scale"](i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")], i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")]), _0x26b484["globalAlpha"] = i_cffPrs[_0x3628("0x394")]["Alpha"], _0x26b484[_0x3628("0x3f3")](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484[_0x3628("0x3f3")](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x0, !0x1, 0x2), (_0x26b484 = (i_YWwjPc = i_THGAxc[_0x3628("0x281")](!0x0))[_0x3628("0x158")]("2d"))["scale"](i_cffPrs["Pad"][_0x3628("0x51f")], i_cffPrs["Pad"][_0x3628("0x51f")]), _0x26b484["globalAlpha"] = i_cffPrs[_0x3628("0x394")]["Alpha"], _0x26b484[_0x3628("0x3f3")](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484[_0x3628("0x3f3")](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x0, !0x1, 0x3), (_0x26b484 = (i_xrnsEc = i_THGAxc[_0x3628("0x281")](!0x0))[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs["Pad"][_0x3628("0x51f")], i_cffPrs["Pad"][_0x3628("0x51f")]), _0x26b484[_0x3628("0x4b7")] = i_cffPrs[_0x3628("0x394")][_0x3628("0x3a3")], _0x26b484[_0x3628("0x3f3")](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484[_0x3628("0x3f3")](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x0, !0x1, 0x4), (_0x26b484 = (i_wZSQSc = i_THGAxc[_0x3628("0x281")](!0x0))["getContext"]("2d"))[_0x3628("0x170")](i_cffPrs[_0x3628("0x394")]["Size"], i_cffPrs["Pad"][_0x3628("0x51f")]), _0x26b484[_0x3628("0x4b7")] = i_cffPrs[_0x3628("0x394")][_0x3628("0x3a3")], i_tJNMUc(_0x26b484, _0x4e17fc, !0x1, !0x0, 1.5), (_0x26b484 = (i_BkJeCc = i_THGAxc[_0x3628("0x281")](!0x0))[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")], i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")]), _0x26b484[_0x3628("0x4b7")] = i_cffPrs["Pad"]["Alpha"], _0x26b484[_0x3628("0x3f3")](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484[_0x3628("0x3f3")](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x1, !0x0, 2.5), (_0x26b484 = (i_xrSbOc = i_THGAxc["cloneNode"](!0x0))["getContext"]("2d"))[_0x3628("0x170")](i_cffPrs["Pad"][_0x3628("0x51f")], i_cffPrs["Pad"][_0x3628("0x51f")]), _0x26b484["globalAlpha"] = i_cffPrs[_0x3628("0x394")][_0x3628("0x3a3")], _0x26b484["translate"](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484["translate"](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x1, !0x0, 3.5), (_0x26b484 = (i_RpTkKc = i_THGAxc["cloneNode"](!0x0))[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")], i_cffPrs["Pad"][_0x3628("0x51f")]), _0x26b484["globalAlpha"] = i_cffPrs[_0x3628("0x394")][_0x3628("0x3a3")], _0x26b484[_0x3628("0x3f3")](_0x4e17fc / 0x2, _0x4fd257 / 0x2), _0x26b484[_0x3628("0x3f3")](-_0x4e17fc / 0x2, -_0x4fd257 / 0x2), i_tJNMUc(_0x26b484, _0x4e17fc, !0x1, !0x0, 4.5), i_cffPrs["Pad"][_0x3628("0x1fc")] = i_THGAxc, i_cffPrs["Pad"]["Reinit"] = i_rXBXDc;
}
function i_FwbC$c() {
  switch (i_Qyfwws["clearRect"](i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs["Pad"]["Y"], i_THGAxc[_0x3628("0x191")], i_THGAxc[_0x3628("0x9")]), i_sHnMXc) {
    case 0x0:
      i_Qyfwws[_0x3628("0x250")](i_THGAxc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
      break;
    case 0x1:
      i_Qyfwws[_0x3628("0x250")](i_fWFkMc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs["Pad"]["Y"]);
      break;
    case 0x2:
      i_Qyfwws[_0x3628("0x250")](i_YWwjPc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
      break;
    case 0x4:
      i_Qyfwws[_0x3628("0x250")](i_xrnsEc, i_cffPrs["Pad"]["X"], i_cffPrs["Pad"]["Y"]);
      break;
    case 0x5:
      i_Qyfwws["drawImage"](i_RpTkKc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
      break;
    case 0x6:
      i_Qyfwws[_0x3628("0x250")](i_xrSbOc, i_cffPrs["Pad"]["X"], i_cffPrs["Pad"]["Y"]);
      break;
    case 0x8:
      i_Qyfwws["drawImage"](i_fjHDAc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
      break;
    case 0x9:
      i_Qyfwws[_0x3628("0x250")](i_wZSQSc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs["Pad"]["Y"]);
      break;
    case 0xa:
      i_Qyfwws[_0x3628("0x250")](i_BkJeCc, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
      break;
    default:
      i_Qyfwws[_0x3628("0x250")](i_THGAxc, i_cffPrs["Pad"]["X"], i_cffPrs[_0x3628("0x394")]["Y"]);
  }
  i_sJjkcs && (i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws[_0x3628("0x190")](i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"], i_THGAxc[_0x3628("0x191")], i_THGAxc[_0x3628("0x9")]), i_Qyfwws[_0x3628("0x324")](), i_Qyfwws[_0x3628("0x25e")] = "rgba(255,\x20255,\x20255,1.0)", i_Qyfwws[_0x3628("0x285")] = 0x2, i_Qyfwws[_0x3628("0x94")] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws["shadowBlur"] = 0x2, i_Qyfwws["shadowColor"] = "rgba(0,0,0,1.0)", i_Qyfwws["stroke"]());
}
function i_TrbXBc(_0x401514, _0x216911) {
  if (!i_pwBFUs(_0x401514, _0x216911, i_cffPrs[_0x3628("0x394")]["X"] - 0x64, i_cffPrs[_0x3628("0x394")]["Y"] - 0x64, 2.5 * i_THGAxc[_0x3628("0x191")], 2.5 * i_THGAxc[_0x3628("0x9")])) return null;
  var _0x358782 = _0x401514 - (i_cffPrs[_0x3628("0x394")]["X"] + i_yPiERc * i_cffPrs[_0x3628("0x394")]["Size"] / 0x2),
    _0x4d8844 = _0x216911 - (i_cffPrs[_0x3628("0x394")]["Y"] + i_yPiERc * i_cffPrs["Pad"][_0x3628("0x51f")] / 0x2),
    _0x50a711 = 0x0,
    _0x5bf1af = i_yPiERc * i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")] / 0x6;
  return _0x5bf1af < _0x358782 && (_0x50a711 |= 0x8), _0x358782 < -_0x5bf1af && (_0x50a711 |= 0x4), _0x4d8844 < -_0x5bf1af && (_0x50a711 |= 0x1), _0x5bf1af < _0x4d8844 && (_0x50a711 |= 0x2), i_GHKWya["_n"](_0x50a711), i_sHnMXc = _0x50a711, i_FwbC$c(), i_GGskFc;
}
function i_XAECGc(_0x585886, _0x3ffd56, _0x4323f3) {
  if (!i_pwBFUs(_0x585886, _0x3ffd56, i_cffPrs[_0x3628("0x394")]["X"], i_cffPrs[_0x3628("0x394")]["Y"], i_THGAxc[_0x3628("0x191")], i_THGAxc[_0x3628("0x9")]) && !_0x4323f3) return null;
  var _0x4b9db7 = _0x585886 - (i_cffPrs[_0x3628("0x394")]["X"] + i_yPiERc * i_cffPrs["Pad"][_0x3628("0x51f")] / 0x2),
    _0x5a5860 = _0x3ffd56 - (i_cffPrs[_0x3628("0x394")]["Y"] + i_yPiERc * i_cffPrs[_0x3628("0x394")][_0x3628("0x51f")] / 0x2),
    _0x4bc65e = Math[_0x3628("0x2f6")](_0x4b9db7),
    _0x4f95f9 = Math["abs"](_0x5a5860),
    _0x34e3e9 = 0x0;
  return i_sHnMXc = (0x8 < _0x4bc65e && 0x8 < _0x4f95f9 && (_0x4f95f9 < _0x4bc65e && _0x4bc65e / 1.8 < _0x4f95f9 || _0x4f95f9 / 1.8 < _0x4bc65e && _0x4bc65e < _0x4f95f9 || _0x4bc65e == _0x4f95f9) ? (0x8 < _0x4b9db7 && (_0x34e3e9 |= 0x8), _0x4b9db7 < -0x8 && (_0x34e3e9 |= 0x4), _0x5a5860 < -0x8 && (_0x34e3e9 |= 0x1), 0x8 < _0x5a5860 && (_0x34e3e9 |= 0x2)) : _0x4f95f9 < _0x4bc65e ? 0x8 < _0x4b9db7 ? _0x34e3e9 |= 0x8 : _0x4b9db7 < -0x8 && (_0x34e3e9 |= 0x4) : _0x5a5860 < -0x8 ? _0x34e3e9 |= 0x1 : 0x8 < _0x5a5860 && (_0x34e3e9 |= 0x2), i_GHKWya["_n"](_0x34e3e9), _0x34e3e9), i_FwbC$c(), i_GGskFc;
}
function i_GGskFc() {
  i_sHnMXc = 0x0, i_GHKWya["_n"](0x0), i_FwbC$c();
}
var i_FhPPLc = null,
  i_Kyjiqc = null,
  i_wYRxVc = null,
  i_hnswjc = null,
  i_xbdtYc = !0x1,
  i_pQYsHc = !0x1;
function i_rhZKWc() {
  (i_wYRxVc = document[_0x3628("0x3fe")](_0x3628("0xff")))["width"] = 0x38 * i_cffPrs[_0x3628("0x1f3")][_0x3628("0x51f")], i_wYRxVc["height"] = 0x38 * i_cffPrs[_0x3628("0x1f3")][_0x3628("0x51f")], i_hnswjc = i_wYRxVc[_0x3628("0x281")](!0x0);
  var _0x3bfe3e = i_wYRxVc[_0x3628("0x158")]("2d");
  _0x3bfe3e[_0x3628("0x170")](i_cffPrs[_0x3628("0x1f3")]["Size"], i_cffPrs[_0x3628("0x1f3")][_0x3628("0x51f")]), _0x3bfe3e["globalAlpha"] = i_cffPrs[_0x3628("0x1f3")][_0x3628("0x3a3")], i_wwBtyc(_0x3bfe3e, 0x0, 0x0, 0x38, "开始"), (_0x3bfe3e = i_hnswjc[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs[_0x3628("0x1f3")][_0x3628("0x51f")], i_cffPrs[_0x3628("0x1f3")][_0x3628("0x51f")]), _0x3bfe3e[_0x3628("0x4b7")] = i_cffPrs["Start"][_0x3628("0x3a3")], i_mfytwc(_0x3bfe3e, 0x0, 0x0, 0x38, "开始"), i_cffPrs[_0x3628("0x1f3")]["Obj"] = i_hnswjc, i_cffPrs["Start"]["Reinit"] = i_rhZKWc;
}
function i_KpDwJc() {
  (i_FhPPLc = document["createElement"](_0x3628("0xff")))["width"] = 0x38 * i_cffPrs[_0x3628("0x4cb")]["Size"], i_FhPPLc[_0x3628("0x9")] = 0x38 * i_cffPrs[_0x3628("0x4cb")]["Size"], i_Kyjiqc = i_FhPPLc[_0x3628("0x281")](!0x0);
  var _0x2fd836 = i_FhPPLc["getContext"]("2d");
  _0x2fd836[_0x3628("0x170")](i_cffPrs["Select"][_0x3628("0x51f")], i_cffPrs["Select"]["Size"]), _0x2fd836["globalAlpha"] = i_cffPrs[_0x3628("0x4cb")]["Alpha"], i_wwBtyc(_0x2fd836, 0x0, 0x0, 0x38, "选择"), (_0x2fd836 = i_Kyjiqc[_0x3628("0x158")]("2d"))[_0x3628("0x170")](i_cffPrs[_0x3628("0x4cb")][_0x3628("0x51f")], i_cffPrs[_0x3628("0x4cb")][_0x3628("0x51f")]), _0x2fd836["globalAlpha"] = i_cffPrs[_0x3628("0x4cb")][_0x3628("0x3a3")], i_mfytwc(_0x2fd836, 0x0, 0x0, 0x38, "选择"), i_cffPrs[_0x3628("0x4cb")]["Obj"] = i_Kyjiqc, i_cffPrs[_0x3628("0x4cb")][_0x3628("0x4c1")] = i_KpDwJc;
}
function i_YkxRQc() {
  i_KpDwJc(), i_rhZKWc();
}
function i_PPBfZc() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs["Select"]["X"], i_cffPrs[_0x3628("0x4cb")]["Y"], i_Kyjiqc[_0x3628("0x191")], i_Kyjiqc["height"]), i_Qyfwws["clearRect"](i_cffPrs[_0x3628("0x1f3")]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"], i_hnswjc[_0x3628("0x191")], i_hnswjc["height"]);
}
function i_PbXses() {
  i_EmRets(), i_ftnNas();
}
function i_rBzRns(_0x575499, _0x35f939) {
  return i_pwBFUs(_0x575499, _0x35f939, i_cffPrs[_0x3628("0x4cb")]["X"], i_cffPrs[_0x3628("0x4cb")]["Y"], i_Kyjiqc[_0x3628("0x191")], i_Kyjiqc[_0x3628("0x9")]) ? (i_xbdtYc = !0x0, i_EmRets(), i_cDyZis) : i_pwBFUs(_0x575499, _0x35f939, i_cffPrs[_0x3628("0x1f3")]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"], i_hnswjc[_0x3628("0x191")], i_hnswjc["height"]) ? (i_pQYsHc = !0x0, i_ftnNas(), i_tjmbos) : void 0x0;
}
function i_EmRets() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs[_0x3628("0x4cb")]["X"], i_cffPrs[_0x3628("0x4cb")]["Y"], i_Kyjiqc["width"], i_Kyjiqc[_0x3628("0x9")]), i_xbdtYc ? (i_GHKWya["Qe"](), i_Qyfwws[_0x3628("0x250")](i_Kyjiqc, i_cffPrs[_0x3628("0x4cb")]["X"], i_cffPrs["Select"]["Y"])) : (i_GHKWya["Ze"](), i_Qyfwws[_0x3628("0x250")](i_FhPPLc, i_cffPrs[_0x3628("0x4cb")]["X"], i_cffPrs[_0x3628("0x4cb")]["Y"])), i_sJjkcs && (i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws["rect"](i_cffPrs[_0x3628("0x4cb")]["X"], i_cffPrs[_0x3628("0x4cb")]["Y"], i_Kyjiqc[_0x3628("0x191")], i_Kyjiqc[_0x3628("0x9")]), i_Qyfwws["strokeStyle"] = _0x3628("0x3d1"), i_Qyfwws[_0x3628("0x285")] = 0x2, i_Qyfwws[_0x3628("0x94")] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws[_0x3628("0x73")] = _0x3628("0x47c"), i_Qyfwws["stroke"]());
}
function i_ftnNas() {
  i_Qyfwws[_0x3628("0x23c")](i_cffPrs[_0x3628("0x1f3")]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"], i_hnswjc["width"], i_hnswjc[_0x3628("0x9")]), i_pQYsHc ? (i_GHKWya["We"](), i_Qyfwws["drawImage"](i_hnswjc, i_cffPrs["Start"]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"])) : (i_GHKWya["Je"](), i_Qyfwws[_0x3628("0x250")](i_wYRxVc, i_cffPrs[_0x3628("0x1f3")]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"])), i_sJjkcs && (i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws[_0x3628("0x190")](i_cffPrs[_0x3628("0x1f3")]["X"], i_cffPrs[_0x3628("0x1f3")]["Y"], i_hnswjc[_0x3628("0x191")], i_hnswjc[_0x3628("0x9")]), i_Qyfwws["strokeStyle"] = "rgba(255,\x20255,\x20255,1.0)", i_Qyfwws[_0x3628("0x285")] = 0x2, i_Qyfwws["shadowOffsetX"] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws[_0x3628("0x73")] = "rgba(0,0,0,1.0)", i_Qyfwws[_0x3628("0x1f1")]());
}
function i_cDyZis() {
  i_xbdtYc = !0x1, i_EmRets();
}
function i_tjmbos() {
  i_pQYsHc = !0x1, i_ftnNas();
}
var i_sJjkcs = !0x1,
  i_Wairss = !0x1,
  i_cffPrs = {
    GamePadType: 0x1,
    Pad: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0x1,
      Size: 0x1
    },
    Select: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0x1,
      Size: 0x1
    },
    Start: {
      X: 0xc8,
      Y: 0xc8,
      Alpha: 0x1,
      Size: 0x1
    },
    A: {
      X: 0x64,
      Y: 0x64,
      Alpha: 0x1,
      Size: 0x1
    },
    B: {
      X: 0xc8,
      Y: 0xc8,
      Alpha: 0x1,
      Size: 0x1
    },
    AA: {
      X: 0xc8,
      Y: 0xc8,
      Alpha: 0x1,
      Size: 0x1
    },
    BB: {
      X: 0xc8,
      Y: 0xc8,
      Alpha: 0x1,
      Size: 0x1
    },
    AB: {
      X: 0xc8,
      Y: 0xc8,
      Alpha: 0x1,
      Size: 0x1
    }
  },
  i_YWSWls = null,
  i_tTwrus = null,
  i_esSxfs = !0x1;
function i_kCpK_s() {
  if (null != i_tTwrus && null != i_tTwrus || (i_tTwrus = i_Qyfwws["drawImage"]), i_Qyfwws[_0x3628("0x250")] = i_Wairss || i_njFT_l ? function () {} : i_tTwrus, !i_esSxfs || 0x0 == i_cffPrs[_0x3628("0x4cb")]["X"] && 0x0 == i_cffPrs["Select"]["Y"]) {
    var _0xafef66 = i_RFyyys[_0x3628("0x191")],
      _0x20d2c1 = i_RFyyys[_0x3628("0x9")];
    i_cffPrs["Pad"]["X"] = 0x2a, i_cffPrs[_0x3628("0x394")]["Y"] = _0x20d2c1 - 0xb4, i_cffPrs[_0x3628("0x4cb")]["Y"] = _0x20d2c1 - 0x50, i_cffPrs["Select"]["X"] = _0xafef66 / 0x2 - 0x3c, i_cffPrs[_0x3628("0x1f3")]["Y"] = _0x20d2c1 - 0x50, i_cffPrs[_0x3628("0x1f3")]["X"] = _0xafef66 / 0x2 + 0xa, i_cffPrs["B"]["X"] = _0xafef66 - 0xbe, i_cffPrs["B"]["Y"] = _0x20d2c1 - 0x58, i_cffPrs["A"]["X"] = _0xafef66 - 0x64, i_cffPrs["A"]["Y"] = _0x20d2c1 - 0x5c, i_cffPrs["BB"]["X"] = _0xafef66 - 0xaf, i_cffPrs["BB"]["Y"] = _0x20d2c1 - 0xa5, i_cffPrs["AA"]["X"] = _0xafef66 - 0x5f, i_cffPrs["AA"]["Y"] = _0x20d2c1 - 0xaa, i_cffPrs["AB"]["X"] = _0xafef66 - 0x5f, i_cffPrs["AB"]["Y"] = _0x20d2c1 - 0xe6;
  } else i_sFmzCs();
}
function i_HMEnvs() {}
function i_PYfcds() {
  i_sJjkcs = !0x0, i_YWSWls = i_cffPrs[_0x3628("0x394")], i_sFmzCs();
}
function i_eTfNps(_0x54f53f, _0xf7d93d) {
  for (var _0x3f68b0 in i_cffPrs) if (_0x3628("0x228") != _0x3f68b0 && null != i_cffPrs[_0x3f68b0]["Obj"] && null != i_cffPrs[_0x3f68b0][_0x3628("0x1fc")] && !i_cffPrs[_0x3f68b0][_0x3628("0x4eb")] && i_pwBFUs(_0x54f53f, _0xf7d93d, i_cffPrs[_0x3f68b0]["X"], i_cffPrs[_0x3f68b0]["Y"], i_cffPrs[_0x3f68b0]["Obj"][_0x3628("0x191")], i_cffPrs[_0x3f68b0][_0x3628("0x1fc")][_0x3628("0x9")])) return i_YWSWls = i_cffPrs[_0x3f68b0], $(_0x3628("0x53e"))["val"](((0x64 * i_YWSWls[_0x3628("0x51f")] - 0x32) / 1.5)[_0x3628("0x249")](0x0))["trigger"](_0x3628("0x440")), $(_0x3628("0x8b"))["val"]((0x64 * i_YWSWls[_0x3628("0x3a3")])[_0x3628("0x249")](0x0))["trigger"](_0x3628("0x440")), i_cffPrs[_0x3f68b0];
  return null;
}
function i_xweams(_0x24fdaa, _0x4d31e2, _0x5bba95) {
  for (var _0xa2c4cb in _0x24fdaa = Math["floor"](_0x24fdaa), _0x4d31e2 = Math["floor"](_0x4d31e2), i_cffPrs) if (_0x3628("0x228") != _0xa2c4cb && null != i_cffPrs[_0xa2c4cb]["Obj"] && null != i_cffPrs[_0xa2c4cb][_0x3628("0x1fc")] && (i_cffPrs[_0xa2c4cb]["X"] != _0x5bba95["X"] || i_cffPrs[_0xa2c4cb]["Y"] != _0x5bba95["Y"]) && !i_cffPrs[_0xa2c4cb][_0x3628("0x4eb")] && i_ZdjBDs(_0x24fdaa, _0x4d31e2, _0x5bba95["Obj"][_0x3628("0x191")], _0x5bba95["Obj"]["height"], i_cffPrs[_0xa2c4cb]["X"], i_cffPrs[_0xa2c4cb]["Y"], i_cffPrs[_0xa2c4cb][_0x3628("0x1fc")][_0x3628("0x191")], i_cffPrs[_0xa2c4cb][_0x3628("0x1fc")]["height"])) return !0x1;
  return !0x0;
}
function i_nszags() {
  i_sJjkcs = !0x1, i_YWSWls = null, i_sFmzCs();
}
function i_CByehs(_0x1b3b3a) {
  null != i_YWSWls && (i_YWSWls[_0x3628("0x51f")] = _0x1b3b3a, i_YWSWls[_0x3628("0x4c1")](), i_sFmzCs());
}
function i_Wzzebs(_0x47c3c1) {
  null != i_YWSWls && (i_YWSWls["Alpha"] = _0x47c3c1, i_YWSWls[_0x3628("0x4c1")](), i_sFmzCs());
}
var i_RFyyys = null,
  i_Qyfwws = null,
  i_ZdSiks = !0x1,
  i_GaPNTs = {},
  i_pEEBxs = {},
  i_BQAJMs = {},
  i_yGbxPs = !0x1;
function i_mrnFEs(_0xb13f72) {
  var _0x16087c = window["orientation"];
  void 0x0 === _0x16087c && null != window[_0x3628("0x3f5")][_0x3628("0x2e2")] && (_0x16087c = window[_0x3628("0x3f5")][_0x3628("0x2e2")][_0x3628("0x2cc")]), 0xb4 !== _0x16087c && 0x0 !== _0x16087c || (i_ZdSiks = !0x0), 0x5a !== _0x16087c && -0x5a !== _0x16087c || (i_ZdSiks = !0x1);
}
function i_PkTzAs() {
  if (i_yGbxPs) i_YJFTSs();else {
    i_yGbxPs = !0x0;
    var _0x369c3e = i_sxfci_("phonewrap");
    (i_RFyyys = i_sxfci_(_0x3628("0xa9")))["width"] = _0x369c3e[_0x3628("0xb3")], i_RFyyys[_0x3628("0x9")] = _0x369c3e[_0x3628("0x188")], i_Qyfwws = i_RFyyys["getContext"]("2d"), i_kCpK_s(), i_YkxRQc(), i_sfNrcc(), i_rXBXDc(), i_ksaPrc(), i_PbXses(), i_FwbC$c(), i_RFyyys[_0x3628("0x314")]("touchstart", i_YpeJOs), i_RFyyys["addEventListener"](_0x3628("0x87"), i_HSYDXs), i_RFyyys[_0x3628("0x314")]("touchend", i_REscRs), i_RFyyys[_0x3628("0x314")]("touchcancel", i_REscRs), window[_0x3628("0x314")](_0x3628("0x55e") in window ? "orientationchange" : "resize", function () {
      setTimeout(i_mrnFEs, 0x1f4);
    }, !0x1), window[_0x3628("0x314")]("resize", function () {
      setTimeout(function () {
        i_RFyyys[_0x3628("0x191")] = _0x369c3e[_0x3628("0xb3")], i_RFyyys[_0x3628("0x9")] = _0x369c3e["clientHeight"], i_sFmzCs();
      }, 0x1f4);
    }, !0x1), i_mrnFEs();
  }
}
function i_YJFTSs() {
  i_YkxRQc(), i_sfNrcc(), i_rXBXDc(), i_sFmzCs();
}
function i_sFmzCs() {
  i_Qyfwws["clearRect"](0x0, 0x0, i_RFyyys[_0x3628("0x191")], i_RFyyys[_0x3628("0x9")]), i_ksaPrc(), i_PbXses(), i_FwbC$c(), null != i_YWSWls ? (i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws[_0x3628("0x190")](i_YWSWls["X"], i_YWSWls["Y"], i_YWSWls[_0x3628("0x1fc")][_0x3628("0x191")], i_YWSWls[_0x3628("0x1fc")][_0x3628("0x9")]), i_Qyfwws[_0x3628("0x25e")] = _0x3628("0x383"), i_Qyfwws[_0x3628("0x285")] = 0x3, i_Qyfwws[_0x3628("0x94")] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws[_0x3628("0x73")] = _0x3628("0x47c"), i_Qyfwws[_0x3628("0x1f1")]()) : i_Qyfwws[_0x3628("0x321")] = 0x0;
}
function i_QtJCKs(_0x3e8a52, _0x3692a9) {
  if (i_ZdSiks) var _0x1b2652 = _0x3692a9,
    _0x31b1b5 = i_RFyyys[_0x3628("0x9")] - _0x3e8a52;else _0x1b2652 = _0x3e8a52, _0x31b1b5 = _0x3692a9;
  return {
    pn: Math[_0x3628("0x560")](_0x1b2652),
    mn: Math[_0x3628("0x560")](_0x31b1b5)
  };
}
function i_YpeJOs(_0x4af08e) {
  if (i_bhkMMf || i_RttEHf(), i_MSkKnl && i_kymCil(), i_sJjkcs) i_TTCDzs(_0x4af08e);else {
    for (var _0x1180a6 = 0x0; _0x1180a6 < _0x4af08e[_0x3628("0x3db")]["length"]; _0x1180a6 += 0x1) {
      var _0x1c8d29 = _0x4af08e[_0x3628("0x3db")][_0x1180a6],
        _0x34c9c7 = i_QtJCKs(_0x1c8d29[_0x3628("0x325")], _0x1c8d29[_0x3628("0x16e")]),
        _0x8ce950 = _0x34c9c7["pn"],
        _0x2f7369 = _0x34c9c7["mn"];
      i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] = i_DCpHlc(_0x8ce950, _0x2f7369, null), null != i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_pEEBxs[_0x1c8d29[_0x3628("0x203")]] = "abc"), null == i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] = i_XAECGc(_0x8ce950, _0x2f7369, !0x1), null != i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_pEEBxs[_0x1c8d29[_0x3628("0x203")]] = _0x3628("0x27c"))), null == i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] = i_rBzRns(_0x8ce950, _0x2f7369)), null == i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] = i_TrbXBc(_0x8ce950, _0x2f7369), null != i_GaPNTs[_0x1c8d29[_0x3628("0x203")]] && (i_pEEBxs[_0x1c8d29[_0x3628("0x203")]] = _0x3628("0x27c")));
    }
    _0x4af08e[_0x3628("0x23")]();
  }
}
function i_HSYDXs(_0x506365) {
  if (i_sJjkcs) i_HpfjIs(_0x506365);else for (var _0x102c98 = 0x0; _0x102c98 < _0x506365[_0x3628("0x3db")]["length"]; _0x102c98 += 0x1) {
    var _0x3a4795 = _0x506365[_0x3628("0x3db")][_0x102c98],
      _0x51983e = i_QtJCKs(_0x3a4795[_0x3628("0x325")], _0x3a4795[_0x3628("0x16e")]),
      _0x381cdb = _0x51983e["pn"],
      _0x1732b5 = _0x51983e["mn"];
    _0x3628("0x27c") === i_pEEBxs[_0x3a4795["identifier"]] ? i_XAECGc(_0x381cdb, _0x1732b5, !0x0) : (i_pEEBxs[_0x3a4795[_0x3628("0x203")]], i_GaPNTs[_0x3a4795[_0x3628("0x203")]] = i_DCpHlc(_0x381cdb, _0x1732b5, i_GaPNTs[_0x3a4795[_0x3628("0x203")]]));
  }
}
function i_REscRs(_0x565c06) {
  if (i_sJjkcs) i_GhtMNs(_0x565c06);else {
    for (var _0xfebbb1 = 0x0; _0xfebbb1 < _0x565c06[_0x3628("0x3db")]["length"]; _0xfebbb1 += 0x1) {
      if (_0x3628("0xe3") !== "DvoAj") {
        var _0x20c9f1 = _0x565c06[_0x3628("0x3db")][_0xfebbb1];
        null != i_GaPNTs[_0x20c9f1["identifier"]] && null != i_GaPNTs[_0x20c9f1[_0x3628("0x203")]] && (i_GaPNTs[_0x20c9f1[_0x3628("0x203")]](), i_GaPNTs[_0x20c9f1[_0x3628("0x203")]] = null, i_pEEBxs[_0x20c9f1[_0x3628("0x203")]] = null);
      } else {
        for (var _0x2a2e5b = i_bDxpe[_0x3628("0x2f9")][_0x3628("0x456")]; _0x2a2e5b["id"][_0x3628("0x2f3")](_0x3628("0x49e")) < 0x0;) _0x2a2e5b = _0x2a2e5b[_0x3628("0x456")];
        _0x2a2e5b["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), i_JcAsUu();
      }
    }
    _0x565c06["preventDefault"]();
  }
}
function i_TTCDzs(_0x49e4ee) {
  for (var _0x5ba6cd = 0x0; _0x5ba6cd < _0x49e4ee[_0x3628("0x3db")][_0x3628("0x288")]; _0x5ba6cd += 0x1) {
    if ("WeBUt" === _0x3628("0x3c")) {
      i_cbdGFn = i_NdQwe, v_gui_pclink_updatetime = performance[_0x3628("0x30b")](), i_sxfci_(_0x3628("0x2f5"))[_0x3628("0x1c5")] = i_NdQwe[_0x3628("0x4af")], i_sxfci_("p2img")[_0x3628("0x1c5")] = i_NdQwe[_0x3628("0x34e")], i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = i_NdQwe[_0x3628("0x4e7")], i_sxfci_("p4img")["src"] = i_NdQwe[_0x3628("0x3a5")], i_sxfci_("p1nick")["innerText"] = i_NdQwe[_0x3628("0x3a4")], i_sxfci_("p2nick")["innerText"] = i_NdQwe["Nick2"], i_sxfci_(_0x3628("0x521"))[_0x3628("0x27e")] = i_NdQwe["Nick3"], i_sxfci_(_0x3628("0x54e"))["innerText"] = i_NdQwe[_0x3628("0x4cd")], i_sxfci_(_0x3628("0x3f7"))[_0x3628("0x27e")] = i_NdQwe[_0x3628("0x3a4")], i_sxfci_("Bp2nick")[_0x3628("0x27e")] = i_NdQwe[_0x3628("0x331")], i_sxfci_("Bp3nick")["innerText"] = i_NdQwe[_0x3628("0x2de")], i_sxfci_(_0x3628("0x326"))[_0x3628("0x27e")] = i_NdQwe[_0x3628("0x4cd")], i_sxfci_(_0x3628("0x178"))["src"] = i_NdQwe["Img1"], i_sxfci_("Bp2img")["src"] = i_NdQwe[_0x3628("0x34e")], i_sxfci_("Bp3img")[_0x3628("0x1c5")] = i_NdQwe[_0x3628("0x4e7")], i_sxfci_(_0x3628("0xbc"))[_0x3628("0x1c5")] = i_NdQwe[_0x3628("0x3a5")], 0x0 < i_NdQwe[_0x3628("0x3a4")][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x2f5"))["src"] = "./img/close.png", i_sxfci_(_0x3628("0x178"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < i_NdQwe["Nick2"][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x262"))["src"] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0x14"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < i_NdQwe[_0x3628("0x2de")]["indexOf"]("关闭") && (i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0x3c6"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < i_NdQwe["Nick4"][_0x3628("0x2f3")]("关闭") && (i_sxfci_("p4img")[_0x3628("0x1c5")] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0xbc"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), i_NdQwe[_0x3628("0x2a1")] ? (i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_("p1dev")[_0x3628("0xea")][_0x3628("0x405")]("igwi-shouji")) : (i_sxfci_(_0x3628("0x2a4"))["classList"][_0x3628("0x405")]("igwi-diannao-copy"), i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x47a")]("igwi-shouji")), i_NdQwe[_0x3628("0x20e")] ? (i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")]["remove"](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0xc2"))) : (i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x149"))["classList"][_0x3628("0x47a")]("igwi-shouji")), i_NdQwe[_0x3628("0x548")] ? (i_sxfci_(_0x3628("0x412"))["classList"][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x412"))[_0x3628("0xea")]["add"](_0x3628("0xc2"))) : (i_sxfci_("p3dev")[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x412"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xc2"))), i_NdQwe[_0x3628("0xb0")] ? (i_sxfci_(_0x3628("0x2d3"))["classList"][_0x3628("0x47a")]("igwi-diannao-copy"), i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")]["add"]("igwi-shouji")) : (i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")]["add"](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xc2"))), i_XPSbZs(i_NdQwe[_0x3628("0x76")]), i_sxfci_(_0x3628("0x439"))[_0x3628("0x27e")] = i_NdQwe[_0x3628("0x41b")], i_sxfci_(_0x3628("0x3dc"))[_0x3628("0x27e")] = "(" + i_NdQwe[_0x3628("0x41b")] + ")";
      var _0x505b53 = i_sxfci_(_0x3628("0x3b"));
      for (var _0x637f09 in _0x505b53[_0x3628("0x24d")] = "", i_NdQwe["LookerNick"]) {
        var _0x1c685a = document["createElement"]("div");
        _0x1c685a["setAttribute"](_0x3628("0x376"), i_NdQwe[_0x3628("0x397")][_0x637f09]), _0x1c685a[_0x3628("0x24d")] = _0x3628("0x101") + i_NdQwe["LookerNick"][_0x637f09] + _0x3628("0xda"), _0x1c685a["className"] = _0x3628("0x2df"), _0x505b53[_0x3628("0xd6")](_0x1c685a);
      }
      v_gui_ismatch_allow = !0x1;
    } else {
      var _0x226ebc = _0x49e4ee[_0x3628("0x3db")][_0x5ba6cd],
        _0x4c56b6 = i_QtJCKs(_0x226ebc["clientX"], _0x226ebc[_0x3628("0x16e")]),
        _0x13d1a1 = i_eTfNps(_0x4c56b6["pn"], _0x4c56b6["mn"]);
      null != _0x13d1a1 && (i_BQAJMs[_0x226ebc[_0x3628("0x203")]] = _0x13d1a1, i_YWSWls = _0x13d1a1, i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws[_0x3628("0x190")](_0x13d1a1["X"], _0x13d1a1["Y"], _0x13d1a1[_0x3628("0x1fc")]["width"], _0x13d1a1[_0x3628("0x1fc")][_0x3628("0x9")]), i_Qyfwws[_0x3628("0x25e")] = _0x3628("0x383"), i_Qyfwws[_0x3628("0x285")] = 0x3, i_Qyfwws["shadowOffsetX"] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws["shadowColor"] = _0x3628("0x47c"), i_Qyfwws[_0x3628("0x1f1")](), i_sFmzCs());
    }
  }
  _0x49e4ee[_0x3628("0x23")]();
}
function i_HpfjIs(_0xe4f44d) {
  for (var _0x380946 = 0x0; _0x380946 < _0xe4f44d[_0x3628("0x3db")]["length"]; _0x380946 += 0x1) {
    var _0x52dcb1 = _0xe4f44d["changedTouches"][_0x380946],
      _0xc53a3b = i_QtJCKs(_0x52dcb1[_0x3628("0x325")], _0x52dcb1[_0x3628("0x16e")]),
      _0x5ede8a = _0xc53a3b["pn"],
      _0x1939b5 = _0xc53a3b["mn"],
      _0x2ded4b = i_BQAJMs[_0x52dcb1[_0x3628("0x203")]];
    if (null != _0x2ded4b && null != _0x2ded4b) {
      var _0x35f978 = Math[_0x3628("0x560")](_0x5ede8a - _0x2ded4b[_0x3628("0x1fc")]["width"] / 0x2),
        _0x2fadfa = Math[_0x3628("0x560")](_0x1939b5 - _0x2ded4b["Obj"][_0x3628("0x9")] / 0x2);
      if (!i_xweams(_0x35f978, _0x2fadfa, _0x2ded4b)) if (i_xweams(_0x2ded4b["X"], _0x2fadfa, _0x2ded4b)) _0x35f978 = _0x2ded4b["X"];else {
        if (!i_xweams(_0x35f978, _0x2ded4b["Y"], _0x2ded4b)) continue;
        _0x2fadfa = _0x2ded4b["Y"];
      }
      _0x2ded4b["X"] = _0x35f978, _0x2ded4b["Y"] = _0x2fadfa, _0x2ded4b["X"] < 0x0 && (_0x2ded4b["X"] = 0x0), _0x2ded4b["Y"] < 0x0 && (_0x2ded4b["Y"] = 0x0), _0x2ded4b["X"] + _0x2ded4b[_0x3628("0x1fc")][_0x3628("0x191")] > i_RFyyys[_0x3628("0x191")] && (_0x2ded4b["X"] = i_RFyyys[_0x3628("0x191")] - _0x2ded4b[_0x3628("0x1fc")]["width"]), _0x2ded4b["Y"] + _0x2ded4b[_0x3628("0x1fc")][_0x3628("0x9")] > i_RFyyys["height"] && (_0x2ded4b["Y"] = i_RFyyys[_0x3628("0x9")] - _0x2ded4b[_0x3628("0x1fc")][_0x3628("0x9")]), i_sFmzCs(), i_Qyfwws[_0x3628("0xfb")](), i_Qyfwws[_0x3628("0x190")](_0x2ded4b["X"], _0x2ded4b["Y"], _0x2ded4b[_0x3628("0x1fc")][_0x3628("0x191")], _0x2ded4b[_0x3628("0x1fc")][_0x3628("0x9")]), i_Qyfwws[_0x3628("0x25e")] = _0x3628("0x383"), i_Qyfwws[_0x3628("0x285")] = 0x3, i_Qyfwws[_0x3628("0x94")] = 0x0, i_Qyfwws[_0x3628("0x4be")] = 0x0, i_Qyfwws[_0x3628("0x321")] = 0x2, i_Qyfwws["shadowColor"] = _0x3628("0x47c"), i_Qyfwws[_0x3628("0x1f1")]();
    }
  }
  _0xe4f44d[_0x3628("0x23")]();
}
function i_GhtMNs(_0x3d9238) {
  for (var _0xa40ec2 = 0x0; _0xa40ec2 < _0x3d9238[_0x3628("0x3db")][_0x3628("0x288")]; _0xa40ec2 += 0x1) {
    var _0x544fab = _0x3d9238[_0x3628("0x3db")][_0xa40ec2];
    i_BQAJMs[_0x544fab[_0x3628("0x203")]] = null;
  }
  i_sFmzCs(), _0x3d9238[_0x3628("0x23")]();
}
function i_pwBFUs(_0x18ee22, _0xdbb0ba, _0x29c7f0, _0x409a7b, _0x5cc1ce, _0x2c5ebe) {
  return _0x29c7f0 <= _0x18ee22 && _0x18ee22 <= _0x29c7f0 + _0x5cc1ce && _0x409a7b <= _0xdbb0ba && _0xdbb0ba <= _0x409a7b + _0x2c5ebe;
}
function i_ZdjBDs(_0x51974d, _0x218cff, _0x2e0556, _0x18a597, _0x2bd01c, _0x1b59ab, _0x1e561b, _0x33988e) {
  return !(_0x1b59ab + _0x33988e < _0x218cff || _0x218cff + _0x18a597 < _0x1b59ab || _0x2bd01c + _0x1e561b < _0x51974d || _0x51974d + _0x2e0556 < _0x2bd01c);
}
var i_eRzm$s = -0x1;
function i_GdHiBs(_0x2b990a) {
  document["getElementsByName"]("keybind")["forEach"](function (_0xec6540) {
    i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(_0xec6540)] == _0x2b990a && (i_WmyDOu["PCSet"][i_Brhwo_(_0xec6540)] = "NO", i_JhjKFs(_0xec6540));
  });
}
function i_dBDxGs(_0xd65924) {
  document["getElementsByName"](_0x3628("0x9f"))[_0x3628("0x64")](function (_0x4d9616) {
    _0x3628("0xf5") != i_Brhwo_(_0x4d9616) && _0x3628("0x488") != i_Brhwo_(_0x4d9616) && _0x3628("0x491") != i_Brhwo_(_0x4d9616) && _0x3628("0x74") != i_Brhwo_(_0x4d9616) && _0x3628("0x4b1") != i_Brhwo_(_0x4d9616) && i_WmyDOu["GPSetx"][i_Brhwo_(_0x4d9616)] == _0xd65924 && (i_WmyDOu["GPSetx"][i_Brhwo_(_0x4d9616)] = -0x1, i_JhjKFs(_0x4d9616));
  });
}
function i_JhjKFs(_0x1f9833) {
  if (-0x1 == i_eRzm$s) {
    "NO" == (_0x548aa1 = (_0x548aa1 = (_0x548aa1 = i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(_0x1f9833)][_0x3628("0x16d")]("Key", ""))[_0x3628("0x16d")]("Digit", ""))["replace"](_0x3628("0x2f8"), "")) && (_0x548aa1 = "空"), _0x1f9833[_0x3628("0x161")][0x1][_0x3628("0x27e")] = _0x548aa1;
  } else {
    if (_0x3628("0x95") !== _0x3628("0x95")) {
      i_ankaua["Ce"]("game", _0x3628("0x51a"));
    } else {
      var _0x548aa1 = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x1f9833)];
      _0x1f9833[_0x3628("0x161")][0x1][_0x3628("0x27e")] = _0x548aa1;
    }
  }
}
var i_CpRJLs = null,
  i_pPNtqs = null;
function i_SDQHVs(_0x800459) {
  var _0x258c9b = {
      buttons: []
    },
    _0x4d13be = i_HHPb_i();
  for (var _0x247fd3 in _0x4d13be["buttons"]) _0x258c9b[_0x3628("0x479")][_0x247fd3] = _0x4d13be[_0x3628("0x479")][_0x247fd3][_0x3628("0xc0")];
  _0x258c9b[_0x3628("0x0")] = _0x4d13be[_0x3628("0x0")], i_CpRJLs = setTimeout(function _0x55132b() {
    if (_0x3628("0x517") === "MYEcA") {
      if ("none" != i_sxfci_(_0x3628("0x481"))["style"]["display"]) {
        if (i_pPNtqs == _0x800459) {
          for (var _0x2b9b69 = i_HHPb_i(), _0x3e831e = 0x0; _0x3e831e < _0x2b9b69["buttons"][_0x3628("0x288")]; _0x3e831e++) if (_0x2b9b69[_0x3628("0x479")][_0x3e831e][_0x3628("0xc0")] && 0x0 == _0x258c9b[_0x3628("0x479")][_0x3e831e]) return i_dBDxGs(_0x3e831e), i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(_0x800459)] = _0x3e831e, _0x3628("0x74") == i_Brhwo_(_0x800459) && (i_WmyDOu[_0x3628("0xe1")][_0x3628("0xf5")] = 0x0), _0x800459[_0x3628("0x1a9")](), i_JhjKFs(_0x800459), i_SFAPYs(_0x800459[_0x3628("0x37d")]), void (i_CpRJLs = null);
          for (_0x3e831e = 0x0; _0x3e831e < _0x2b9b69[_0x3628("0x0")][_0x3628("0x288")]; _0x3e831e++) if (0.5 < Math[_0x3628("0x2f6")](_0x2b9b69[_0x3628("0x0")][_0x3e831e]) && _0x2b9b69[_0x3628("0x0")][_0x3e831e] != _0x258c9b[_0x3628("0x0")][_0x3e831e]) return 0x9 == _0x3e831e ? (i_rrYppt(_0x3628("0x30f"), !0x0), i_rrYppt(_0x3628("0x4ff"), !0x0), _0x800459[_0x3628("0x1a9")](), i_WmyDOu[_0x3628("0xe1")]["KeyJoy"] = 0x1, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x488")] = 0x9, i_WmyDOu[_0x3628("0xe1")]["KeyDown"] = 0x9, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x74")] = 0x9, i_WmyDOu[_0x3628("0xe1")][_0x3628("0x4b1")] = 0x9, i_dhBiu_("keybind", function (_0x948308) {
            _0x3628("0x488") != i_Brhwo_(_0x948308) && _0x3628("0x491") != i_Brhwo_(_0x948308) && _0x3628("0x74") != i_Brhwo_(_0x948308) && _0x3628("0x4b1") != i_Brhwo_(_0x948308) || i_JhjKFs(_0x948308), "KeyCoin" == i_Brhwo_(_0x948308) && setTimeout(function () {
              _0x948308[_0x3628("0x30e")]();
            }, 0xc8);
          })) : (i_WmyDOu["GPSetx"]["KeyJoy"] = 0x1, i_WmyDOu["GPSetx"][i_Brhwo_(_0x800459)] = _0x3e831e, _0x800459[_0x3628("0x1a9")](), i_JhjKFs(_0x800459), setTimeout(function () {
            i_SFAPYs(_0x800459["tabIndex"]);
          }, 0xc8)), void (i_CpRJLs = null);
          i_CpRJLs = setTimeout(_0x55132b, 0x14);
        } else _0x800459[_0x3628("0x1a9")]();
      } else _0x800459[_0x3628("0x1a9")]();
    } else {
      i_KDJte[_0x3628("0xea")][_0x3628("0x47a")]("File_ind_act");
    }
  }, 0x14);
}
function i_NQjGjs() {
  i_dhBiu_("keybind", function (_0x4ccf91) {
    i_JhjKFs(_0x4ccf91);
  }), i_sxfci_(_0x3628("0x3a8"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")], i_sxfci_("cb_lrrever")["checked"] = i_WmyDOu["GPSetx"][_0x3628("0x33f")];
}
function i_SFAPYs(_0x75780b) {
  i_dhBiu_(_0x3628("0x9f"), function (_0x2b158f) {
    if (_0x3628("0x272") === _0x3628("0x500")) {
      i_isthe ? (i_WmyDOu[_0x3628("0x1b5")] = 0x1, $(_0x3628("0x1ec"))[_0x3628("0x12")]("image-rendering", _0x3628("0x23f"))) : (i_WmyDOu[_0x3628("0x1b5")] = 0x0, $(_0x3628("0x1ec"))[_0x3628("0x12")](_0x3628("0x32f"), _0x3628("0x1b")));
    } else {
      _0x2b158f[_0x3628("0x37d")] == _0x75780b + 0x1 && (_0x2b158f[_0x3628("0x30e")](), !0x0);
    }
  });
}
function i_bTXGHs() {
  i_Hfbes_(_0x3628("0x504"), function () {
    -0x1 == i_eRzm$s ? i_WmyDOu[_0x3628("0x46f")] = JSON[_0x3628("0x1aa")](JSON[_0x3628("0x2ae")](i_jwnmXu)) : i_WmyDOu["GPSetx"] = JSON[_0x3628("0x1aa")](JSON[_0x3628("0x2ae")](i_mACQRu)), i_NQjGjs();
  }), i_ZKQCf_("keybind", "focus", function (_0x54e223) {
    this[_0x3628("0xea")]["add"]("_state_conduct"), 0x0 <= i_eRzm$s && i_SDQHVs(i_pPNtqs = this);
  }), i_ZKQCf_(_0x3628("0x9f"), "blur", function (_0x167c84) {
    this[_0x3628("0xea")][_0x3628("0x47a")]("_state_conduct");
  }), i_ZKQCf_(_0x3628("0x9f"), _0x3628("0x36e"), function (_0x484e1b) {
    if (_0x3628("0x257") !== _0x3628("0x3b7")) {
      _0x3628("0x2bf") != _0x484e1b[_0x3628("0x376")] ? (i_GdHiBs(_0x484e1b[_0x3628("0x26f")]), _0x3628("0x474") == _0x484e1b[_0x3628("0x26f")] ? i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(this)] = "NO" : i_WmyDOu[_0x3628("0x46f")][i_Brhwo_(this)] = _0x484e1b["code"], i_JhjKFs(this), this[_0x3628("0x1a9")](), _0x484e1b[_0x3628("0x23")](), i_SFAPYs(this[_0x3628("0x37d")])) : this[_0x3628("0x1a9")]();
    } else {
      i_zXJHnu(i_cZwcUl["In"], 0x1, 0x0);
    }
  }), i_tXrZr_(_0x3628("0x3a8"), "change", function () {
    if (_0x3628("0x344") === _0x3628("0x252")) {
      i_xNJjDu(), i_iDQBme ? (i_QcDssf(), i_siAWrf()) : (i_HmQPYu(), i_MFnNHu(), i_TXHhWu()), i_XFSipu(i_WmyDOu["gSuperSpeed"]);
    } else {
      i_WmyDOu[_0x3628("0xe1")]["gKeyRev"] = this["checked"];
    }
  }), i_tXrZr_("cb_lrrever", _0x3628("0x2fd"), function () {
    i_WmyDOu[_0x3628("0xe1")][_0x3628("0x33f")] = this[_0x3628("0x206")];
  }), i_NQjGjs(), i_bxRKJs();
}
function i_GSEmWs() {
  var _0x3f0b8a = i_sxfci_(_0x3628("0x5d")),
    _0x591f83 = i_sxfci_(_0x3628("0x4d3"));
  document[_0x3628("0x416")](_0x3628("0x4ba"))["innerHTML"] = event["target"][_0x3628("0x24d")], _0x3f0b8a["style"]["display"] = "none", _0x591f83[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x4d3")), _0x591f83[_0x3628("0xea")][_0x3628("0x47a")]("optionDownRev"), i_eRzm$s = i_Brhwo_(event[_0x3628("0x2f9")]), i_NQjGjs(), i_HjtSei = parseInt(i_Brhwo_(event[_0x3628("0x2f9")])), -0x1 == i_eRzm$s ? i_sxfci_("cbg_gamepadrev")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e") : (i_sxfci_(_0x3628("0x4c"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x3a8"))[_0x3628("0x206")] = i_WmyDOu["GPSetx"][_0x3628("0x2cd")], i_sxfci_(_0x3628("0x20f"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0xe1")]["gKeyRevLR"], localStorage[_0x3628("0x381")](_0x3628("0x304"), v_allGamePadName[i_eRzm$s]["id"]));
}
function i_bxRKJs() {
  i_cirYl_(_0x3628("0x166"), i_GSEmWs);
}
function i_tCYnQs(_0x253469) {
  i_sxfci_("selectOptionBox")[_0x3628("0x24d")] = _0x3628("0x458");
  var _0x38ae75 = 0x0;
  i_HjtSei = -0x1, v_allGamePadName = {};
  for (var _0x205e13 = localStorage[_0x3628("0x392")]("lastgamepad"), _0x3a7163 = 0x0; _0x3a7163 < _0x253469[_0x3628("0x288")]; _0x3a7163++) if (null != _0x253469[_0x3a7163]) {
    !0x0;
    var _0x327a56 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
    _0x327a56[_0x3628("0x4a8")] = _0x3628("0x513"), _0x327a56[_0x3628("0x107")](_0x3628("0x4dc"), "inputdevicename"), _0x327a56[_0x3628("0x107")]("key", _0x3a7163), _0x327a56[_0x3628("0x24d")] = _0x253469[_0x3a7163]["id"][_0x3628("0x26a")](0x0, _0x253469[_0x3a7163]["id"][_0x3628("0x2f3")](_0x3628("0x75"))), "" == _0x327a56[_0x3628("0x24d")] && (_0x327a56[_0x3628("0x24d")] = _0x253469[_0x3a7163]["id"]["substr"](0x0, _0x253469[_0x3a7163]["id"][_0x3628("0x2f3")]("("))), "" == _0x327a56["innerHTML"] && (_0x327a56[_0x3628("0x24d")] = _0x253469[_0x3a7163]["id"]), i_sxfci_("selectOptionBox")["appendChild"](_0x327a56), _0x38ae75++, -0x1 != i_HjtSei && _0x205e13 != _0x253469[_0x3a7163]["id"] || (i_HjtSei = _0x3a7163), v_allGamePadName[_0x3a7163] = {}, v_allGamePadName[_0x3a7163]["id"] = _0x253469[_0x3a7163]["id"], v_allGamePadName[_0x3a7163][_0x3628("0x63")] = _0x3a7163;
  }
  return setTimeout(i_bxRKJs, 0x64), _0x38ae75;
}
function i_XPSbZs(_0x61808d) {
  var _0x2f6c3a = i_sxfci_("roomOwnerFlag"),
    _0x1db87c = i_sxfci_("Broomownerflag");
  0x0 == _0x61808d ? (_0x2f6c3a[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x1cd"), _0x2f6c3a["style"][_0x3628("0x270")] = "22px", _0x1db87c["style"][_0x3628("0x270")] = _0x3628("0x3e3")) : 0x1 == _0x61808d ? (_0x2f6c3a[_0x3628("0x3e")]["left"] = _0x3628("0x1cd"), _0x2f6c3a[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x1f6"), _0x1db87c[_0x3628("0x3e")][_0x3628("0x270")] = "118px") : 0x2 == _0x61808d ? (_0x2f6c3a[_0x3628("0x3e")][_0x3628("0x282")] = _0x3628("0x1cd"), _0x2f6c3a[_0x3628("0x3e")]["top"] = _0x3628("0x54"), _0x1db87c[_0x3628("0x3e")][_0x3628("0x270")] = "188px") : 0x3 == _0x61808d && (_0x2f6c3a[_0x3628("0x3e")][_0x3628("0x282")] = "128px", _0x2f6c3a["style"][_0x3628("0x270")] = _0x3628("0xe5"), _0x1db87c[_0x3628("0x3e")][_0x3628("0x270")] = _0x3628("0x4a6"));
}
function i_nFFBer(_0x45f6c3) {
  if (null != _0x45f6c3) {
    i_cbdGFn = _0x45f6c3, v_gui_pclink_updatetime = performance[_0x3628("0x30b")](), i_sxfci_(_0x3628("0x2f5"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x4af")], i_sxfci_(_0x3628("0x262"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x34e")], i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x4e7")], i_sxfci_("p4img")["src"] = _0x45f6c3[_0x3628("0x3a5")], i_sxfci_(_0x3628("0x2cb"))[_0x3628("0x27e")] = _0x45f6c3["Nick1"], i_sxfci_(_0x3628("0x274"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x331")], i_sxfci_(_0x3628("0x521"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x2de")], i_sxfci_(_0x3628("0x54e"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x4cd")], i_sxfci_(_0x3628("0x3f7"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x3a4")], i_sxfci_(_0x3628("0x322"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x331")], i_sxfci_("Bp3nick")[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x2de")], i_sxfci_(_0x3628("0x326"))[_0x3628("0x27e")] = _0x45f6c3[_0x3628("0x4cd")], i_sxfci_("Bp1img")[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x4af")], i_sxfci_(_0x3628("0x14"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x34e")], i_sxfci_(_0x3628("0x3c6"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x4e7")], i_sxfci_(_0x3628("0xbc"))[_0x3628("0x1c5")] = _0x45f6c3[_0x3628("0x3a5")], 0x0 < _0x45f6c3[_0x3628("0x3a4")][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x2f5"))[_0x3628("0x1c5")] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0x178"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < _0x45f6c3["Nick2"][_0x3628("0x2f3")]("关闭") && (i_sxfci_("p2img")[_0x3628("0x1c5")] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0x14"))[_0x3628("0x1c5")] = "./img/close.png"), 0x0 < _0x45f6c3[_0x3628("0x2de")][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x293"))[_0x3628("0x1c5")] = "./img/close.png", i_sxfci_(_0x3628("0x3c6"))[_0x3628("0x1c5")] = _0x3628("0x3ac")), 0x0 < _0x45f6c3["Nick4"][_0x3628("0x2f3")]("关闭") && (i_sxfci_(_0x3628("0x45a"))[_0x3628("0x1c5")] = _0x3628("0x3ac"), i_sxfci_(_0x3628("0xbc"))[_0x3628("0x1c5")] = "./img/close.png"), _0x45f6c3[_0x3628("0x2a1")] ? (i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0xc2"))) : (i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2a4"))[_0x3628("0xea")]["remove"](_0x3628("0xc2"))), _0x45f6c3[_0x3628("0x20e")] ? (i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")]["add"]("igwi-shouji")) : (i_sxfci_(_0x3628("0x149"))["classList"][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x149"))[_0x3628("0xea")]["remove"](_0x3628("0xc2"))), _0x45f6c3["Mob3"] ? (i_sxfci_(_0x3628("0x412"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x412"))[_0x3628("0xea")]["add"](_0x3628("0xc2"))) : (i_sxfci_(_0x3628("0x412"))[_0x3628("0xea")]["add"](_0x3628("0x1b9")), i_sxfci_("p3dev")[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xc2"))), _0x45f6c3[_0x3628("0xb0")] ? (i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")][_0x3628("0x405")]("igwi-shouji")) : (i_sxfci_(_0x3628("0x2d3"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x1b9")), i_sxfci_(_0x3628("0x2d3"))["classList"]["remove"](_0x3628("0xc2"))), i_XPSbZs(_0x45f6c3[_0x3628("0x76")]), i_sxfci_("lookcount")["innerText"] = _0x45f6c3[_0x3628("0x41b")], i_sxfci_("watchplayer")["innerText"] = "(" + _0x45f6c3[_0x3628("0x41b")] + ")";
    var _0x182422 = i_sxfci_(_0x3628("0x3b"));
    for (var _0x48604c in _0x182422["innerHTML"] = "", _0x45f6c3[_0x3628("0x24a")]) {
      if (_0x3628("0x271") === "FuBLr") {
        var _0x1b94ba = i_WmyDOu[_0x3628("0xe1")][i_Brhwo_(i_Wyjte)];
        i_Wyjte[_0x3628("0x32d")] = _0x1b94ba;
      } else {
        var _0x1caff9 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
        _0x1caff9["setAttribute"]("key", _0x45f6c3["LookerList"][_0x48604c]), _0x1caff9["innerHTML"] = _0x3628("0x101") + _0x45f6c3[_0x3628("0x24a")][_0x48604c] + "</div><div></div>", _0x1caff9["className"] = _0x3628("0x2df"), _0x182422["appendChild"](_0x1caff9);
      }
    }
    v_gui_ismatch_allow = !0x1;
  }
}
function i_Qrtrnr() {
  0x0 < i_HDQdvr[_0x3628("0x2f3")](_0x3628("0x1da")) ? i_sxfci_(_0x3628("0x28f"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c") : i_wewzwr(_0x3628("0xd0"));
}
function i_rpEWtr(_0x353f0f) {
  var _0x598e13 = i_sxfci_(_0x3628("0x21b")),
    _0x3aa4f3 = i_sxfci_(_0x3628("0x507"));
  switch (_0x353f0f) {
    case _0x3628("0x5"):
      _0x3aa4f3[_0x3628("0x3e")][_0x3628("0x4d6")] = _0x3628("0x5f"), _0x3aa4f3["classList"]["replace"]("igwi-zanting", _0x3628("0x2db")), _0x598e13[_0x3628("0x27e")] = "继续";
      break;
    case _0x3628("0x537"):
      _0x3aa4f3["style"][_0x3628("0x4d6")] = "", _0x3aa4f3[_0x3628("0xea")][_0x3628("0x16d")](_0x3628("0x2db"), _0x3628("0x454")), _0x598e13[_0x3628("0x27e")] = "暂停";
  }
}
function i_dGdJar() {}
function i_pPaJir(_0x5c4e4e) {
  i_sxfci_("loadpostxt")["innerText"] = "(" + _0x5c4e4e[_0x3628("0x249")](0x0) + "%)", i_sxfci_(_0x3628("0x310"))[_0x3628("0x3e")][_0x3628("0x191")] = _0x5c4e4e["toFixed"](0x0) + "%", 0x64 <= _0x5c4e4e && (i_sxfci_(_0x3628("0x1d7"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_sxfci_("gamescr")[_0x3628("0x3e")][_0x3628("0x3b5")] = "block", i_nHTFAe());
}
var i_XSWHor = "<div\x20class=\x22talkRandom\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkedName\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20{{nick}}：\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<span>{{text}}</span>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20</div>",
  i_KEnCcr = _0x3628("0x4de"),
  i_jFJtsr = _0x3628("0x4dd"),
  i_PjRTrr = _0x3628("0x32b"),
  i_bFaalr = _0x3628("0x3ae"),
  i_KZnrur = "<div\x20class=\x22talking\x20\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTop\x20myTalk\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingTime\x22>{{time}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingName\x22>{{nick}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22yellowStick\x22></div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22myWordsBox\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22talkingWords\x20myWords\x22>{{text}}</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>",
  i_hefSfr = null,
  i_YJQx_r = null,
  i_HDQdvr = "";
function i_jSpbdr() {
  null == i_hefSfr && (i_hefSfr = i_sxfci_("chatInGame"), i_tXrZr_("messageinput", _0x3628("0x36e"), function (_0x589745) {
    _0x3628("0x396") == _0x589745[_0x3628("0x376")] && i_AfxrMr();
  }), i_tXrZr_(_0x3628("0x16c"), _0x3628("0x30e"), function (_0x182b4c) {}), i_tXrZr_(_0x3628("0x16c"), _0x3628("0x1a9"), function (_0x3b0a0e) {})), null == i_YJQx_r && (i_YJQx_r = i_sxfci_("roommsglist")), i_rrYppt = i_xmzByr, i_iZhRvt = i_GJQKdt = i_Wkbybr;
}
var i_weGYpr = 0x0;
function i_zpMRmr() {
  i_weGYpr = performance["now"]() + 0x1324, i_hefSfr[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), setTimeout(function () {
    if (_0x3628("0x7a") === "QYeku") {
      performance[_0x3628("0x30b")]() > i_weGYpr && (i_hefSfr[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", i_hefSfr[_0x3628("0x24d")] = "");
    } else {
      i_ankaua["ue"](_0x3628("0x382"), _0x3628("0x9b"), "");
    }
  }, 0x1388);
}
function i_cNaigr(_0x2d3621) {
  _0x2d3621[_0x3628("0x373")] + _0x2d3621["clientHeight"] + 0x64 > _0x2d3621["scrollHeight"] && _0x2d3621[_0x3628("0x4db")](0x0, _0x2d3621[_0x3628("0xb9")]);
}
function i_JdsYhr(_0x568ddc) {
  i_hefSfr["appendChild"](_0x568ddc), 0x3 < i_hefSfr["children"][_0x3628("0x288")] && i_hefSfr[_0x3628("0x147")](i_hefSfr[_0x3628("0x161")][0x0]), i_zpMRmr();
}
function i_Wkbybr(_0x3b3f88) {
  null == i_hefSfr && i_jSpbdr();
  var _0x222f04 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x222f04[_0x3628("0x24d")] = i_XSWHor[_0x3628("0x16d")](_0x3628("0x10"), _0x3b3f88)[_0x3628("0x16d")]("{{nick}}", _0x3628("0x528")), i_HDQdvr = _0x3b3f88, i_JdsYhr(_0x222f04);
}
function i_xmzByr(_0x33b9c5) {
  null == i_hefSfr && i_jSpbdr();
  var _0x356199 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x356199[_0x3628("0x24d")] = i_KEnCcr[_0x3628("0x16d")](_0x3628("0x10"), _0x33b9c5)[_0x3628("0x16d")](_0x3628("0xeb"), _0x3628("0x528")), i_JdsYhr(_0x356199);
}
function i_wewzwr(_0x570830) {
  null == i_hefSfr && i_jSpbdr();
  var _0x5582e7 = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0x5582e7[_0x3628("0x24d")] = i_jFJtsr["replace"](_0x3628("0x10"), _0x570830)[_0x3628("0x16d")]("{{nick}}", _0x3628("0x528")), i_JdsYhr(_0x5582e7);
}
function i_jPrYkr(_0x4a7dde, _0x45cd18) {
  var _0xa633dd = document[_0x3628("0x3fe")](_0x3628("0x30a"));
  _0xa633dd[_0x3628("0x24d")] = i_KZnrur["replace"](_0x3628("0x10"), _0x45cd18)[_0x3628("0x16d")]("{{nick}}", _0x4a7dde)[_0x3628("0x16d")](_0x3628("0x19"), new Date()["toLocaleTimeString"]()), i_YJQx_r[_0x3628("0xd6")](_0xa633dd), i_cNaigr(i_YJQx_r);
}
function i_EyyJTr(_0x69e8, _0x573771) {
  var _0x373c79 = document["createElement"](_0x3628("0x30a"));
  _0x373c79[_0x3628("0x24d")] = i_bFaalr["replace"]("{{text}}", _0x573771)["replace"](_0x3628("0xeb"), _0x69e8)[_0x3628("0x16d")](_0x3628("0x19"), new Date()[_0x3628("0x1fb")]()), i_YJQx_r[_0x3628("0xd6")](_0x373c79), 0x63 < i_YJQx_r[_0x3628("0x1a0")][_0x3628("0x288")] && i_YJQx_r["removeChild"](i_YJQx_r[_0x3628("0x161")][0x0]), i_cNaigr(i_YJQx_r);
}
function i_fGppxr(_0xcaa3b8, _0x28ac98, _0x52c1fc, _0x4e964d) {
  null == i_hefSfr && i_jSpbdr();
  var _0x35edec = document["createElement"](_0x3628("0x30a"));
  if (_0x35edec[_0x3628("0x24d")] = i_XSWHor[_0x3628("0x16d")](_0x3628("0x10"), _0x28ac98)["replace"](_0x3628("0xeb"), _0xcaa3b8), i_HDQdvr = _0x28ac98, i_JdsYhr(_0x35edec), 0x2 != _0x4e964d) {
    if (_0xcaa3b8 != i_DdTaQf["NickName"]) {
      var _0x34756c = document["createElement"](_0x3628("0x30a"));
      _0x34756c[_0x3628("0x24d")] = i_PjRTrr[_0x3628("0x16d")](_0x3628("0x10"), _0x28ac98)[_0x3628("0x16d")](_0x3628("0xeb"), _0xcaa3b8)["replace"](_0x3628("0x19"), new Date()[_0x3628("0x1fb")]()), i_YJQx_r[_0x3628("0xd6")](_0x34756c), 0x63 < i_YJQx_r[_0x3628("0x1a0")][_0x3628("0x288")] && i_YJQx_r[_0x3628("0x147")](i_YJQx_r[_0x3628("0x161")][0x0]), i_cNaigr(i_YJQx_r);
    } else i_jPrYkr(_0xcaa3b8, _0x28ac98);
  } else i_EyyJTr(_0xcaa3b8, _0x28ac98);
}
function i_AfxrMr() {
  var _0x24e22e = i_hGaila(i_sxfci_(_0x3628("0x16c"))[_0x3628("0x32d")]);
  if ("" != _0x24e22e) i_sxfci_("messageinput")[_0x3628("0x32d")] = "", i_jPrYkr(_0x24e22e);else if (0x0 == i_iDpHde) i_sxfci_(_0x3628("0x16c"))[_0x3628("0x32d")] = "", i_jPrYkr("我", _0x3628("0xbe"));else {
    var _0x440eee = i_sxfci_(_0x3628("0x16c"))[_0x3628("0x32d")];
    "" != _0x440eee && i_hnXPpo(_0x3628("0x2a5"), _0x440eee), i_sxfci_("messageinput")[_0x3628("0x32d")] = "";
  }
  i_sxfci_(_0x3628("0x16c"))["blur"]();
}
var i_hEJCPr = null;
function i_fhTBEr() {
  i_rHShLf(0x50);
}
function i_yAWnAr() {
  i_hEJCPr || (i_hEJCPr = !0x0, i_mRsncl(), i_Hfbes_(_0x3628("0x429"), function () {
    var _0x32b8a5 = document["getElementById"]("voiceBox");
    i_kkffJf(), _0x32b8a5[_0x3628("0x3e")][_0x3628("0x4d6")] = i_PAkc_f ? (_0x32b8a5[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0xdf")), _0x32b8a5[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x49b")), "") : (_0x32b8a5[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x49b")), _0x32b8a5[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0xdf")), _0x3628("0x5f"));
  }), i_Hfbes_(_0x3628("0x43c"), function () {
    i_Frwnk();
  }), i_Hfbes_(_0x3628("0x212"), function () {
    if (_0x3628("0x29b") !== _0x3628("0x2c6")) {
      i_DAFET();
    } else {
      var _0x281bdd = document[_0x3628("0x3fe")]("div");
      _0x281bdd[_0x3628("0x24d")] = i_QEbyet["replace"](_0x3628("0x10"), i_NAzde)[_0x3628("0x16d")](_0x3628("0xeb"), i_DdTaQf["NickName"])[_0x3628("0x16d")](_0x3628("0x19"), new Date()["toLocaleTimeString"]()), i_KXdcit[_0x3628("0xd6")](_0x281bdd), i_KXdcit["scrollTo"](0x0, i_KXdcit[_0x3628("0xb9")]);
    }
  }), i_Hfbes_("btn_help", function () {}), i_Hfbes_(_0x3628("0x40c"), function () {
    i_RchPx();
  }), i_Hfbes_(_0x3628("0x502"), function () {
    0x1 == i_iDpHde ? i_xmzByr(_0x3628("0x3c7")) : 0x0 < i_DdTaQf[_0x3628("0x31")][_0x3628("0x33e")] || 0x0 < i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")] ? i_WwiRv_(_0x3628("0x2c1"), _0x3628("0x8c")) : (i_xmzByr(_0x3628("0x40")), i_ankaua["ze"]());
  }), i_Hfbes_(_0x3628("0x159"), function () {
    if (_0x3628("0x343") === _0x3628("0x41a")) {
      i_iZhRvt(_0x3628("0x478"));
    } else {
      i_sxfci_(_0x3628("0x2c1"))[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
    }
  }), i_Hfbes_("btn_fullscreen", function (_0xb8cd55) {
    i_rXQKhe ? i_Wkbybr(_0x3628("0xfc")) : null != document["body"][_0x3628("0x3a9")] && (null == document[_0x3628("0x42e")] ? (document[_0x3628("0x148")][_0x3628("0x3a9")](), this[_0x3628("0x3e")][_0x3628("0x4d6")] = _0x3628("0x353"), i_ankaua["Pe"](0x3)) : (document[_0x3628("0x2c")](), i_ankaua["Pe"](0x4), this["style"][_0x3628("0x4d6")] = ""));
  }), i_Hfbes_(_0x3628("0x1d2"), function () {
    i_WwiRv_("popwin_save", _0x3628("0x8c"));
  }), i_Hfbes_("btn_closeroomcard", function () {
    i_WwiRv_(_0x3628("0x52e"), _0x3628("0x8c"));
  }), i_Hfbes_(_0x3628("0x6a"), function () {
    if (_0x3628("0x1ca") !== _0x3628("0x1ca")) {
      i_CcCZrl + 0x493e0 > performance[_0x3628("0x30b")]() ? i_iZhRvt(_0x3628("0x37e")) : (i_CcCZrl = performance[_0x3628("0x30b")](), "" != i_sxfci_(_0x3628("0x483"))[_0x3628("0x32d")] ? ($[_0x3628("0x276")](_0x3628("0x2be"), {
        type: "POST",
        async: !0x0,
        data: {
          gtype: "fc",
          gamename: i_BThm_e,
          gid: gid,
          lid: i_ZmCwpe,
          call: _0x3628("0x3ad"),
          max: i_ZHidMe,
          now: i_xjHeZi,
          pass: "",
          sev: i_hkNr_o,
          msg: i_sxfci_(_0x3628("0x483"))[_0x3628("0x32d")],
          se: i_YHmAt_(_0x3628("0x81"))
        },
        crossDomain: !0x0,
        xhrFields: {
          withCredentials: !0x1
        },
        success: function (_0x67b634, _0x45f71a, _0x1514c3) {
          "ok" == _0x67b634[_0x3628("0x446")] ? i_rrYppt(_0x3628("0x54c")) : i_iZhRvt("邀请发送失败");
        }
      }), i_sxfci_("popwin_sharegame")[_0x3628("0x3e")][_0x3628("0x3b5")] = "none") : i_iZhRvt("请先输入邀请信息"));
    } else {
      i_WwiRv_("layoutconfig", "flex") && (i_MSkKnl && i_kymCil(), i_wCwYtl && i_HWZeal(), i_PYfcds());
    }
  }), i_Hfbes_(_0x3628("0x452"), function () {
    if (_0x3628("0x495") === "hGJtv") {
      i_sxfci_(_0x3628("0x3ef"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
    } else {
      i_ankaua["ue"]("control", "close", "");
    }
  }), i_WKxiJr(), i_mASxDr());
}
function i_GcpMSr(_0x476c10) {
  i_sxfci_(_0x3628("0x259"))["innerText"] = null != i_hFSCU ? (alert("使用了SAB"), "" + _0x476c10) : _0x476c10;
}
function i_YjDQCr() {
  i_Hfbes_(_0x3628("0x255"), function () {
    i_WwiRv_("layoutconfig", _0x3628("0x3dd")), i_nszags();
  }), i_Hfbes_(_0x3628("0x2e1"), function () {
    i_WmyDOu[_0x3628("0x438")] = JSON["parse"](JSON["stringify"](i_NSYbNu)), i_MzyCVu(), i_xmzByr(_0x3628("0x42b"));
  }), i_Hfbes_("btn_allhide", function () {
    i_WmyDOu[_0x3628("0x485")][_0x3628("0x20c")] ? (this[_0x3628("0x3e")][_0x3628("0x45f")] = "", i_WmyDOu["MobBase"][_0x3628("0x20c")] = !0x1, this[_0x3628("0x161")][0x1]["innerText"] = _0x3628("0x4ef")) : (this["style"][_0x3628("0x45f")] = _0x3628("0x34d"), i_WmyDOu[_0x3628("0x485")][_0x3628("0x20c")] = !0x0, this[_0x3628("0x161")][0x1][_0x3628("0x27e")] = _0x3628("0x28e")), i_MzyCVu();
  }), i_Hfbes_(_0x3628("0x6d"), function () {
    i_WwiRv_(_0x3628("0x55f"), "flex"), i_nszags(), i_NfcJju();
  });
  var _0x2efc29 = performance[_0x3628("0x30b")](),
    _0x321f40 = performance[_0x3628("0x30b")](),
    _0x42d1a2 = i_sxfci_("layout_size"),
    _0x3e1150 = i_sxfci_(_0x3628("0x1bd"));
  i_tXrZr_("layout_size_area", _0x3628("0x87"), function (_0x5d5ab1) {
    if ("EFPOH" === _0x3628("0x1d9")) {
      var _0x1a6f4f = i_QtJCKs(_0x5d5ab1["changedTouches"][0x0][_0x3628("0x325")], _0x5d5ab1["changedTouches"][0x0][_0x3628("0x16e")]),
        _0x1f12b9 = _0x42d1a2["getClientRects"](),
        _0x5d3cd8 = i_QtJCKs(_0x1f12b9[0x0]["x"], _0x1f12b9[0x0]["y"]),
        _0x4430e2 = _0x1a6f4f["pn"];
      _0x42d1a2[_0x3628("0x32d")] = (_0x4430e2 - _0x5d3cd8["pn"]) / 1.3, _0x2efc29 + 0x64 < performance[_0x3628("0x30b")]() && (_0x2efc29 = performance[_0x3628("0x30b")](), _0x42d1a2[_0x3628("0x3e")]["backgroundSize"] = _0x42d1a2["value"] + "%\x20100%", i_sxfci_("layoutsizetxt")[_0x3628("0x27e")] = (0x32 + 0x96 * _0x42d1a2[_0x3628("0x32d")] / 0x64)[_0x3628("0x249")](0x0) + "%", i_CByehs((0x32 + 0x96 * _0x42d1a2["value"] / 0x64) / 0x64)), _0x5d5ab1[_0x3628("0x23")]();
    } else {
      i_ankaua["ue"](_0x3628("0x382"), "ingame", JSON[_0x3628("0x2ae")]({
        gametype: "fc",
        roomid: i_ZmCwpe,
        gamename: i_BThm_e,
        gameid: gid,
        server: i_wesTbe
      }));
    }
  }), i_tXrZr_(_0x3628("0x330"), _0x3628("0x2fd"), function (_0x3af3c3) {
    this[_0x3628("0x3e")]["backgroundSize"] = this[_0x3628("0x32d")] + _0x3628("0x62"), i_sxfci_("layoutsizetxt")["innerText"] = (0x32 + 0x96 * this[_0x3628("0x32d")] / 0x64)[_0x3628("0x249")](0x0) + "%", i_CByehs((0x32 + 0x96 * this[_0x3628("0x32d")] / 0x64) / 0x64);
  }), i_tXrZr_("layout_transparent_area", _0x3628("0x87"), function (_0x4bf95d) {
    var _0x772b9d = i_QtJCKs(_0x4bf95d[_0x3628("0x3db")][0x0][_0x3628("0x325")], _0x4bf95d["changedTouches"][0x0]["clientY"]),
      _0x144a25 = _0x3e1150[_0x3628("0x39f")](),
      _0x28babb = i_QtJCKs(_0x144a25[0x0]["x"], _0x144a25[0x0]["y"]),
      _0x2abebd = _0x772b9d["pn"];
    _0x3e1150["value"] = (_0x2abebd - _0x28babb["pn"]) / 1.3, _0x321f40 + 0x64 < performance[_0x3628("0x30b")]() && (_0x321f40 = performance[_0x3628("0x30b")](), _0x3e1150[_0x3628("0x3e")][_0x3628("0x1c8")] = _0x3e1150["value"] + _0x3628("0x62"), i_sxfci_(_0x3628("0x52d"))["innerText"] = _0x3e1150[_0x3628("0x32d")] + "%", i_Wzzebs(_0x3e1150[_0x3628("0x32d")] / 0x64)), _0x4bf95d["preventDefault"]();
  }), i_tXrZr_(_0x3628("0x1bd"), _0x3628("0x2fd"), function (_0x317aa1) {
    this[_0x3628("0x3e")][_0x3628("0x1c8")] = this[_0x3628("0x32d")] + _0x3628("0x62"), i_sxfci_(_0x3628("0x52d"))[_0x3628("0x27e")] = this[_0x3628("0x32d")] + "%", i_Wzzebs(this[_0x3628("0x32d")] / 0x64);
  });
}
function i_AJbmKr() {
  i_sxfci_(_0x3628("0x2cb"))["innerText"] = i_DdTaQf["NickName"], i_sxfci_(_0x3628("0x2f5"))[_0x3628("0x1c5")] = i_DdTaQf[_0x3628("0x4f9")], i_sxfci_(_0x3628("0x3f7"))["innerText"] = i_DdTaQf[_0x3628("0x289")], i_sxfci_(_0x3628("0x178"))[_0x3628("0x1c5")] = i_DdTaQf["HeadImg"];
}
function i_wfTQOr() {
  i_sxfci_(_0x3628("0x3ef"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
}
function i_EibxXr() {
  0x1 == i_iDpHde && (i_sxfci_(_0x3628("0x230"))[_0x3628("0x27e")] = _0x3628("0x53a") + i_ZmCwpe), i_JDzaxt = i_sxfci_(_0x3628("0x52b")), null != i_hFSCU && (i_sxfci_(_0x3628("0x259"))["innerText"] = "⚡\x20" + i_BThm_e), 0x0 == i_iDpHde && (i_sxfci_("sharetiptxt")[_0x3628("0x27e")] = _0x3628("0xec"), i_Hfbes_(_0x3628("0x1eb"), function () {
    if ("LgryQ" !== _0x3628("0x1c9")) {
      if (-0x1 == i_HjtSei) i_Wkbybr(_0x3628("0x509"));else {
        if (null == (i_TBnkka = navigator[_0x3628("0xa")]()[i_HjtSei])) return void i_Wkbybr(_0x3628("0x509"));
        var _0x3fe440 = i_TBnkka["id"][_0x3628("0x26a")](0x0, i_TBnkka["id"][_0x3628("0x2f3")](_0x3628("0x75")));
        switch ("" == _0x3fe440 && (_0x3fe440 = i_TBnkka["id"]["substr"](0x0, i_TBnkka["id"]["indexOf"]("("))), "" == _0x3fe440 && (_0x3fe440 = i_TBnkka["id"]), 0x3 == ++i_DFBCti && (i_DFBCti = 0x0), i_DFBCti) {
          case 0x0:
            i_xmzByr(_0x3628("0x2e9"));
            break;
          case 0x1:
            i_xmzByr(_0x3628("0xe2"));
            break;
          case 0x2:
            i_xmzByr("进入【1P\x20手柄摇杆】\x20，【2P\x20手柄摇杆】多人模式");
        }
      }
    } else {
      var _0x6decf1 = firstCall ? function () {
        if (fn) {
          var _0x1b17df = fn[_0x3628("0x45e")](context, arguments);
          fn = null;
          return _0x1b17df;
        }
      } : function () {};
      firstCall = ![];
      return _0x6decf1;
    }
  })), i_fhTBEr(), i_yAWnAr(), i_jSpbdr(), i_XENaLr(), i_dGdJar(), i_YjDQCr();
}
var i_AwMiRr = null,
  i_BDeZzr = 0x1;
function i_paAhIr(_0x32a6d3, _0x22e490) {
  var _0x42bc06 = _0x32a6d3;
  if (i_Tnctna = _0x22e490, 0x0 == i_iDpHde) i_sxfci_("p" + _0x42bc06 + _0x3628("0x265"))[_0x3628("0x1c5")] = "https://static.wo1wan.com/game/close.png", i_sxfci_("p" + _0x42bc06 + _0x3628("0x308"))[_0x3628("0x27e")] = "空", i_sxfci_("p" + i_BDeZzr + _0x3628("0x265"))["src"] = i_DdTaQf[_0x3628("0x4f9")], i_sxfci_("p" + i_BDeZzr + _0x3628("0x308"))["innerText"] = i_DdTaQf[_0x3628("0x289")], i_ktCsEu(i_BDeZzr), i_XPSbZs(i_Kbzklo = i_BDeZzr - 0x1);else {
    if (_0x3628("0xd2") !== "yIugX") {
      var _0x4baae1 = window["orientation"];
      void 0x0 === _0x4baae1 && null != window["screen"][_0x3628("0x2e2")] && (_0x4baae1 = window[_0x3628("0x3f5")]["orientation"][_0x3628("0x2cc")]), 0xb4 !== _0x4baae1 && 0x0 !== _0x4baae1 || (i_ZdSiks = !0x0), 0x5a !== _0x4baae1 && -0x5a !== _0x4baae1 || (i_ZdSiks = !0x1);
    } else {
      if (i_nibwYi) return void (_0x3628("0x356") == i_sxfci_("p" + i_BDeZzr + _0x3628("0x308"))[_0x3628("0x27e")] && i_hnXPpo(_0x3628("0x37c"), i_BDeZzr + ""));
      if ("等待连接" != i_sxfci_("p" + i_BDeZzr + _0x3628("0x308"))["innerText"]) return;
      i_fdZfJi = !0x0, i_wsSfso[0x0] = i_emcHro, i_wsSfso[0x1] = 0x0, i_bYNtOo(), setTimeout(function () {
        i_hnXPpo(_0x3628("0x20"), i_BDeZzr + "");
      }, 0xc8);
    }
  }
}
function i_rMrpNr(_0x47737c) {
  i_sxfci_(_0x3628("0x1f8"))[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
  var _0x927dce = i_cbdGFn[_0x3628("0x307")][i_BDeZzr - 0x1];
  switch (this["id"]) {
    case "plmenu_switch":
      i_paAhIr(0x0, i_BDeZzr);
      break;
    case _0x3628("0x498"):
      i_hnXPpo(_0x3628("0x3d8"), i_BDeZzr + "");
      break;
    case _0x3628("0x415"):
      i_hnXPpo(_0x3628("0x424"), i_BDeZzr + "");
      break;
    case _0x3628("0xf1"):
      if (0x0 <= i_BThm_e["indexOf"]("拳皇") && v_gui_pclink_updatetime + 0x7530 < performance[_0x3628("0x30b")]()) {
        if (_0x3628("0x54b") === _0x3628("0x3c4")) {
          performance[_0x3628("0x30b")]() > i_weGYpr && (i_hefSfr[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), i_hefSfr[_0x3628("0x24d")] = "");
        } else {
          i_rrYppt(_0x3628("0x3c8"));
          break;
        }
      }
      i_hnXPpo("kick", i_BDeZzr - 0x1 + "");
      break;
    case "plmenu_ban":
      i_pjQNyo() ? $["post"](_0x3628("0x369"), {
        who: _0x927dce,
        where: _0x3628("0x197") + gid + "_" + i_ZmCwpe,
        say: ""
      }, function (_0x23a3ff) {
        "ok" == _0x23a3ff[_0x3628("0x446")] ? i_hnXPpo(_0x3628("0x2a5"), i_cbdGFn[_0x3628("0x15c") + i_BDeZzr] + _0x3628("0xfe")) : i_iZhRvt(_0x3628("0x16b"));
      }) : i_iZhRvt(_0x3628("0xf9"));
      break;
    case _0x3628("0x17f"):
      i_TrjcCu(-0x1, _0x927dce, function (_0x3d96d1) {
        i_ankaua["ue"](_0x3628("0x313"), _0x3628("0x405"), _0x3d96d1[_0x3628("0x187")]) ? i_xmzByr(_0x3628("0x37f")) : i_Wkbybr("游戏大厅无法找到，请从大厅开始游戏");
      });
      break;
    case _0x3628("0x261"):
      i_xmzByr(_0x3628("0x45d"));
      break;
    case "plmenu_give":
      i_pjQNyo() && i_hnXPpo("transferroom", i_BDeZzr - 0x1 + "");
  }
}
function i_kdydUr(_0x3703f6, _0x5bdcda) {
  if (0x0 != i_iDpHde) {
    if (null == i_AwMiRr && (i_AwMiRr = i_sxfci_(_0x3628("0x1f8"))), (i_BDeZzr = _0x3703f6) - 0x1 != i_emcHro) if (i_nibwYi) _0x3628("0x356") == i_FZEFji[_0x3628("0x15c") + i_BDeZzr] && window[_0x3628("0x497")][_0x3628("0x16d")](window[_0x3628("0x497")][_0x3628("0x3ee")] + window["location"]["pathname"] + "?&id=" + gid + _0x3628("0x360") + i_wesTbe + _0x3628("0x18f") + i_ZmCwpe);else {
      var _0x5a5205 = i_sxfci_(_0x3628("0x4bc")),
        _0x276de8 = i_QtJCKs(_0x5bdcda["clientX"], _0x5bdcda[_0x3628("0x16e")]);
      _0x276de8["mn"] + 0x78 > i_RFyyys[_0x3628("0x9")] && (_0x276de8["mn"] = i_RFyyys[_0x3628("0x9")] - 0x78), _0x5a5205[_0x3628("0x3e")]["left"] = _0x276de8["pn"] + "px", _0x5a5205[_0x3628("0x3e")]["top"] = _0x276de8["mn"] + "px", i_AwMiRr[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), $(_0x3628("0x19d"))[_0x3628("0x49f")](), _0x3628("0x364") == i_FZEFji[_0x3628("0x15c") + i_BDeZzr] ? (i_sxfci_(_0x3628("0x3cb"))[_0x3628("0x3e")]["display"] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x415"))[_0x3628("0x3e")]["display"] = _0x3628("0x8c")) : _0x3628("0x486") == i_FZEFji["Nick" + i_BDeZzr] ? (i_sxfci_("plmenu_switch")["style"][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_("plmenu_open")[_0x3628("0x3e")]["display"] = _0x3628("0x8c")) : (i_pjQNyo() && (i_sxfci_(_0x3628("0xf1"))["style"][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x387"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0xc1"))[_0x3628("0x3e")]["display"] = _0x3628("0x8c")), i_sxfci_(_0x3628("0x17f"))["style"][_0x3628("0x3b5")] = _0x3628("0x8c"));
    }
  } else i_paAhIr(i_BDeZzr, i_BDeZzr = _0x3703f6);
}
function i_mASxDr() {
  i_Hfbes_(_0x3628("0x216"), function () {
    if ("kXyxo" === "iCnxw") {
      i_MzyCVu();
    } else {
      i_WwiRv_(_0x3628("0x97"), _0x3628("0x8c"));
    }
  }), i_cirYl_(_0x3628("0x2a0"), function (_0x883bfe) {
    i_WwiRv_("quickBox", "block"), 0x0 == i_iDpHde ? i_Wkbybr(_0x3628("0xbe")) : i_hnXPpo(_0x3628("0x2a5"), this[_0x3628("0x27e")]);
  }), i_Hfbes_("tab_roomchat", function () {
    i_sxfci_(_0x3628("0x2f1"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), i_sxfci_(_0x3628("0x3b2"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", i_sxfci_("tab_roomchat")["classList"]["add"](_0x3628("0x185")), i_sxfci_("tab_roomwatch")["classList"][_0x3628("0x47a")](_0x3628("0x185"));
  }), i_Hfbes_("tab_roomwatch", function () {
    if ("rpQnA" === _0x3628("0x4cf")) {
      i_sxfci_(_0x3628("0x2f1"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", i_sxfci_(_0x3628("0x3b2"))[_0x3628("0x3e")]["display"] = "block", i_sxfci_(_0x3628("0x53"))["classList"]["remove"](_0x3628("0x185")), i_sxfci_(_0x3628("0x41"))[_0x3628("0xea")][_0x3628("0x405")]("switchChatAndWatch");
    } else {
      i_thkew_ = i_sxfci_(_0x3628("0x552")), i_zMDwk_ = i_sxfci_(i_iDQBme ? "helpergui" : _0x3628("0xa9"));
    }
  }), i_Hfbes_(_0x3628("0x562"), function (_0x42cfcf) {
    i_kdydUr(0x1, _0x42cfcf);
  }), i_Hfbes_(_0x3628("0x90"), function (_0x4e8c5c) {
    i_kdydUr(0x2, _0x4e8c5c);
  }), i_Hfbes_("p3card", function (_0x540ceb) {
    i_kdydUr(0x3, _0x540ceb);
  }), i_Hfbes_(_0x3628("0x50d"), function (_0xd02f57) {
    i_kdydUr(0x4, _0xd02f57);
  }), i_cirYl_("plmenuitem", i_rMrpNr), i_Hfbes_(_0x3628("0xf0"), function () {
    i_sxfci_(_0x3628("0x1f8"))[_0x3628("0x3e")]["display"] = _0x3628("0x47e");
  }), i_Hfbes_(_0x3628("0x3ef"), function () {
    window[_0x3628("0x497")][_0x3628("0x16d")](window[_0x3628("0x497")]["origin"] + window[_0x3628("0x497")][_0x3628("0x4e5")] + _0x3628("0x354") + gid + _0x3628("0x360") + i_wesTbe + _0x3628("0x18f") + i_ZmCwpe);
  });
}
var i_wPSp$r = 0x0;
function i_nZYEBr(_0x561a47, _0x3b8d9d) {
  i_wPSp$r = _0x561a47, i_sxfci_(_0x3628("0x31a"))[_0x3628("0xea")]["remove"](_0x3628("0x494")), "空" != _0x3b8d9d["innerText"] && "" != _0x3b8d9d[_0x3628("0x27e")] ? i_sxfci_(_0x3628("0x361"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x22f")) : i_sxfci_(_0x3628("0x361"))[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x22f"));
}
function i_QWiaGr() {
  return 0x6 < i_wPSp$r && i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")] <= 0x0 ? (i_Wkbybr(_0x3628("0x2ab")), void i_ankaua["ze"](_0x3628("0x56f"))) : 0x4 < i_wPSp$r && i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")] <= 0x0 && i_DdTaQf[_0x3628("0x31")][_0x3628("0x1ba")] <= 0x0 ? (i_Wkbybr(_0x3628("0x538")), void i_ankaua["ze"]()) : void i_WmWryu(i_wPSp$r);
}
function i_EMQDFr() {
  0x0 <= this[_0x3628("0x4a8")][_0x3628("0x2f3")]("PrivateBtn_readact") || i_ZjZkwu(i_wPSp$r);
}
function i_XENaLr() {
  i_Hfbes_("btn_save_close", function () {
    if ("FnvGz" !== _0x3628("0x4ee")) {
      i_CkPAe[_0x3628("0x314")](_0x3628("0x4ab"), i_WdHCn);
    } else {
      i_WwiRv_(_0x3628("0x50c"), "block");
    }
  }), i_Hfbes_(_0x3628("0x31a"), i_QWiaGr), i_Hfbes_(_0x3628("0x361"), i_EMQDFr), i_Hfbes_(_0x3628("0x1f5"), function () {
    i_dhBiu_(_0x3628("0x418"), function (_0x537125) {
      _0x537125[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2eb"));
    }), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x2eb")), i_nZYEBr(0x1, i_sxfci_(_0x3628("0x1bf")));
  }), i_Hfbes_(_0x3628("0x88"), function () {
    i_dhBiu_(_0x3628("0x418"), function (_0x416607) {
      if (_0x3628("0x365") !== _0x3628("0x365")) {
        i_WwiRv_(_0x3628("0x52e"), _0x3628("0x8c"));
      } else {
        _0x416607[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2eb"));
      }
    }), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x2eb")), i_nZYEBr(0x2, i_sxfci_("sv2time"));
  }), i_Hfbes_("card_save3", function () {
    i_dhBiu_(_0x3628("0x418"), function (_0x3ccfbd) {
      _0x3ccfbd[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2eb"));
    }), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x2eb")), i_nZYEBr(0x3, i_sxfci_(_0x3628("0x4d")));
  }), i_Hfbes_(_0x3628("0x4b6"), function () {
    i_dhBiu_(_0x3628("0x418"), function (_0x2de040) {
      _0x2de040[_0x3628("0xea")][_0x3628("0x47a")]("File_ind_act");
    }), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x2eb")), i_nZYEBr(0x4, i_sxfci_("sv4time"));
  }), i_Hfbes_(_0x3628("0x4b2"), function () {
    i_dhBiu_("savecard", function (_0x22fd48) {
      _0x22fd48[_0x3628("0xea")][_0x3628("0x47a")]("File_ind_act");
    }), this["classList"]["add"]("File_ind_act"), i_nZYEBr(0x5, i_sxfci_("sv5time"));
  }), i_Hfbes_(_0x3628("0x2d6"), function () {
    i_dhBiu_("savecard", function (_0x5d1fd5) {
      _0x5d1fd5["classList"][_0x3628("0x47a")](_0x3628("0x2eb"));
    }), this[_0x3628("0xea")]["add"](_0x3628("0x2eb")), i_nZYEBr(0x6, i_sxfci_("sv6time"));
  }), i_Hfbes_(_0x3628("0x493"), function () {
    i_dhBiu_("savecard", function (_0x2d581f) {
      _0x2d581f[_0x3628("0xea")][_0x3628("0x47a")]("File_ind_act");
    }), this["classList"]["add"](_0x3628("0x2eb")), i_nZYEBr(0x7, i_sxfci_(_0x3628("0x317")));
  }), i_Hfbes_(_0x3628("0x4fb"), function () {
    if (_0x3628("0x3b4") === "nXDvA") {
      i_dhBiu_(_0x3628("0x418"), function (_0xa38f85) {
        if (_0x3628("0xa0") !== _0x3628("0xa6")) {
          _0xa38f85["classList"][_0x3628("0x47a")]("File_ind_act");
        } else {
          i_pFxkn["style"][_0x3628("0x270")] = _0x3628("0x49c");
        }
      }), this[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x2eb")), i_nZYEBr(0x8, i_sxfci_(_0x3628("0x18b")));
    } else {
      var _0x5b9f13;
      _0x5b9f13 = parseInt(this["id"][_0x3628("0x16d")](_0x3628("0x349"), "")), i_kJKK$t["sv" + _0x5b9f13] = 0x0, i_kJKK$t["st" + _0x5b9f13] = null, i_TkwGLt(), i_sxfci_(_0x3628("0x4ec") + _0x5b9f13)[_0x3628("0x306")] = !0x1;
    }
  });
  var _0x562080 = new XMLHttpRequest();
  _0x562080[_0x3628("0x3d8")](_0x3628("0x13f"), "/" + i_kJwtg + _0x3628("0x312") + gid, !0x0), _0x562080[_0x3628("0x1c4")] = _0x3628("0x346"), _0x562080[_0x3628("0x278")] = function () {
    if ("zuJML" !== "DDiTz") {
      if (_0x562080[_0x3628("0xee")] == XMLHttpRequest[_0x3628("0x4e")] && 0xc8 == _0x562080[_0x3628("0x446")]) {
        if (_0x3628("0x35f") === _0x3628("0x12d")) {
          i_FDTyt = 0x2 * i_EHzDe[0x31 + i_rTAen], i_PicFa = 0x2 * i_EHzDe[0x41 + i_rTAen] + 0x2 * i_EHzDe[0x51 + i_rTAen] - 0x94;
          i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x1ce"), i_aPKPKe["strokeStyle"] = "#111";
          var _0x51d5ca = "";
          i_tdceon["M"][_0x3628("0x32d")] && (_0x51d5ca = i_rTAen + 0x1 + ".\x20"), i_tdceon["T"][_0x3628("0x32d")] && (i_aPKPKe[_0x3628("0x1fd")](_0x51d5ca + "HP:" + 0x64 * i_EHzDe[0x11 + i_rTAen], i_FDTyt + 0x1 - 0xa, i_PicFa + 0x1, 0x64), i_aPKPKe[_0x3628("0x1c2")](_0x51d5ca + _0x3628("0x44c") + 0x64 * i_EHzDe[0x11 + i_rTAen], i_FDTyt - 0xa, i_PicFa, 0x64)), i_aPKPKe[_0x3628("0x25e")] = _0x3628("0x3a0"), i_aPKPKe[_0x3628("0x285")] = 0x1, i_aPKPKe[_0x3628("0xfb")](), i_aPKPKe[_0x3628("0x190")](i_FDTyt - 0x10, i_PicFa - 0x14, 0x3c, 0x2), i_aPKPKe[_0x3628("0x324")](), i_aPKPKe["stroke"]();
          var _0x49172c = i_EHzDe[0x11 + i_rTAen] / i_EHzDe[0x21 + i_rTAen] * 0x3c;
          i_aPKPKe[_0x3628("0x46d")] = 0x28 < _0x49172c ? _0x3628("0xa2") : 0x14 < _0x49172c ? _0x3628("0x220") : "#cf0000", i_aPKPKe[_0x3628("0xb1")](i_FDTyt - 0x10, i_PicFa - 0x14, _0x49172c, 0x2), i_PPDas++;
        } else {
          var _0x38b5c9 = _0x562080[_0x3628("0x3f")];
          i_kJKK$t = _0x38b5c9, i_NcxYqr();
        }
      }
    } else {
      i_zXJHnu(i_cZwcUl["dn"], 0xff & i_rnbke, i_rnbke / 0x100);
    }
  }, _0x562080[_0x3628("0x559")]();
}
function i_NcxYqr() {
  0x1 == i_kJKK$t[_0x3628("0x55a")] ? ($(_0x3628("0x44d"))["html"](i_kJKK$t["st1"]), null == i_cQmQBt["d1i"] && (i_cQmQBt[_0x3628("0x1cb")] = "/" + i_kJwtg + _0x3628("0xde") + gid), i_sxfci_(_0x3628("0x186"))["src"] = i_cQmQBt[_0x3628("0x1cb")]) : ($(_0x3628("0x44d"))[_0x3628("0x24b")](""), i_sxfci_("sv1img")[_0x3628("0x1c5")] = "https://static.wo1wan.com/game/empsv.png"), 0x1 == i_kJKK$t[_0x3628("0x3f8")] ? ($(_0x3628("0x2e5"))[_0x3628("0x24b")](i_kJKK$t[_0x3628("0x213")]), null == i_cQmQBt[_0x3628("0x4d7")] && (i_cQmQBt[_0x3628("0x4d7")] = "/" + i_kJwtg + _0x3628("0x3fb") + gid), i_sxfci_(_0x3628("0x451"))[_0x3628("0x1c5")] = i_cQmQBt[_0x3628("0x4d7")]) : ($(_0x3628("0x2e5"))[_0x3628("0x24b")](""), i_sxfci_("sv2img")[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t[_0x3628("0xaf")] ? ($(_0x3628("0x222"))["html"](i_kJKK$t[_0x3628("0x2c3")]), null == i_cQmQBt[_0x3628("0x256")] && (i_cQmQBt[_0x3628("0x256")] = "/" + i_kJwtg + _0x3628("0x19e") + gid), i_sxfci_("sv3img")["src"] = i_cQmQBt[_0x3628("0x256")]) : ($("#sv3time")[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x4"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t[_0x3628("0x43e")] ? ($("#sv4time")[_0x3628("0x24b")](i_kJKK$t[_0x3628("0x4d0")]), null == i_cQmQBt[_0x3628("0x35")] && (i_cQmQBt[_0x3628("0x35")] = "/" + i_kJwtg + _0x3628("0x108") + gid), i_sxfci_(_0x3628("0x4c4"))[_0x3628("0x1c5")] = i_cQmQBt[_0x3628("0x35")]) : ($(_0x3628("0x2ee"))["html"](""), i_sxfci_(_0x3628("0x4c4"))["src"] = _0x3628("0x116")), 0x1 == i_kJKK$t["W"] ? ($(_0x3628("0x524"))[_0x3628("0x24b")](i_kJKK$t["J"]), null == i_cQmQBt["Z"] && (i_cQmQBt["Z"] = "/" + i_kJwtg + _0x3628("0x2b5") + gid), i_sxfci_("sv5img")["src"] = i_cQmQBt["Z"]) : ($(_0x3628("0x524"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x133"))["src"] = "https://static.wo1wan.com/game/empsv.png"), 0x1 == i_kJKK$t["ee"] ? ($("#sv6time")[_0x3628("0x24b")](i_kJKK$t["ne"]), null == i_cQmQBt["te"] && (i_cQmQBt["te"] = "/" + i_kJwtg + _0x3628("0x1b8") + gid), i_sxfci_("sv6img")["src"] = i_cQmQBt["te"]) : ($("#sv6time")[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x35c"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["ae"] ? ($(_0x3628("0x10f"))[_0x3628("0x24b")](i_kJKK$t["ie"]), null == i_cQmQBt["oe"] && (i_cQmQBt["oe"] = "/" + i_kJwtg + _0x3628("0x4d4") + gid), i_sxfci_("sv7img")["src"] = i_cQmQBt["oe"]) : ($(_0x3628("0x10f"))["html"](""), i_sxfci_(_0x3628("0xcb"))[_0x3628("0x1c5")] = _0x3628("0x116")), 0x1 == i_kJKK$t["ce"] ? ($("#sv8time")["html"](i_kJKK$t["re"]), null == i_cQmQBt["le"] && (i_cQmQBt["le"] = "/" + i_kJwtg + _0x3628("0x48e") + gid), i_sxfci_(_0x3628("0x3c2"))[_0x3628("0x1c5")] = i_cQmQBt["le"]) : ($(_0x3628("0x18"))[_0x3628("0x24b")](""), i_sxfci_(_0x3628("0x3c2"))["src"] = _0x3628("0x116"));
}
function i_PNGrVr() {
  var _0x3da4b2 = new XMLHttpRequest();
  _0x3da4b2["open"](_0x3628("0x4b"), "/" + i_kJwtg + _0x3628("0x38a") + gid, !0x0), _0x3da4b2[_0x3628("0x59")](_0x3628("0x1de"), _0x3628("0x564")), _0x3da4b2["onreadystatechange"] = function () {
    _0x3da4b2[_0x3628("0xee")] == XMLHttpRequest[_0x3628("0x4e")] && _0x3da4b2[_0x3628("0x446")];
  }, _0x3da4b2["send"](JSON[_0x3628("0x2ae")](i_kJKK$t)), i_NcxYqr();
}
function i_etMQjr() {
  var _0x344dcf = i_sxfci_(_0x3628("0x12c")),
    _0x116aa8 = i_sxfci_(_0x3628("0x47b")),
    _0x50a394 = i_sxfci_("basicRangeWidth"),
    _0x8b4fb9 = i_sxfci_(_0x3628("0x27b"));
  _0x344dcf[_0x3628("0x27e")] = _0x50a394[_0x3628("0x32d")] + "%", _0x50a394["style"][_0x3628("0x1c8")] = (_0x50a394[_0x3628("0x32d")] - 0x28) / 0x3c * 0x64 + "%,100%", _0x116aa8[_0x3628("0x27e")] = _0x8b4fb9[_0x3628("0x32d")] + "%", _0x8b4fb9["style"][_0x3628("0x1c8")] = (_0x8b4fb9["value"] - 0x1e) / 0x46 * 0x64 + _0x3628("0x43"), document[_0x3628("0x148")][_0x3628("0x3e")][_0x3628("0x189")](_0x3628("0x1e5"), _0x50a394[_0x3628("0x32d")] + "%"), i_WmyDOu["MobBase"]["Lock43"] ? document[_0x3628("0x148")][_0x3628("0x3e")][_0x3628("0x189")](_0x3628("0x36b"), i_sxfci_("whathis")[_0x3628("0x3a1")] / 0x4 * 0x3 + "px") : document["body"][_0x3628("0x3e")][_0x3628("0x189")](_0x3628("0x36b"), _0x8b4fb9["value"] + "%"), document["body"][_0x3628("0x3e")][_0x3628("0x189")](_0x3628("0x357"), (0x64 - _0x50a394[_0x3628("0x32d")]) / 0x2 + "%"), i_WmyDOu[_0x3628("0x485")]["Width"] = _0x50a394[_0x3628("0x32d")], i_WmyDOu[_0x3628("0x485")][_0x3628("0x26c")] = _0x8b4fb9[_0x3628("0x32d")];
}
function i_jCWyYr() {
  var _0xd28bd7 = i_sxfci_("boostSpeedTxt"),
    _0x40a78 = i_sxfci_(_0x3628("0x184"));
  _0x40a78["style"][_0x3628("0x1c8")] = _0x40a78[_0x3628("0x32d")] / 0x8 * 0x64 + _0x3628("0x43"), _0xd28bd7[_0x3628("0x27e")] = "\x20一秒" + Math[_0x3628("0x560")](0x3c / (0x2 * (0x9 - parseInt(_0x40a78["value"])))) + "次", i_WmyDOu[_0x3628("0x7e")] = parseInt(_0x40a78["value"]), i_XFSipu(i_WmyDOu["gSuperSpeed"]);
}
function i_WTCyHr() {
  i_tXrZr_("cb_vidstyle", "change", function (_0x2b6ca2) {
    i_ZcmNFu(this[_0x3628("0x206")]);
  }), i_tXrZr_(_0x3628("0x442"), _0x3628("0x47d"), function () {
    i_sxfci_("SetUp")[_0x3628("0x3e")]["opacity"] = _0x3628("0x367");
  }), i_tXrZr_(_0x3628("0x442"), _0x3628("0x407"), function () {
    i_sxfci_(_0x3628("0x481"))["style"]["opacity"] = "";
  }), i_rBKib_(_0x3628("0x453"), function (_0x1e8cfa) {
    i_WmyDOu["MobBase"][_0x3628("0x1c1")] && (i_sxfci_("basicRangeHeight")[_0x3628("0x32d")] = _0x1e8cfa[_0x3628("0x32d")] / 0x4 * 0x3), i_etMQjr();
  }), i_rBKib_("basicRangeHeight", function (_0x3eb176) {
    i_etMQjr();
  }), i_rBKib_("boostSpeed", function (_0x34e9a5) {
    i_jCWyYr();
  }), i_tXrZr_(_0x3628("0x510"), _0x3628("0x2fd"), function (_0x41ae44) {
    if ("qfvuG" !== _0x3628("0x28d")) {
      i_sxfci_(_0x3628("0x207"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x303")], i_sxfci_(_0x3628("0x529"))[_0x3628("0x206")] = i_WmyDOu["gBSuper"], i_sxfci_(_0x3628("0x477"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x247")], i_sxfci_(_0x3628("0x39b"))[_0x3628("0x206")] = i_WmyDOu["gDSuper"], i_sxfci_("SP_E")[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x215")], i_sxfci_(_0x3628("0x38"))["checked"] = i_WmyDOu[_0x3628("0x5c")], i_sxfci_("AutoSpeedText")[_0x3628("0x27e")] = i_WmyDOu[_0x3628("0x7e")], i_sxfci_(_0x3628("0x41d"))[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0x7e")];
    } else {
      i_WmyDOu[_0x3628("0x485")][_0x3628("0x1c1")] = this["checked"], this["checked"] ? i_sxfci_(_0x3628("0x27b"))["disabled"] = !0x0 : i_sxfci_(_0x3628("0x27b"))[_0x3628("0x306")] = !0x1;
    }
  });
}
function i_fKBDWr(_0x3ad4f1) {
  document["getElementById"](_0x3628("0x2f0"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x19f") + _0x3ad4f1 + _0x3628("0xd5")] & i_TEaXzu["A"], document[_0x3628("0x416")](_0x3628("0x1a7"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x19f") + _0x3ad4f1 + _0x3628("0xd5")] & i_TEaXzu["B"], document["getElementById"](_0x3628("0x21c"))[_0x3628("0x206")] = i_WmyDOu["gExtX" + _0x3ad4f1 + _0x3628("0xd5")] & i_TEaXzu["C"], document[_0x3628("0x416")](_0x3628("0x47"))["checked"] = i_WmyDOu[_0x3628("0x19f") + _0x3ad4f1 + "Key"] & i_TEaXzu["L"], document[_0x3628("0x416")](_0x3628("0x4c8"))[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x19f") + _0x3ad4f1 + _0x3628("0xd5")] & i_TEaXzu["E"], document[_0x3628("0x416")]("cb_xbtnF")[_0x3628("0x206")] = i_WmyDOu[_0x3628("0x19f") + _0x3ad4f1 + _0x3628("0xd5")] & i_TEaXzu["q"];
}
function i_WKxiJr() {
  i_Hfbes_(_0x3628("0x299"), function () {
    if (_0x3628("0x134") === _0x3628("0x134")) {
      i_WwiRv_(_0x3628("0x481"), _0x3628("0x8c")), i_kymCil();
    } else {
      i_rccea["style"][_0x3628("0x191")] = 0x4 * i_BDaDt + "px", i_rccea[_0x3628("0x3e")][_0x3628("0x9")] = 0x3 * i_BDaDt + "px";
      var _0x2afce5 = (i_CYQWn[_0x3628("0x188")] - 0x40 - 0x3 * i_BDaDt) / 0x2;
      i_rccea[_0x3628("0x3e")][_0x3628("0x436")] = _0x2afce5 + "px", i_rccea[_0x3628("0x3e")][_0x3628("0x290")] = _0x2afce5 + "px";
    }
  }), i_Hfbes_(_0x3628("0x151"), function () {
    if (_0x3628("0x10e") === "irAvF") {
      $("#soundvol")[_0x3628("0x489")]({
        min: 0x0,
        max: 0x64,
        step: 0.1,
        i: function (_0x3890c5) {
          i_rHShLf(_0x3890c5["value"] / 0x64), i_WmyDOu["gSoundVol"] = _0x3890c5[_0x3628("0x32d")];
        }
      }), i_sxfci_(_0x3628("0x37"))[_0x3628("0x314")](_0x3628("0x1a9"), function () {
        i_JcAsUu();
      });
    } else {
      i_WwiRv_(_0x3628("0x481"), _0x3628("0x8c")), i_JcAsUu();
    }
  }), i_Hfbes_(_0x3628("0x38e"), function () {
    i_WwiRv_(_0x3628("0x481"), _0x3628("0x8c")), i_JcAsUu();
  });
  var _0x127b49 = document[_0x3628("0x416")](_0x3628("0xb7")),
    _0x50367e = document[_0x3628("0x416")](_0x3628("0x318")),
    _0x4b7ea1 = document[_0x3628("0x416")]("gesturel"),
    _0x22a88b = document[_0x3628("0x416")]("roomSet");
  i_Hfbes_(_0x3628("0x4d2"), function () {
    if (_0x3628("0x536") === "FPgSb") {
      _0x127b49[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), _0x22a88b["style"]["display"] = _0x3628("0x47e"), _0x50367e["style"]["display"] = _0x3628("0x47e"), _0x4b7ea1[_0x3628("0x3e")]["display"] = "none";
    } else {
      i_Qyfwws[_0x3628("0x23c")](i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"], i_cKFiYo[_0x3628("0x191")], i_cKFiYo[_0x3628("0x9")]), i_iFrkZo ? (i_GHKWya["sn"](), i_Qyfwws[_0x3628("0x250")](i_cKFiYo, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"])) : (i_GHKWya["rn"](), i_Qyfwws[_0x3628("0x250")](i_NQhmFo, i_cffPrs["BB"]["X"], i_cffPrs["BB"]["Y"]));
    }
  }), i_Hfbes_(_0x3628("0x298"), function () {
    if (_0x3628("0x8d") === _0x3628("0x2af")) {
      if (i_mbJin[_0x3628("0xee")] == XMLHttpRequest["DONE"] && 0xc8 == i_mbJin[_0x3628("0x446")]) {
        var _0x1bfd9b = i_mbJin[_0x3628("0x3f")];
        null == _0x1bfd9b[_0x3628("0x46f")] || null == _0x1bfd9b[_0x3628("0xe1")] ? i_JcAsUu() : i_WmyDOu = _0x1bfd9b, null == i_WmyDOu[_0x3628("0x53d")] && (i_WmyDOu[_0x3628("0x53d")] = !0x1, i_WmyDOu[_0x3628("0x296")] = 0x0, i_WmyDOu[_0x3628("0x3b8")] = !0x1, i_WmyDOu[_0x3628("0x2ca")] = 0x0, i_WmyDOu[_0x3628("0x46f")][_0x3628("0x3d6")] = "NO", i_WmyDOu[_0x3628("0x46f")]["KeyX6"] = "NO", i_WmyDOu["GPSetx"]["KeyX5"] = -0x1, i_WmyDOu[_0x3628("0xe1")][_0x3628("0xb6")] = -0x1), null == i_WmyDOu[_0x3628("0x19b")] && (i_WmyDOu["gExtX3"] = !0x1, i_WmyDOu["gExtX3Key"] = 0x0, i_WmyDOu[_0x3628("0x315")] = "4\x208\x202\x20A\x20", i_WmyDOu[_0x3628("0xa1")] = "4\x203\x20A\x20", i_WmyDOu[_0x3628("0x363")] = "", i_WmyDOu[_0x3628("0x4d5")] = ""), null == i_WmyDOu[_0x3628("0xb")] && (i_WmyDOu[_0x3628("0xb")] = !0x1, i_WmyDOu[_0x3628("0x143")] = 0x0), null == i_WmyDOu[_0x3628("0x2ba")] && (i_WmyDOu[_0x3628("0x2ba")] = 0x4), null == i_WmyDOu["PCSet"] && (i_WmyDOu[_0x3628("0x46f")] = i_jwnmXu), null == i_WmyDOu[_0x3628("0xe1")] && (i_WmyDOu["GPSetx"] = i_mACQRu), null == i_WmyDOu[_0x3628("0x16")] && (i_WmyDOu[_0x3628("0x16")] = !0x0), null == i_WmyDOu[_0x3628("0x40d")] && (i_WmyDOu["gSoundVol"] = 0x46), null == i_WmyDOu[_0x3628("0x1d8")] && (i_WmyDOu["lockScr"] = !0x1), null == i_WmyDOu[_0x3628("0x192")] && (i_WmyDOu["gDisHoldLR"] = !0x1), null == i_WmyDOu[_0x3628("0x2a3")] && (i_WmyDOu[_0x3628("0x2a3")] = 0x4), null == i_WmyDOu[_0x3628("0xe1")]["gKeyRev"] && (i_WmyDOu[_0x3628("0xe1")][_0x3628("0x2cd")] = !0x1), null == i_WmyDOu["GPSetx"][_0x3628("0x33f")] && (i_WmyDOu[_0x3628("0xe1")]["gKeyRevLR"] = !0x1), i_NRRXBu(), i_AQsA$u(), i_XejQKu = !0x0;
      }
    } else {
      _0x22a88b["style"][_0x3628("0x3b5")] = "block", _0x127b49[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x50367e[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x4b7ea1[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
    }
  }), i_Hfbes_(_0x3628("0x3cc"), function () {
    _0x50367e[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x8c"), _0x22a88b["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x127b49["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x4b7ea1[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
  }), i_Hfbes_(_0x3628("0x348"), function () {
    _0x50367e[_0x3628("0x3e")]["display"] = "none", _0x22a88b[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", _0x127b49[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"), _0x4b7ea1["style"][_0x3628("0x3b5")] = _0x3628("0x8c");
  }), $(_0x3628("0x2d1"))[_0x3628("0x4ab")](function () {
    $(this)[_0x3628("0xd3")](_0x3628("0x2d1"))[_0x3628("0xe0")](_0x3628("0x89")), $(this)[_0x3628("0x32")](_0x3628("0x89"));
  }), i_RyHcQr(), i_PYeZel(), i_WTCyHr();
}
function i_RyHcQr() {
  var _0x52624c = i_sxfci_("selectOptionBox"),
    _0xfb4c8a = i_sxfci_(_0x3628("0x4d3"));
  i_Hfbes_(_0x3628("0x399"), function () {
    _0x3628("0x4d3") === _0xfb4c8a[_0x3628("0x4a8")] ? (_0x52624c[_0x3628("0x3e")][_0x3628("0x3b5")] = "block", _0xfb4c8a["classList"][_0x3628("0x405")]("optionDownRev"), _0xfb4c8a[_0x3628("0xea")][_0x3628("0x47a")]("optionDown")) : (_0x52624c["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), _0xfb4c8a[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x4d3")), _0xfb4c8a[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2fc")));
  }), i_bTXGHs();
}
var i_TaiyZr = _0x3628("0x4df");
function i_PYeZel() {
  $(_0x3628("0x535"))[_0x3628("0x4ab")](function () {
    if (_0x3628("0x4bd") === _0x3628("0x4bd")) {
      $(this)[_0x3628("0xd3")](_0x3628("0x535"))[_0x3628("0xe0")](_0x3628("0x4d1")), $(this)[_0x3628("0xd3")](_0x3628("0x535"))[_0x3628("0x32")](_0x3628("0xc3")), $(this)[_0x3628("0x32")]("_com_TabAct"), $(this)[_0x3628("0xe0")](_0x3628("0xc3"));
    } else {
      var _0x2609e9 = document["getElementById"](i_rAiAe);
      null != _0x2609e9 && _0x2609e9[_0x3628("0x314")](_0x3628("0x4ab"), i_BMXCn);
    }
  }), i_cirYl_("extkeytab", function (_0x4723c6) {
    i_TaiyZr = i_Brhwo_(_0x4723c6[_0x3628("0x2f9")]), i_sxfci_("cb_xbtnSwitch")["checked"] = i_WmyDOu[i_TaiyZr], i_fKBDWr(i_TaiyZr[_0x3628("0x16d")](_0x3628("0x19f"), ""));
  }), i_tXrZr_("cb_xbtnSwitch", _0x3628("0x2fd"), function (_0x57b24d) {
    if (_0x3628("0x34f") === "XGYDL") {
      if (i_tdame[_0x3628("0x2e7")]["byteLength"] % 0xc == 0x0 || 0x8 == i_tdame[_0x3628("0x2e7")][_0x3628("0x22")]) new Uint32Array(i_tdame[_0x3628("0x2e7")]);
      i_DYNBTe[_0x3628("0x3ab")](i_tdame[_0x3628("0x2e7")], [i_tdame["data"]]);
    } else {
      i_WmyDOu[i_TaiyZr] = this[_0x3628("0x206")], i_MzyCVu();
    }
  }), i_ZKQCf_(_0x3628("0x8a"), _0x3628("0x2fd"), function () {
    var _0x24017a = i_TaiyZr[_0x3628("0x16d")](_0x3628("0x19f"), "");
    switch (this["id"]) {
      case _0x3628("0x2f0"):
        this[_0x3628("0x206")] ? i_WmyDOu["gExtX" + _0x24017a + _0x3628("0xd5")] |= i_TEaXzu["A"] : i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] &= ~i_TEaXzu["A"];
        break;
      case _0x3628("0x1a7"):
        this[_0x3628("0x206")] ? i_WmyDOu["gExtX" + _0x24017a + _0x3628("0xd5")] |= i_TEaXzu["B"] : i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] &= ~i_TEaXzu["B"];
        break;
      case _0x3628("0x21c"):
        this["checked"] ? i_WmyDOu[_0x3628("0x19f") + _0x24017a + "Key"] |= i_TEaXzu["C"] : i_WmyDOu["gExtX" + _0x24017a + "Key"] &= ~i_TEaXzu["C"];
        break;
      case _0x3628("0x47"):
        this[_0x3628("0x206")] ? i_WmyDOu["gExtX" + _0x24017a + _0x3628("0xd5")] |= i_TEaXzu["L"] : i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] &= ~i_TEaXzu["L"];
        break;
      case _0x3628("0x4c8"):
        this[_0x3628("0x206")] ? i_WmyDOu["gExtX" + _0x24017a + "Key"] |= i_TEaXzu["E"] : i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] &= ~i_TEaXzu["E"];
        break;
      case _0x3628("0x332"):
        this[_0x3628("0x206")] ? i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] |= i_TEaXzu["q"] : i_WmyDOu[_0x3628("0x19f") + _0x24017a + _0x3628("0xd5")] &= ~i_TEaXzu["q"];
    }
  });
}
var i_MSkKnl = !0x1,
  i_wCwYtl = !0x0;
function i_HWZeal() {
  i_wCwYtl = !i_wCwYtl;
  var _0x3f8e64 = i_sxfci_(_0x3628("0x443"));
  0x0 == i_wCwYtl ? _0x3f8e64[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x9e")) : _0x3f8e64["classList"]["add"](_0x3628("0x9e"));
  var _0x9a95bd = i_sxfci_(_0x3628("0x4e9"));
  0x0 == i_wCwYtl ? (_0x9a95bd[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x3c3")), _0x9a95bd["children"][0x0]["classList"][_0x3628("0x47a")](_0x3628("0x457")), _0x9a95bd["children"][0x0][_0x3628("0xea")][_0x3628("0x405")]("igwi-gengduo2")) : (_0x9a95bd[_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x3c3")), _0x9a95bd[_0x3628("0x1a0")][0x0]["classList"][_0x3628("0x47a")](_0x3628("0x2d4")), _0x9a95bd[_0x3628("0x1a0")][0x0][_0x3628("0xea")][_0x3628("0x405")](_0x3628("0x457")));
}
function i_kymCil() {
  i_MSkKnl = i_WwiRv_(_0x3628("0x46a"), _0x3628("0x8c")) ? (i_sxfci_(_0x3628("0xa8"))["style"][_0x3628("0x4d6")] = _0x3628("0x27d"), !0x0) : (i_sxfci_(_0x3628("0xa8"))[_0x3628("0x3e")][_0x3628("0x4d6")] = "", !0x1);
}
var i_kpKwol = 0x1;
function i_mRsncl() {
  i_Hfbes_(_0x3628("0x4e9"), i_HWZeal), i_Hfbes_(_0x3628("0xa8"), i_kymCil), i_HWZeal(), i_Hfbes_(_0x3628("0x2bc"), function () {
    i_WwiRv_(_0x3628("0x52e"), _0x3628("0x8c"));
  }), i_Hfbes_(_0x3628("0x172"), function () {
    i_WwiRv_(_0x3628("0x52e"), "block");
  }), 0x1 == i_iDpHde ? i_sxfci_(_0x3628("0xe7"))[_0x3628("0x3e")][_0x3628("0x3b5")] = "none" : i_Hfbes_(_0x3628("0xe7"), function () {
    this[_0x3628("0x3e")][_0x3628("0x4d6")] = i_kpKwol < 0x8 ? (i_kpKwol *= 0x2, this[_0x3628("0x24d")] = i_kpKwol + "X", _0x3628("0x5f")) : (i_kpKwol = 0x1, this[_0x3628("0x24d")] = "加速", _0x3628("0x3a0")), i_bkNDhu(i_kpKwol);
  });
}
function i_NeMSsl() {}
var i_CcCZrl = -0x989298;
function i_WBYHll() {
  i_CcCZrl + 0x493e0 > performance["now"]() ? i_iZhRvt(_0x3628("0x37e")) : (i_CcCZrl = performance[_0x3628("0x30b")](), "" != i_sxfci_(_0x3628("0x483"))[_0x3628("0x32d")] ? ($["ajax"](_0x3628("0x2be"), {
    type: _0x3628("0x4b"),
    async: !0x0,
    data: {
      gtype: "fc",
      gamename: i_BThm_e,
      gid: gid,
      lid: i_ZmCwpe,
      call: _0x3628("0x3ad"),
      max: i_ZHidMe,
      now: i_xjHeZi,
      pass: "",
      sev: i_hkNr_o,
      msg: i_sxfci_("txt_shareinfo")["value"],
      se: i_YHmAt_("igwflyfree")
    },
    crossDomain: !0x0,
    xhrFields: {
      withCredentials: !0x1
    },
    success: function (_0x16a171, _0x158947, _0x52dd2f) {
      "ok" == _0x16a171["status"] ? i_rrYppt("邀请发送成功，等待伙伴到来吧") : i_iZhRvt(_0x3628("0x2d0"));
    }
  }), i_sxfci_("popwin_sharegame")[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e")) : i_iZhRvt(_0x3628("0x115")));
}
var i_dPmwul = 0x0,
  i_sQwkfl = 0x0,
  i_njFT_l = !0x1;
function i_Bsazvl() {
  var _0x48a483 = $(".replay-slider"),
    _0x48e386 = $(".replay-slider__range"),
    _0xf04759 = $(_0x3628("0x5b"));
  _0x48a483[_0x3628("0x4a")](function () {
    _0x48e386["on"](_0x3628("0x440"), function () {
      switch (this["id"]) {
        case _0x3628("0x4c9"):
          var _0x42da02 = 0x3e8 / 0x3c * this[_0x3628("0x32d")] / 0x3e8;
          _0x42da02 = "0" + Math["floor"](_0x42da02 / 0xe10) + ":" + Math[_0x3628("0x560")](_0x42da02 / 0x3c) % 0x3c + ":" + Math[_0x3628("0x560")](_0x42da02 % 0x3c), $(this)[_0x3628("0x29a")](_0xf04759)[_0x3628("0x24b")](_0x42da02), i_heWQTl(this[_0x3628("0x32d")]);
          break;
        case _0x3628("0x2f4"):
          $(this)[_0x3628("0x29a")](_0xf04759)[_0x3628("0x24b")](this[_0x3628("0x32d")]), i_XFSipu(this["value"]), i_WmyDOu["gSuperSpeed"] = parseInt(this[_0x3628("0x32d")]);
      }
    }), _0xf04759[_0x3628("0x4a")](function () {
      if (_0x3628("0x441") === "pKLMd") {
        i_bAExa["style"][_0x3628("0x3b5")] = _0x3628("0x8c"), i_RmeQe["style"]["display"] = "none", i_KYfkn[_0x3628("0x3e")][_0x3628("0x3b5")] = "none", i_QPWBt[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e");
      } else {
        var _0x18b273 = $(this)["prev"]()["attr"](_0x3628("0x32d"));
        $(this)[_0x3628("0x24b")](_0x18b273);
      }
    });
  });
}
function i_mBphdl(_0x45c0a5) {
  if (i_njFT_l) {
    var _0x294b67 = 0x3e8 / 0x3c * (i_dPmwul = _0x45c0a5) / 0x3e8;
    i_iDQBme && (cyc_queryID("ctlRepValMax")[_0x3628("0x27e")] = "0" + Math[_0x3628("0x560")](_0x294b67 / 0xe10) + ":" + Math[_0x3628("0x560")](_0x294b67 / 0x3c) % 0x3c + ":" + Math["floor"](_0x294b67 % 0x3c), cyc_queryID(_0x3628("0x4c9"))[_0x3628("0x1d1")] = _0x45c0a5), 0xa < Math[_0x3628("0x560")](_0x294b67 / 0xe10) && i_iZhRvt(_0x3628("0x102"));
  }
}
function i_jYGKpl(_0x914f68) {
  if (i_njFT_l) {
    if ("Nbfnf" !== _0x3628("0x32c")) {
      var _0x1b5e7e = 0x3e8 / 0x3c * (i_sQwkfl = _0x914f68) / 0x3e8;
      i_dPmwul < i_sQwkfl + 0x3c && i_EtKHbl && i_iZhRvt(_0x3628("0x506")), cyc_queryID(_0x3628("0x4c9"))[_0x3628("0x32d")] = Math[_0x3628("0x560")](i_sQwkfl / i_dPmwul * i_dPmwul), cyc_queryID("ctlRepVal")["innerText"] = "0" + Math[_0x3628("0x560")](_0x1b5e7e / 0xe10) + ":" + Math[_0x3628("0x560")](_0x1b5e7e / 0x3c) % 0x3c + ":" + Math[_0x3628("0x560")](_0x1b5e7e % 0x3c);
    } else {
      i_rrYppt(i_YYZSe[_0x3628("0x289")] + _0x3628("0x3d7") + i_YYZSe[_0x3628("0x187")]), i_sxfci_(_0x3628("0x136"))["click"]();
    }
  }
}
function i_TEpTml() {
  i_njFT_l = !0x0;
}
var i_jejYK = -0xf4240;
function i_wSYXgl() {
  0x1 == i_iDpHde ? i_Kbzklo == i_emcHro ? performance[_0x3628("0x30b")]() < 0xea60 ? i_iZhRvt("录像长度小于一分钟，不允许保存") : performance[_0x3628("0x30b")]() - i_jejYK < 0xea60 ? i_iZhRvt(_0x3628("0x501")) : (i_jejYK = performance[_0x3628("0x30b")](), i_hnXPpo(_0x3628("0x269"), "")) : i_iZhRvt("目前只允许房主保存房间录像") : i_iZhRvt("只有联机模式才允许保存录像");
}
var i_fJyphl = null,
  i_EtKHbl = !0x1;
function i_icQdyl() {
  $(_0x3628("0xdc"))[_0x3628("0x1a2")]("录像回放"), cyc_queryID(_0x3628("0x547"))["style"][_0x3628("0x3b5")] = _0x3628("0x8c"), cyc_queryID("pcmsg")["style"]["top"] = _0x3628("0x23a"), cyc_queryID("pcmsg")["style"][_0x3628("0x9")] = "calc(28%\x20-\x2048px)", document[_0x3628("0x566")](_0x3628("0x4ad"))[_0x3628("0x64")](function (_0xe44745) {
    if (_0x3628("0x137") === _0x3628("0x437")) {
      i_WmyDOu[_0x3628("0x438")] = JSON[_0x3628("0x1aa")](JSON[_0x3628("0x2ae")](i_NSYbNu)), i_MzyCVu(), i_xmzByr(_0x3628("0x42b"));
    } else {
      _0xe44745[_0x3628("0x3e")][_0x3628("0x3b5")] = "none";
    }
  }), cyc_queryID(_0x3628("0x3cf"))["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), cyc_queryID(_0x3628("0x211"))[_0x3628("0x27e")] = _0x3628("0x54a"), cyc_queryID(_0x3628("0xb2"))[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x11c"), cyc_queryID(_0x3628("0x4c3"))["style"][_0x3628("0x3b5")] = _0x3628("0x47e"), i_Bsazvl();
}
function i_Newnwl(_0x1fc90f, _0x24d09e) {
  SendBigCommand(i_cZwcUl["gn"], _0x1fc90f, _0x24d09e);
}
function i_SWyJkl(_0x3f97f2) {
  SendBigCommand(i_cZwcUl["hn"], _0x3f97f2, 0x0);
}
function i_heWQTl(_0x1b34f3) {
  i_EtKHbl && SendBigCommand(i_cZwcUl["bn"], _0x1b34f3);
}
function i_nZDNxl() {
  i_EtKHbl = !0x0, gid = parseInt(i_nbDGge) + 0xf4240, i_njFT_l = !0x0, $[_0x3628("0x463")](_0x3628("0x469") + i_nbDGge, function (_0x543e25) {
    i_iZhRvt(_0x3628("0x26e") + (i_fJyphl = _0x543e25)[_0x3628("0x77")]), i_mBphdl(_0x543e25["ReplayLen"]);
  }), i_iDQBme && i_icQdyl();
}
function i_YijrMl() {
  gid = i_fJyphl[_0x3628("0x260")], i_Newnwl(i_fJyphl[_0x3628("0x29")], i_fJyphl["GameID"]);
}
function i_FHkTPl(_0x136705) {}
function i_HBySEl() {
  cyc_queryID("linkmsg")[_0x3628("0x314")](_0x3628("0x4ab"), function (_0x12b993) {
    var _0x59a8a6 = _0x12b993[_0x3628("0x2f9")];
    if (0x1 < _0x59a8a6[_0x3628("0x4a8")][_0x3628("0x2f3")](_0x3628("0x280"))) {
      var _0x34271f = _0x59a8a6[_0x3628("0x13")][_0x3628("0x27e")][_0x3628("0x26a")](0x0, _0x59a8a6[_0x3628("0x13")][_0x3628("0x27e")][_0x3628("0x2f3")](":\x20")),
        _0x4602a8 = _0x59a8a6[_0x3628("0x456")][_0x3628("0x27e")]["substr"](_0x59a8a6[_0x3628("0x456")][_0x3628("0x27e")][_0x3628("0x2f3")](":\x20") + 0x2);
      _0x4602a8 = _0x4602a8["substr"](0x0, _0x4602a8["length"] - 0x1), $["post"]("/sayban", {
        who: _0x34271f,
        where: _0x3628("0x197") + gid + "_" + i_ZmCwpe,
        say: _0x4602a8 + ""
      }, function (_0x4b9db9) {
        "ok" == _0x4b9db9[_0x3628("0x446")] ? i_hnXPpo("message", _0x34271f + _0x3628("0x50e") + (i_emcHro + 0x1) + _0x3628("0x18e")) : i_iZhRvt("禁言失败");
      });
    }
  });
}
var i_iZEzAl = {
  yn: !0x1
};
function i_CzBHSl() {
  i_iZEzAl["yn"] = !0x0;
}
function i_HxJwCl() {
  if (i_snTDve) {
    if ("sPeaD" === "QdZhK") {
      var _0x27f3fe;
      try {
        _0x27f3fe = Function(_0x3628("0x44") + _0x3628("0x470") + ");")();
      } catch (_0x2db5ee) {
        _0x27f3fe = window;
      }
      return _0x27f3fe;
    } else {
      var _0x2919c4 = i_tdBda_(_0x3628("0x248"));
      i_DYNBTe[_0x3628("0x3ab")]({
        act: _0x3628("0x4ed"),
        1: gid,
        2: -0x1,
        5: i_NXdZKf[_0x3628("0x6c")],
        3: i_rNzMye,
        4: i_zrnPwe,
        6: i_trmnke,
        16: i_iDpHde,
        17: i_ZmCwpe,
        18: i_iDQBme,
        19: _0x2919c4,
        sab: i_hFSCU,
        sabi: i_kAGRD
      });
    }
  } else setTimeout(i_HxJwCl, 0xbb8);
}
function i_DPmFKl(_0x3f8808) {
  if (0x4 != _0x3f8808[_0x3628("0x2e7")][_0x3628("0x22")]) {
    if (_0x3628("0x4ed") == _0x3f8808[_0x3628("0x2e7")]["act"]) i_HxJwCl();else if ("go" == _0x3f8808[_0x3628("0x2e7")][_0x3628("0x162")]) {
      if (i_cNjsB_ = _0x3f8808["data"][i_esjAjl], i_ZHidMe = _0x3f8808[_0x3628("0x2e7")][i_XcWfHl], i_thkew_[_0x3628("0x191")] = _0x3f8808["data"][i_CSsKql], i_thkew_[_0x3628("0x9")] = _0x3f8808[_0x3628("0x2e7")][i_WKTXVl], i_thkew_["width"] < i_thkew_[_0x3628("0x9")]) {
        var _0x1f92d0 = i_thkew_[_0x3628("0x191")];
        i_thkew_[_0x3628("0x191")] = i_thkew_[_0x3628("0x9")], i_thkew_["height"] = _0x1f92d0, i_TBJJz_ = !0x0;
      }
      i_kRDkS_ = i_thkew_[_0x3628("0x191")], i_kPQcC_ = i_thkew_[_0x3628("0x9")], i_iDQBme && (i_thkew_["width"] *= i_yhDYO_, i_thkew_[_0x3628("0x9")] *= i_yhDYO_), ctlEnable = _0x3f8808["data"][i_FXhQYl], i_ecCpE_ = i_CrAfx_(), i_dHGrA_ = i_chNJM_, i_kNJaU_(), i_CzBHSl(), i_iDQBme ? (i_DYNBTe["onmessage"] = i_hDFGNl, setTimeout(i_wbiGr, 0xbb8), i_Gphxdn(0x64)) : (i_DYNBTe[_0x3628("0x286")] = i_hDFGNl, i_pPaJir(0x64)), 0x1 == i_iDpHde && i_ckezgo();
    } else _0x3628("0x2fe") == _0x3f8808["data"][_0x3628("0x162")] ? -0x1 == _0x3f8808[_0x3628("0x2e7")][_0x3628("0xdd")] ? i_sxfci_(_0x3628("0x98"))["src"] = "./img/loadfail.png" : -0x2 == _0x3f8808["data"][_0x3628("0xdd")] || (i_iDQBme ? i_Gphxdn(_0x3f8808["data"][_0x3628("0xdd")]) : i_pPaJir(_0x3f8808["data"]["pos"])) : i_zXJHnu(i_cZwcUl["vn"], 0xff & i_iChyAi, i_iChyAi / 0x100);
    i_EtKHbl && i_YijrMl();
  } else i_KKCaXl(_0x3f8808[_0x3628("0x2e7")]);
}
i_iZEzAl["s"] = function () {
  i_bAiBbu(0x0);
}, i_iZEzAl["l"] = function () {
  i_bAiBbu(0x1);
}, i_iZEzAl["wn"] = function (_0x62f39e) {
  i_bkNDhu(_0x62f39e);
}, i_iZEzAl["kn"] = function (_0x1e5203) {
  i_piEjku(_0x1e5203);
};
var i_WNFHOl = 0x12345;
function i_KKCaXl(_0x184d40) {
  var _0xf1e26d = _0x184d40[0x0];
  if (_0xf1e26d == i_cZwcUl["Tn"]) i_TBswui();else if (_0xf1e26d == i_cZwcUl["xn"]) {
    var _0xf5bb29 = _0x184d40[0x2];
    if (0x1 == _0x184d40[0x1]) {
      var _0x4fcf94 = i_wccDd_(),
        _0x575595 = i_PZAFm_(_0x4fcf94),
        _0x2b8f18 = new XMLHttpRequest();
      _0x2b8f18[_0x3628("0x3d8")](_0x3628("0x4b"), _0x3628("0x2a7") + _0xf5bb29 + _0x3628("0x14f") + gid, !0x0), _0x2b8f18[_0x3628("0x59")](_0x3628("0x1de"), _0x3628("0x42")), _0x2b8f18[_0x3628("0x278")] = function () {
        if (_0x3628("0x1ab") !== "ycOHH") {
          if (_0x2b8f18["readyState"] == XMLHttpRequest["DONE"] && 0xc8 == _0x2b8f18[_0x3628("0x446")]) {
            if (_0x3628("0x3e1") === _0x3628("0xf")) {
              if (0x4 != i_PhEme[_0x3628("0x2e7")][_0x3628("0x22")]) {
                if (_0x3628("0x4ed") == i_PhEme[_0x3628("0x2e7")][_0x3628("0x162")]) i_HxJwCl();else if ("go" == i_PhEme[_0x3628("0x2e7")][_0x3628("0x162")]) {
                  if (i_cNjsB_ = i_PhEme[_0x3628("0x2e7")][i_esjAjl], i_ZHidMe = i_PhEme[_0x3628("0x2e7")][i_XcWfHl], i_thkew_[_0x3628("0x191")] = i_PhEme["data"][i_CSsKql], i_thkew_[_0x3628("0x9")] = i_PhEme[_0x3628("0x2e7")][i_WKTXVl], i_thkew_[_0x3628("0x191")] < i_thkew_["height"]) {
                    var _0x1ce66c = i_thkew_["width"];
                    i_thkew_[_0x3628("0x191")] = i_thkew_["height"], i_thkew_[_0x3628("0x9")] = _0x1ce66c, i_TBJJz_ = !0x0;
                  }
                  i_kRDkS_ = i_thkew_[_0x3628("0x191")], i_kPQcC_ = i_thkew_[_0x3628("0x9")], i_iDQBme && (i_thkew_[_0x3628("0x191")] *= i_yhDYO_, i_thkew_[_0x3628("0x9")] *= i_yhDYO_), ctlEnable = i_PhEme[_0x3628("0x2e7")][i_FXhQYl], i_ecCpE_ = i_CrAfx_(), i_dHGrA_ = i_chNJM_, i_kNJaU_(), i_CzBHSl(), i_iDQBme ? (i_DYNBTe[_0x3628("0x286")] = i_hDFGNl, setTimeout(i_wbiGr, 0xbb8), i_Gphxdn(0x64)) : (i_DYNBTe["onmessage"] = i_hDFGNl, i_pPaJir(0x64)), 0x1 == i_iDpHde && i_ckezgo();
                } else _0x3628("0x2fe") == i_PhEme["data"][_0x3628("0x162")] ? -0x1 == i_PhEme[_0x3628("0x2e7")][_0x3628("0xdd")] ? i_sxfci_("loadingimg")[_0x3628("0x1c5")] = _0x3628("0x2b0") : -0x2 == i_PhEme[_0x3628("0x2e7")][_0x3628("0xdd")] || (i_iDQBme ? i_Gphxdn(i_PhEme[_0x3628("0x2e7")][_0x3628("0xdd")]) : i_pPaJir(i_PhEme[_0x3628("0x2e7")]["pos"])) : i_zXJHnu(i_cZwcUl["vn"], 0xff & i_iChyAi, i_iChyAi / 0x100);
                i_EtKHbl && i_YijrMl();
              } else i_KKCaXl(i_PhEme[_0x3628("0x2e7")]);
            } else {
              var _0x52f4e1 = new Date();
              switch (_0xf5bb29) {
                case 0x1:
                  i_kJKK$t[_0x3628("0x55a")] = 0x1, i_kJKK$t[_0x3628("0x1b6")] = _0x52f4e1["toLocaleString"](), i_cQmQBt["d1"] = 0x1, i_cQmQBt[_0x3628("0x1cb")] = _0x4fcf94;
                  break;
                case 0x2:
                  i_kJKK$t[_0x3628("0x3f8")] = 0x1, i_kJKK$t["st2"] = _0x52f4e1[_0x3628("0x377")](), i_cQmQBt["d2"] = 0x1, i_cQmQBt["d2i"] = _0x4fcf94;
                  break;
                case 0x3:
                  i_kJKK$t[_0x3628("0xaf")] = 0x1, i_kJKK$t["st3"] = _0x52f4e1[_0x3628("0x377")](), i_cQmQBt["d3"] = 0x1, i_cQmQBt[_0x3628("0x256")] = _0x4fcf94;
                  break;
                case 0x4:
                  i_kJKK$t[_0x3628("0x43e")] = 0x1, i_kJKK$t[_0x3628("0x4d0")] = _0x52f4e1[_0x3628("0x377")](), i_cQmQBt["d4"] = 0x1, i_cQmQBt[_0x3628("0x35")] = _0x4fcf94;
                  break;
                case 0x5:
                  i_kJKK$t["W"] = 0x1, i_kJKK$t["J"] = _0x52f4e1["toLocaleString"](), i_cQmQBt["Mn"] = 0x1, i_cQmQBt["Z"] = _0x4fcf94;
                  break;
                case 0x6:
                  i_kJKK$t["ee"] = 0x1, i_kJKK$t["ne"] = _0x52f4e1["toLocaleString"](), i_cQmQBt["Pn"] = 0x1, i_cQmQBt["te"] = _0x4fcf94;
                  break;
                case 0x7:
                  i_kJKK$t["ae"] = 0x1, i_kJKK$t["ie"] = _0x52f4e1[_0x3628("0x377")](), i_cQmQBt["En"] = 0x1, i_cQmQBt["oe"] = _0x4fcf94;
                  break;
                case 0x8:
                  i_kJKK$t["ce"] = 0x1, i_kJKK$t["re"] = _0x52f4e1[_0x3628("0x377")](), i_cQmQBt["An"] = 0x1, i_cQmQBt["le"] = _0x4fcf94;
              }
              i_TkwGLt(), i_iDQBme && i_smExYt(_0xf5bb29);
            }
          }
        } else {
          var _0x2bf022 = document["createElement"](_0x3628("0x30a"));
          _0x2bf022[_0x3628("0x107")](_0x3628("0x376"), i_NdQwe["LookerList"][i_Ttcit]), _0x2bf022[_0x3628("0x24d")] = _0x3628("0x101") + i_NdQwe["LookerNick"][i_Ttcit] + _0x3628("0xda"), _0x2bf022[_0x3628("0x4a8")] = "lookout", i_YDFdn[_0x3628("0xd6")](_0x2bf022);
        }
      }, _0x2b8f18[_0x3628("0x559")](_0x575595);
    }
  } else {
    if (_0xf1e26d == i_cZwcUl["Sn"]) {
      if (_0x3628("0x551") === _0x3628("0x2a2")) {
        if (0x2 == i_Jbaje[_0x3628("0x305")] && 0x1 == i_iDpHde) {
          var _0x5b6871 = i_sxfci_("usermenu");
          _0x5b6871[_0x3628("0x3e")]["opacity"] = 0x1, _0x5b6871[_0x3628("0x3e")][_0x3628("0x282")] = i_Jbaje["clientX"] - 0xf + "px", _0x5b6871[_0x3628("0x3e")][_0x3628("0x270")] = i_Jbaje["clientY"] - 0xf + "px", i_ieCCaa = _0x3628("0x562") == this["id"] ? "1" : _0x3628("0x90") == this["id"] ? "2" : _0x3628("0x525") == this["id"] ? "3" : "4", i_bTamia();
        }
      } else {
        _0xf5bb29 = _0x184d40[0x2];
        return void (i_iDQBme && i_MKbRHt(_0xf5bb29));
      }
    }
    _0xf1e26d == i_cZwcUl["Cn"] ? 0x1 == _0x184d40[0x1] ? i_ePsnwo() : i_sCnmko() : _0xf1e26d == i_cZwcUl["Kn"] ? gid < 0x1869f && i_MNhEfn() : _0xf1e26d == i_cZwcUl["On"] && i_sEwEen(_0x3628("0x21"));
  }
}
var i_jpiRRl = 0x64,
  i_Hkrhzl = 0x0;
function i_iWZGIl(_0x2f9322, _0x38a678) {
  i_DQjNRf["push"](_0x2f9322), null != i_MjAfV && (i_MjAfV[i_NxPXW] = i_DQjNRf["length"]);
}
function i_hDFGNl(_0x132ddb) {
  if (0x4 != _0x132ddb["data"][_0x3628("0x22")]) {
    if (0x18 != _0x132ddb[_0x3628("0x2e7")][_0x3628("0x22")]) {
      if (0x1 == i_iDpHde) {
        if ("vPAKt" === _0x3628("0x225")) {
          if (_0x132ddb[_0x3628("0x2e7")][_0x3628("0x22")] < 0x800) {
            if ("XrraI" === _0x3628("0x3ce")) {
              if (i_KatGXo(_0x132ddb[_0x3628("0x2e7")]), 0x10 == _0x132ddb[_0x3628("0x2e7")]["byteLength"]) {
                var _0x4f95cd = new Uint32Array(_0x132ddb["data"]);
                _0x4f95cd[0x0] % 0x3c == 0x0 && i_hErJXt(_0x4f95cd[0x0]);
              }
              return;
            } else {
              gid = i_fJyphl["GameID"], i_Newnwl(i_fJyphl[_0x3628("0x29")], i_fJyphl[_0x3628("0x260")]);
            }
          }
        } else {
          i_hnXPpo(_0x3628("0x20"), i_BDeZzr + "");
        }
      } else i_hErJXt(i_MChKiu);
      if (i_MChKiu++, i_WEwySe++, null != i_kAGRD) var _0x59e36f = i_kAGRD;else _0x59e36f = _0x132ddb[_0x3628("0x2e7")];
      if (i_PAkc_f && i_bhkMMf && 0x5 < i_MChKiu && !i_GbdzSf) {
        var _0x2e8260 = new Int32Array(_0x59e36f, 0x0, 0x400);
        if (i_cNjsB_ = _0x2e8260[0x0], null != i_pwwhGf) {
          for (var _0x2cd6fd = 0x0; _0x2cd6fd < i_cNjsB_; _0x2cd6fd++) i_SEwKkf <= i_bjXRwf && (i_bjXRwf = 0x0), i_asXxpf[i_bjXRwf] = _0x2e8260[_0x2cd6fd + 0x1] / 6553.5, i_bjXRwf++;
          Atomics[_0x3628("0x405")](i_XFNiFf, 0x0, i_cNjsB_), i_MjAfV[i_NxPXW] = i_XFNiFf[0x1] / 0x400;
        } else {
          for (_0x2cd6fd = 0x0; _0x2cd6fd < i_cNjsB_ && (0x19000 <= i_bjXRwf && (i_bjXRwf = 0x0), !(i_akCNBf < i_iWFTUf - i_pTEbDf)); _0x2cd6fd++) i_daHxIf[i_bjXRwf] = _0x2e8260[_0x2cd6fd + 0x1] / 6553.5, i_bjXRwf++, i_iWFTUf++;
          null != i_MjAfV ? i_MjAfV[i_NxPXW] = (i_iWFTUf - i_pTEbDf) / 0x400 : i_akCNBf - 0x400 < i_iWFTUf - i_pTEbDf && i_MChKiu % 0x1e == 0x0 && i_zXJHnu(i_cZwcUl["Xn"], (i_iWFTUf - i_pTEbDf) / 0x400, 0x0);
        }
      }
      if (0x1000 < _0x59e36f["byteLength"]) i_bxjR$_(new Uint16Array(_0x59e36f, 0x1000)), i_BSKman(new Int32Array(_0x59e36f, 0x1000 + i_kRDkS_ * i_kPQcC_ * 0x2));
    } else {
      i_hErJXt(new Uint32Array(_0x132ddb["data"])[0x0]);
    }
  } else i_KKCaXl(_0x132ddb[_0x3628("0x2e7")]);
}
var i_cZwcUl = {
    Rn: 0x1,
    zn: 0x2,
    In: 0x3,
    Nn: 0x4,
    Un: 0x5,
    Dn: 0x6,
    xn: 0x7,
    Sn: 0x8,
    $n: 0x9,
    Bn: 0xa,
    Gn: 0xb,
    Fn: 0xc,
    Cn: 0xd,
    Ln: 0xe,
    qn: 0xf,
    Kn: 0x10,
    Vn: 0x11,
    vn: 0x12,
    jn: 0x13,
    On: 0x14,
    Yn: 0x15,
    Hn: 0x16,
    gn: 0x17,
    hn: 0x18,
    bn: 0x19,
    Tn: 0x1a,
    Xn: 0x1b,
    dn: 0x1c,
    Wn: 0x1d,
    Jn: 0x1e
  },
  i_jaMBDl = 0x1,
  i_SGMf$l = 0x2,
  i_ZZYYBl = 0x3,
  i_GKesGl = 0x4,
  i_ikkmFl = 0x5,
  i_FGabLl = 0x6,
  i_CSsKql = 0xb,
  i_WKTXVl = 0xc,
  i_esjAjl = 0xd,
  i_FXhQYl = 0xe,
  i_XcWfHl = 0xf,
  i_CJiJWl = 0x10,
  i_kjidJl = 0x11,
  i_aZrxQl = 0x12;
ik_looker = 0x13;
var i_FGeWZl = new Uint8Array(0x4),
  i_wasReu = new Uint32Array(0x3);
function i_zXJHnu(_0x347899, _0x333e65, _0xde7535) {
  null != i_DYNBTe && (i_FGeWZl[0x0] = _0x347899, i_FGeWZl[0x1] = _0x333e65, i_FGeWZl[0x2] = _0xde7535, i_DYNBTe[_0x3628("0x3ab")](i_FGeWZl));
}
function i_ecwRtu(_0x23b120, _0x449c0d, _0x231932) {
  null != i_DYNBTe && (i_wasReu[0x0] = _0x23b120, i_wasReu[0x1] = _0x449c0d, i_wasReu[0x2] = _0x231932, i_DYNBTe[_0x3628("0x3ab")](i_wasReu));
}
function i_aZwsau(_0x1e591f) {
  null != i_DYNBTe && i_DYNBTe[_0x3628("0x3ab")](_0x1e591f);
}
var i_MChKiu = 0x0,
  i_CCRpou = 0x0,
  i_hipxcu = 0x1,
  i_WYbZsu = 0x1,
  i_mnEKru = 0x0,
  i_WdkRlu = 0x0;
function i_aCbGuu(_0xbaa3c3, _0x557895) {
  i_aGfyVi(i_WdkRlu = i_mDiGLi(i_WdkRlu, _0xbaa3c3, _0x557895));
}
function i_jPzhfu(_0x45aa6b, _0x469d0a) {
  0x10 * _0x45aa6b + _0x469d0a == i_mnEKru && _0x45aa6b != i_GHKWya["$e"] || 0x0 < i_HaCAva["length"] && _0x45aa6b != i_GHKWya["$e"] || (_0x45aa6b == i_GHKWya["Fe"] && (_0x469d0a & i_ipNXXi ? i_iYai_a = 0x1 : _0x469d0a & i_xcfpRi && (i_iYai_a = 0x0)), i_mnEKru = 0x10 * _0x45aa6b + _0x469d0a, 0x1 == i_iDpHde ? i_fJfXvo(_0x45aa6b, _0x469d0a) : (i_GHKWya["$e"], i_kcihCi(_0x45aa6b, _0x469d0a)), i_yYwPle["p"]++);
}
var i_yDbf_u = 0x1;
function i_xCyTvu() {
  0x0 != i_WmyDOu[_0x3628("0x24f")] && i_yDbf_u < 0x3 && (i_iDQBme || (i_yDbf_u++, i_zXJHnu(i_cZwcUl["In"], i_yDbf_u, 0x0)));
}
function i_ARBMdu() {
  i_zXJHnu(i_cZwcUl["In"], 0x1, 0x0);
}
function i_XFSipu(_0x598291) {
  i_HSeGX_ = 0x2 * (0x9 - _0x598291), i_zXJHnu(i_cZwcUl["$n"], i_HSeGX_, 0x0);
}
function i_cxmKmu() {}
function i_nYekgu(_0x5a7cfa, _0x5a012a) {
  i_ecwRtu(i_cZwcUl["Cn"], _0x5a7cfa, _0x5a012a);
}
function i_bkNDhu(_0x564b49) {
  i_hipxcu = _0x564b49, i_zXJHnu(i_cZwcUl["zn"], _0x564b49, 0x0);
}
function i_bAiBbu(_0x1b9288) {
  i_WYbZsu = _0x1b9288, i_CCRpou = 0x0, i_zXJHnu(i_cZwcUl["Dn"], _0x1b9288, 0x0);
}
function i_WmWryu(_0x15a1c5) {
  i_zXJHnu(i_cZwcUl["xn"], _0x15a1c5, 0x0);
}
function i_ZjZkwu(_0x24c0cc) {
  i_zXJHnu(i_cZwcUl["Sn"], _0x24c0cc, 0x0);
}
function i_piEjku(_0x465242) {
  0x0 == i_ZHidMe ? setTimeout(i_piEjku, 0x7d0, _0x465242) : i_zXJHnu(i_cZwcUl["Rn"], _0x465242, 0x0);
}
function i_ndBeTu(_0xcd7d68) {
  i_zXJHnu(i_cZwcUl["Bn"], _0xcd7d68, 0x0);
}
function i_Pxzfxu(_0xd9a80f) {
  i_zXJHnu(i_cZwcUl["Fn"], 0xff & _0xd9a80f, _0xd9a80f / 0x100);
}
function i_ZQAWMu(_0x3d240b) {
  i_zXJHnu(i_cZwcUl["Ln"], _0x3d240b, 0x0);
}
function i_jnKJPu() {
  i_zXJHnu(i_cZwcUl["Vn"], 0x0, 0x0);
}
function i_ktCsEu(_0x183a97) {
  i_zXJHnu(i_cZwcUl["jn"], _0x183a97, 0x0);
}
function i_XwGBAu() {
  i_zXJHnu(i_cZwcUl["Hn"], 0x0, 0x0);
}
var i_GCasSu = {};
function i_TrjcCu(_0x2674b6, _0x4afc0d, _0x2156ec) {
  if (null == i_GCasSu[_0x4afc0d]) {
    var _0x54f2c7 = new XMLHttpRequest();
    _0x54f2c7[_0x3628("0x3d8")](_0x3628("0x13f"), _0x3628("0x12f") + _0x2674b6 + _0x3628("0x337") + _0x4afc0d, !0x0), _0x54f2c7[_0x3628("0x559")](), _0x54f2c7[_0x3628("0x278")] = function () {
      if (0x4 == _0x54f2c7[_0x3628("0xee")] && 0xc8 == _0x54f2c7[_0x3628("0x446")]) try {
        if ("LxNyf" !== _0x3628("0x3df")) {
          !0x0;
          var _0x1474cc = document[_0x3628("0x3fe")]("div");
          _0x1474cc[_0x3628("0x4a8")] = _0x3628("0x513"), _0x1474cc[_0x3628("0x107")]("name", _0x3628("0x166")), _0x1474cc[_0x3628("0x107")](_0x3628("0x376"), i_rNJia), _0x1474cc[_0x3628("0x24d")] = i_TtRQe[i_rNJia]["id"]["substr"](0x0, i_TtRQe[i_rNJia]["id"][_0x3628("0x2f3")]("(Ven")), "" == _0x1474cc[_0x3628("0x24d")] && (_0x1474cc[_0x3628("0x24d")] = i_TtRQe[i_rNJia]["id"][_0x3628("0x26a")](0x0, i_TtRQe[i_rNJia]["id"][_0x3628("0x2f3")]("("))), "" == _0x1474cc[_0x3628("0x24d")] && (_0x1474cc[_0x3628("0x24d")] = i_TtRQe[i_rNJia]["id"]), i_sxfci_(_0x3628("0x5d"))[_0x3628("0xd6")](_0x1474cc), i_fQZDn++, -0x1 != i_HjtSei && i_EWZBt != i_TtRQe[i_rNJia]["id"] || (i_HjtSei = i_rNJia), v_allGamePadName[i_rNJia] = {}, v_allGamePadName[i_rNJia]["id"] = i_TtRQe[i_rNJia]["id"], v_allGamePadName[i_rNJia][_0x3628("0x63")] = i_rNJia;
        } else {
          var _0x30c779 = JSON[_0x3628("0x1aa")](_0x54f2c7[_0x3628("0x3f")]);
          _0x3628("0x68") == _0x30c779[_0x3628("0x446")] && (i_GCasSu[_0x4afc0d] = _0x30c779[_0x3628("0x2e7")], _0x2156ec(_0x30c779[_0x3628("0x2e7")]));
        }
      } catch (_0x1c0b5b) {}
    };
  } else _0x2156ec(i_GCasSu[_0x4afc0d]);
}
var i_XejQKu = !0x1,
  i_WmyDOu = {
    gJoyType: 0x1,
    gASuper: !0x1,
    gBSuper: !0x1,
    gCSuper: !0x1,
    gDSuper: !0x1,
    gESuper: !0x1,
    gFSuper: !0x1,
    gCtlZD: !0x1,
    gCtlSound: !0x1,
    gCtlOpt: 0x32,
    gScreen: 0x5,
    gSuperSpeed: 0x5,
    gSkipFrame: 0x1,
    gExtX1: !0x1,
    gExtX2: !0x1,
    gExtX3: !0x1,
    gExtX4: !0x1,
    gExtX5: !0x1,
    gExtX6: !0x1,
    gExtX1Key: 0x3,
    gExtX2Key: 0x0,
    gExtX3Key: 0x0,
    gExtX4Key: 0x0,
    gExtX5Key: 0x0,
    gExtX6Key: 0x0,
    gGesSpeed: 0x5,
    gGes1: _0x3628("0x2b6"),
    gGes2: _0x3628("0x245"),
    gGes3: "",
    gGes4: "",
    gGes5: "",
    gGes6: "",
    gGes7: "",
    gGes8: "",
    PCSet: {
      KeyUp: "KeyW",
      KeyDown: _0x3628("0x200"),
      KeyLeft: "KeyA",
      KeyRight: _0x3628("0x6e"),
      KeyFire1: _0x3628("0x1fa"),
      KeyFire2: _0x3628("0x4ca"),
      KeyFire3: _0x3628("0xa4"),
      KeyFire4: _0x3628("0x1cf"),
      KeyFire5: "KeyO",
      KeyFire6: "KeyP",
      KeyStart: _0x3628("0x180"),
      KeyCoin: "Digit5",
      KeyX1: _0x3628("0x232"),
      KeyX2: _0x3628("0x466"),
      KeyX3: "KeyU",
      KeyX4: _0x3628("0x1bc"),
      KeyX5: _0x3628("0x2e3"),
      KeyX6: _0x3628("0x51e"),
      KeyS1: _0x3628("0x52a"),
      KeyS2: _0x3628("0x2c8"),
      KeyS3: _0x3628("0xf2"),
      KeyS4: "KeyM"
    },
    GPSetx: {
      KeyJoy: 0x1,
      KeyUp: 0x1,
      KeyDown: 0x1,
      KeyLeft: 0x0,
      KeyRight: 0x0,
      KeyFire1: 0x0,
      KeyFire2: 0x1,
      KeyFire3: 0x2,
      KeyFire4: 0x3,
      KeyFire5: 0x6,
      KeyFire6: 0x7,
      KeyStart: 0x5,
      KeyCoin: 0x4,
      KeyX1: -0x1,
      KeyX2: -0x1,
      KeyX3: -0x1,
      KeyX4: -0x1,
      KeyX5: -0x1,
      KeyX6: -0x1,
      KeyS1: -0x1,
      KeyS2: -0x1,
      KeyS3: -0x1,
      KeyS4: -0x1,
      gKeyRev: !0x1,
      gKeyRevLR: !0x1
    },
    sHQX: !0x0,
    gSoundVol: 0x50,
    lockScr: !0x1,
    gDisHoldLR: !0x1,
    gJoyLagFix: 0x4,
    gScrType: 0x1,
    Qn: !0x1,
    Mobset: null,
    MobBase: {
      Lock43: !0x1,
      ScrStyle: !0x1,
      Width: 0x64,
      Height: 0x64,
      SimpleMode: !0x1,
      AllHide: !0x1
    }
  },
  i_jwnmXu = {
    KeyUp: "KeyW",
    KeyDown: _0x3628("0x200"),
    KeyLeft: _0x3628("0x413"),
    KeyRight: _0x3628("0x6e"),
    KeyFire1: _0x3628("0x1fa"),
    KeyFire2: _0x3628("0x4ca"),
    KeyFire3: _0x3628("0xa4"),
    KeyFire4: "KeyI",
    KeyFire5: "KeyO",
    KeyFire6: _0x3628("0x238"),
    KeyStart: _0x3628("0x180"),
    KeyCoin: _0x3628("0x4f8"),
    KeyX1: "KeyL",
    KeyX2: _0x3628("0x466"),
    KeyX3: _0x3628("0xa4"),
    KeyX4: "KeyF",
    KeyX5: _0x3628("0x2e3"),
    KeyX6: _0x3628("0x51e"),
    KeyS1: _0x3628("0x52a"),
    KeyS2: _0x3628("0x2c8"),
    KeyS3: _0x3628("0xf2"),
    KeyS4: _0x3628("0x31c")
  },
  i_mACQRu = {
    KeyJoy: -0x1,
    KeyUp: -0x1,
    KeyDown: -0x1,
    KeyLeft: -0x1,
    KeyRight: -0x1,
    KeyFire1: -0x1,
    KeyFire2: -0x1,
    KeyFire3: -0x1,
    KeyFire4: -0x1,
    KeyFire5: -0x1,
    KeyFire6: -0x1,
    KeyStart: -0x1,
    KeyCoin: -0x1,
    KeyX1: -0x1,
    KeyX2: -0x1,
    KeyX3: -0x1,
    KeyX4: -0x1,
    KeyX5: -0x1,
    KeyX6: -0x1,
    KeyS1: -0x1,
    KeyS2: -0x1,
    KeyS3: -0x1,
    KeyS4: -0x1,
    gKeyRev: !0x1,
    gKeyRevLR: !0x1
  },
  i_TEaXzu = {
    A: 0x1,
    B: 0x2,
    C: 0x4,
    L: 0x8,
    E: 0x10,
    q: 0x20
  },
  i_wtWEIu = {
    Lock43: !0x1,
    ScrStyle: !0x1,
    Width: 0x64,
    Height: 0x64
  },
  i_NSYbNu = {
    GamePadType: 0x1,
    Pad: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    Select: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    Start: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    A: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    B: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    AA: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    BB: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    },
    AB: {
      X: 0x0,
      Y: 0x0,
      Alpha: 0.8,
      Size: 0x1
    }
  };
function i_JcAsUu() {
  i_WSEwg_(_0x3628("0x1ee") + gid, i_WmyDOu), i_iDQBme ? i_rrYppt("配置已保存在畅玩云服务器") : i_xmzByr("配置已保存在畅玩云服务器");
}
function i_xNJjDu() {
  0x8 < i_WmyDOu[_0x3628("0x7e")] && (i_WmyDOu[_0x3628("0x7e")] = 0x8), i_WmyDOu["gSuperSpeed"] <= 0x0 && (i_WmyDOu[_0x3628("0x7e")] = 0x1);
}
function i_AQsA$u() {
  i_xNJjDu(), i_iDQBme ? (i_QcDssf(), i_siAWrf()) : (i_HmQPYu(), i_MFnNHu(), i_TXHhWu()), i_XFSipu(i_WmyDOu[_0x3628("0x7e")]);
}
function i_NRRXBu() {
  null != i_WmyDOu[_0x3628("0x438")] && null != i_WmyDOu[_0x3628("0x438")] || (i_WmyDOu[_0x3628("0x438")] = JSON["parse"](JSON["stringify"](i_NSYbNu))), null != i_WmyDOu["MobBase"] && null != i_WmyDOu[_0x3628("0x485")] || (i_WmyDOu[_0x3628("0x485")] = JSON["parse"](JSON[_0x3628("0x2ae")](i_wtWEIu)));
}
function i_TAQtGu() {
  i_AQsA$u();
  var _0x37e704 = new XMLHttpRequest();
  _0x37e704[_0x3628("0x3d8")](_0x3628("0x13f"), "/loadset?type=fc&gid=" + gid, !0x0), _0x37e704[_0x3628("0x1c4")] = _0x3628("0x346"), _0x37e704["onreadystatechange"] = function () {
    if (_0x37e704["readyState"] == XMLHttpRequest[_0x3628("0x4e")] && 0xc8 == _0x37e704["status"]) {
      var _0x12709c = _0x37e704[_0x3628("0x3f")];
      null == _0x12709c["PCSet"] || null == _0x12709c[_0x3628("0xe1")] ? i_JcAsUu() : i_WmyDOu = _0x12709c, null == i_WmyDOu["gExtX5"] && (i_WmyDOu[_0x3628("0x53d")] = !0x1, i_WmyDOu["gExtX5Key"] = 0x0, i_WmyDOu[_0x3628("0x3b8")] = !0x1, i_WmyDOu["gExtX6Key"] = 0x0, i_WmyDOu[_0x3628("0x46f")][_0x3628("0x3d6")] = "NO", i_WmyDOu[_0x3628("0x46f")][_0x3628("0xb6")] = "NO", i_WmyDOu[_0x3628("0xe1")][_0x3628("0x3d6")] = -0x1, i_WmyDOu[_0x3628("0xe1")][_0x3628("0xb6")] = -0x1), null == i_WmyDOu["gExtX3"] && (i_WmyDOu["gExtX3"] = !0x1, i_WmyDOu[_0x3628("0x4b8")] = 0x0, i_WmyDOu[_0x3628("0x315")] = _0x3628("0x2b6"), i_WmyDOu[_0x3628("0xa1")] = _0x3628("0x245"), i_WmyDOu[_0x3628("0x363")] = "", i_WmyDOu[_0x3628("0x4d5")] = ""), null == i_WmyDOu[_0x3628("0xb")] && (i_WmyDOu["gExtX4"] = !0x1, i_WmyDOu["gExtX4Key"] = 0x0), null == i_WmyDOu[_0x3628("0x2ba")] && (i_WmyDOu["gGesSpeed"] = 0x4), null == i_WmyDOu[_0x3628("0x46f")] && (i_WmyDOu[_0x3628("0x46f")] = i_jwnmXu), null == i_WmyDOu[_0x3628("0xe1")] && (i_WmyDOu[_0x3628("0xe1")] = i_mACQRu), null == i_WmyDOu[_0x3628("0x16")] && (i_WmyDOu[_0x3628("0x16")] = !0x0), null == i_WmyDOu[_0x3628("0x40d")] && (i_WmyDOu[_0x3628("0x40d")] = 0x46), null == i_WmyDOu[_0x3628("0x1d8")] && (i_WmyDOu["lockScr"] = !0x1), null == i_WmyDOu["gDisHoldLR"] && (i_WmyDOu[_0x3628("0x192")] = !0x1), null == i_WmyDOu[_0x3628("0x2a3")] && (i_WmyDOu["gJoyLagFix"] = 0x4), null == i_WmyDOu["GPSetx"][_0x3628("0x2cd")] && (i_WmyDOu["GPSetx"]["gKeyRev"] = !0x1), null == i_WmyDOu[_0x3628("0xe1")][_0x3628("0x33f")] && (i_WmyDOu[_0x3628("0xe1")][_0x3628("0x33f")] = !0x1), i_NRRXBu(), i_AQsA$u(), i_XejQKu = !0x0;
    }
  }, _0x37e704[_0x3628("0x559")](), i_NRRXBu(), i_iDQBme ? i_axfcof() : i_TXHhWu();
}
function i_ZcmNFu(_0x319e05) {
  i_sxfci_(_0x3628("0x35b"))[_0x3628("0x206")] = _0x319e05 ? (i_WmyDOu[_0x3628("0x485")]["ScrStyle"] = !0x0, $(_0x3628("0x1ec"))["css"]("image-rendering", _0x3628("0x23f")), !0x0) : (i_WmyDOu[_0x3628("0x485")]["ScrStyle"] = !0x1, $("#whathis")[_0x3628("0x12")](_0x3628("0x32f"), "auto"), !0x1);
}
function i_yNXRLu() {
  var _0x5292fe = i_sxfci_("basicRangeWidth"),
    _0x58ffd3 = i_sxfci_(_0x3628("0x27b"));
  _0x5292fe[_0x3628("0x32d")] = parseInt(i_WmyDOu[_0x3628("0x485")][_0x3628("0x28c")]), _0x58ffd3["value"] = parseInt(i_WmyDOu[_0x3628("0x485")][_0x3628("0x26c")]), i_sxfci_("cb_lock43")["checked"] = i_WmyDOu[_0x3628("0x485")]["Lock43"], i_etMQjr();
}
function i_TQnEqu() {
  i_sxfci_(_0x3628("0x184"))[_0x3628("0x32d")] = parseInt(i_WmyDOu[_0x3628("0x7e")]), i_jCWyYr();
}
function i_MzyCVu() {
  for (var _0x2a0368 in i_WmyDOu[_0x3628("0x438")]) {
    for (var _0x23ac95 in i_WmyDOu[_0x3628("0x438")][_0x2a0368]) i_cffPrs[_0x2a0368][_0x23ac95] = i_WmyDOu["Mobset"][_0x2a0368][_0x23ac95];
    _0x3628("0x394") == _0x2a0368 && (i_esSxfs = !0x0);
  }
  i_Wairss = i_WmyDOu[_0x3628("0x485")][_0x3628("0x20c")], i_PkTzAs(), i_kCpK_s(), i_sFmzCs();
}
function i_NfcJju() {
  for (var _0x764250 in i_WmyDOu[_0x3628("0x438")]) for (var _0x2abe85 in i_WmyDOu["Mobset"][_0x764250]) i_WmyDOu[_0x3628("0x438")][_0x764250][_0x2abe85] = i_cffPrs[_0x764250][_0x2abe85];
  i_JcAsUu();
}
function i_HmQPYu() {
  i_MzyCVu();
}
function i_MFnNHu() {}
function i_TXHhWu() {
  i_ZcmNFu(i_WmyDOu[_0x3628("0x485")][_0x3628("0x15b")]), i_yNXRLu(), i_TQnEqu();
}
function i_GezfJu(_0x2d1b5b) {
  _0x2d1b5b ? (i_WmyDOu["gScrType"] = 0x1, $(_0x3628("0x1ec"))[_0x3628("0x12")]("image-rendering", "pixelated")) : (i_WmyDOu[_0x3628("0x1b5")] = 0x0, $(_0x3628("0x1ec"))[_0x3628("0x12")](_0x3628("0x32f"), _0x3628("0x1b")));
}
function i_PafEQu(_0x3958d3) {
  i_QCDwD_(i_WmyDOu[_0x3628("0x16")] = _0x3958d3);
}
function i_JzBSZu(_0x4c4087) {
  i_TfQXnf(i_WmyDOu[_0x3628("0x1d8")]);
}
function i_sJXWef(_0x568d2a) {
  void 0x0 === _0x568d2a && (_0x568d2a = !0x1), i_WmyDOu["Qn"] = (_0x568d2a ? i_sxfci_(_0x3628("0x390"))["classList"][_0x3628("0x405")](_0x3628("0x2f")) : i_sxfci_(_0x3628("0x390"))[_0x3628("0xea")][_0x3628("0x47a")](_0x3628("0x2f")), _0x568d2a);
}
function i_TfQXnf(_0xd3323d) {
  if (i_WmyDOu[_0x3628("0x1d8")] = _0xd3323d, i_WmyDOu[_0x3628("0x1d8")]) {
    var _0x4d81a6 = i_sxfci_(_0x3628("0x56e")),
      _0x487312 = i_sxfci_(_0x3628("0x390")),
      _0x3412bf = i_sxfci_(_0x3628("0x552")),
      _0x44cea7 = _0x4d81a6[_0x3628("0xb3")],
      _0x5cb68a = _0x4d81a6["clientHeight"];
    if (0x0 < _0x487312[_0x3628("0x4a8")][_0x3628("0x2f3")]("win")) return void setTimeout(function () {
      _0x44cea7 = _0x487312[_0x3628("0xb3")], _0x5cb68a = _0x487312[_0x3628("0x188")];
      var _0x75794b = _0x44cea7 / 0x4,
        _0x57d558 = _0x5cb68a / 0x3;
      if (_0x57d558 < _0x75794b) {
        _0x75794b = _0x57d558, _0x3412bf[_0x3628("0x3e")]["width"] = 0x4 * _0x75794b + "px", _0x3412bf[_0x3628("0x3e")]["height"] = 0x3 * _0x75794b + "px";
        var _0x4951fc = (_0x44cea7 - 0x4 * _0x75794b) / 0x2;
        _0x3412bf["style"][_0x3628("0x40f")] = _0x4951fc + "px", _0x3412bf[_0x3628("0x3e")][_0x3628("0x4b3")] = _0x4951fc + "px";
      } else {
        _0x3412bf[_0x3628("0x3e")][_0x3628("0x191")] = 0x4 * _0x75794b + "px", _0x3412bf[_0x3628("0x3e")][_0x3628("0x9")] = 0x3 * _0x75794b + "px", _0x3412bf[_0x3628("0x3e")][_0x3628("0x292")] = _0x3628("0x44a");
        _0x4951fc = (_0x5cb68a - 0x3 * _0x75794b) / 0x2;
        _0x3412bf[_0x3628("0x3e")][_0x3628("0x436")] = _0x4951fc + "px", _0x3412bf["style"][_0x3628("0x290")] = _0x4951fc + "px";
      }
    }, 0x1f4);
    _0x3412bf[_0x3628("0x3e")][_0x3628("0x292")] = "", _0x3412bf[_0x3628("0x3e")][_0x3628("0x191")] = _0x3628("0x1a8"), _0x3412bf["style"][_0x3628("0x9")] = _0x3628("0x1a8");
    var _0x15bd15 = _0x4d81a6["clientWidth"] / 0x4,
      _0x14eba7 = (_0x4d81a6[_0x3628("0x188")] - 0x40) / 0x3;
    if (_0x14eba7 < _0x15bd15) _0x15bd15 = _0x14eba7, _0x487312["style"][_0x3628("0x191")] = 0x4 * _0x15bd15 + "px", _0x487312[_0x3628("0x3e")][_0x3628("0x9")] = 0x3 * _0x15bd15 + "px", _0x487312[_0x3628("0x3e")][_0x3628("0x292")] = "auto";else {
      _0x487312[_0x3628("0x3e")]["width"] = 0x4 * _0x15bd15 + "px", _0x487312[_0x3628("0x3e")][_0x3628("0x9")] = 0x3 * _0x15bd15 + "px";
      var _0x196ea5 = (_0x4d81a6[_0x3628("0x188")] - 0x40 - 0x3 * _0x15bd15) / 0x2;
      _0x487312[_0x3628("0x3e")]["marginTop"] = _0x196ea5 + "px", _0x487312[_0x3628("0x3e")]["marginBottom"] = _0x196ea5 + "px";
    }
  } else {
    (_0x4d81a6 = i_sxfci_("gamescr"))[_0x3628("0x3e")][_0x3628("0x191")] = "", _0x4d81a6[_0x3628("0x3e")]["height"] = "", _0x4d81a6[_0x3628("0x3e")][_0x3628("0x292")] = _0x3628("0x44a");
  }
}
function i_GAQTtf(_0xe086b0) {
  0x0 != i_iDpHde ? i_pjQNyo() ? i_hnXPpo(_0x3628("0x242"), _0xe086b0 ? "1" : "0") : i_iZhRvt(_0x3628("0x370")) : i_iZhRvt("单机无需设置");
}
function i_YtNzaf(_0xcd8a2d) {
  0x0 != i_iDpHde ? i_pjQNyo() ? i_hnXPpo(_0x3628("0x13c"), _0xcd8a2d ? "1" : "0") : i_iZhRvt("房主才可设置") : i_iZhRvt("单机无需设置");
}
function i_axfcof() {
  i_ZKQCf_(_0x3628("0x480"), _0x3628("0x2fd"), function (_0x37c785) {
    if (_0x3628("0x4fd") === _0x3628("0x31f")) {
      0x1 == i_iDpHde ? i_rrYppt("联机模式目前不支持调速度") : (i_sxfci_("tx_gamespeed")[_0x3628("0x24d")] = i_YWkhPt < 0x8 ? (i_YWkhPt *= 0x2) + "倍" : (i_YWkhPt = 0x1, "加速"), i_bkNDhu(i_YWkhPt), i_rrYppt(_0x3628("0x182") + i_YWkhPt + "倍速"));
    } else {
      i_GezfJu(0x0 < this[_0x3628("0x32d")]);
    }
  }), i_ZKQCf_(_0x3628("0x340"), "change", function () {
    i_PafEQu(0x0 < this[_0x3628("0x32d")]);
  }), i_ZKQCf_(_0x3628("0x516"), _0x3628("0x2fd"), function () {
    if (_0x3628("0x11") !== _0x3628("0x9a")) {
      i_TfQXnf(0x0 < this[_0x3628("0x32d")]);
    } else {
      i_fFmbta(0x2);
    }
  }), i_tXrZr_(_0x3628("0x36c"), _0x3628("0x2fd"), function () {
    i_sJXWef(this[_0x3628("0x206")]);
  }), i_tXrZr_(_0x3628("0x30c"), "change", function () {
    if ("qIPKG" === _0x3628("0x473")) {
      i_GAQTtf(this[_0x3628("0x206")]);
    } else {
      i_njFT_l = !0x0;
    }
  }), i_tXrZr_(_0x3628("0x2aa"), "change", function () {
    i_YtNzaf(this[_0x3628("0x206")]);
  }), window[_0x3628("0x314")]("resize", i_JzBSZu);
}
function i_JKfMcf(_0x29ae17, _0x57683a) {
  for (var _0x4bf46b = document["getElementsByName"](_0x29ae17), _0x2269ed = 0x0; _0x2269ed < _0x4bf46b[_0x3628("0x288")]; _0x2269ed++) 0x0 < _0x4bf46b[_0x2269ed][_0x3628("0x32d")] == _0x57683a ? _0x4bf46b[_0x2269ed][_0x3628("0x206")] = !0x0 : _0x4bf46b[_0x2269ed][_0x3628("0x206")] = !0x1;
}
function i_QcDssf() {
  i_sxfci_(_0x3628("0x37"))[_0x3628("0x32d")] = i_WmyDOu[_0x3628("0x40d")], i_sxfci_(_0x3628("0x37"))[_0x3628("0x3e")][_0x3628("0x1c8")] = i_WmyDOu[_0x3628("0x40d")] + _0x3628("0x62"), i_rHShLf(i_WmyDOu[_0x3628("0x40d")] / 0x64), i_GezfJu(i_WmyDOu[_0x3628("0x1b5")]), i_JKfMcf(_0x3628("0x480"), i_WmyDOu[_0x3628("0x1b5")]), i_PafEQu(i_WmyDOu[_0x3628("0x16")]), i_JKfMcf(_0x3628("0x340"), i_WmyDOu[_0x3628("0x16")]), i_TfQXnf(i_WmyDOu["lockScr"]), i_JKfMcf(_0x3628("0x516"), i_WmyDOu[_0x3628("0x1d8")]), i_sJXWef(i_WmyDOu["Qn"]), i_sxfci_(_0x3628("0x36c"))[_0x3628("0x206")] = i_WmyDOu["Qn"];
}
function i_siAWrf() {
  i_iTBeIn(), i_Bckxkn(), i_fkKwAn();
}
var i_JBMrlf = 0x0,
  i_tDsKuf = 0x0,
  i_CEeRff = !0x1,
  i_PAkc_f = !0x1,
  i_mktmvf = !0x1,
  i_jKHfdf = !0x1,
  i_asXxpf,
  i_iDPymf,
  i_yHyrgf,
  i_nZPkhf,
  i_mSNPbf,
  i_KzHiyf = 0x0,
  i_bjXRwf = 0x0,
  i_SEwKkf = 0x1000,
  i_BfbdTf = 0x0,
  i_nwDwxf = 0x0,
  i_bhkMMf = !0x1,
  i_zmXCPf,
  i_dMCdEf,
  i_AsnZAf = 0x2,
  i_GbdzSf = !0x1,
  i_sfenCf = window[_0x3628("0x553")] || window["webkitAudioContext"],
  i_NXdZKf = new i_sfenCf({
    sampleRate: 0xbb80
  }),
  i_xZXYOf = [],
  i_tNmsXf = null,
  i_DQjNRf = [],
  i_ttskzf = [],
  i_daHxIf = null,
  i_rDftNf = null,
  i_iWFTUf = 0x0,
  i_pTEbDf = 0x0,
  i_RSxB$f = 0x0,
  i_akCNBf = 0x2800,
  i_pwwhGf = null,
  i_XFNiFf = null;
function i_rHShLf(_0x4da4a3) {
  i_CEeRff || i_kkffJf(), i_iDQBme && (window["Zn"][_0x3628("0x2d8")]["value"] = _0x4da4a3), 0x0 < _0x4da4a3 ? (i_PAkc_f = !0x0, i_NXdZKf[_0x3628("0x2d9")](), i_iZEzAl["kn"](0x1)) : (i_PAkc_f = !0x1, i_NXdZKf[_0x3628("0x531")](), i_iZEzAl["kn"](0x0));
}
function i_kbxYqf() {
  if (_0x3628("0x56a") == typeof SharedArrayBuffer) return !0x1;
  if (0xbb80 / (0x1 / i_NXdZKf[_0x3628("0x1c0")]) < 0x200) return !0x1;
  i_SEwKkf = 0x2800;
  var _0x2c08a3 = new SharedArrayBuffer(0x19000);
  i_nZPkhf = new Float32Array(i_SEwKkf), i_mSNPbf = new Float32Array(i_SEwKkf), i_asXxpf = new Float32Array(_0x2c08a3, 0x0, 0x2800), i_iDPymf = new Float32Array(_0x2c08a3, 0xa000, 0x2800), i_XFNiFf = new Uint32Array(_0x2c08a3, 0x14000, 0xa);
  var _0x28709c = new Function(_0x3628("0x131"), _0x3628("0x2d8"), _0x3628("0x32e"));
  return i_iDQBme ? (window["Zn"] = i_NXdZKf[_0x3628("0x7d")](), window["Zn"]["connect"](i_NXdZKf["destination"]), window["Zn"][_0x3628("0x2d8")][_0x3628("0x32d")] = 0x1, _0x28709c(i_NXdZKf, window["Zn"])["then"](function (_0x2167e1) {
    (i_pwwhGf = _0x2167e1)[_0x3628("0x417")]["postMessage"](_0x2c08a3);
  })) : _0x28709c(i_NXdZKf, i_NXdZKf["destination"])[_0x3628("0x515")](function (_0x3a8f2c) {
    if (_0x3628("0x3c5") !== _0x3628("0x3c5")) {
      $(i_tKMSa)["next"]()[_0x3628("0x30e")]();
    } else {
      (i_pwwhGf = _0x3a8f2c)[_0x3628("0x417")]["postMessage"](_0x2c08a3);
    }
  }), !0x0;
}
function i_KGzBVf() {
  i_SEwKkf = 0x400, i_daHxIf = new Float32Array(0x19000), i_iDQBme ? (i_SEwKkf = 0x400, window["et"] = i_NXdZKf["createScriptProcessor"](i_SEwKkf, 0x0, 0x2), window["Zn"] = i_NXdZKf["createGain"](), window["Zn"][_0x3628("0xba")](i_NXdZKf[_0x3628("0x421")]), window["Zn"]["gain"][_0x3628("0x32d")] = 0x1, window["et"][_0x3628("0xba")](window["Zn"]), window["et"][_0x3628("0x3b9")] = i_CHyYYf) : (i_SEwKkf = 0x1000, i_akCNBf = 0x2000, 0xbb80 / (0x1 / i_NXdZKf["baseLatency"]) <= 0x100 && !i_iDQBme && (i_SEwKkf = 0x800, i_akCNBf = 0x2000), window["et"] = i_NXdZKf[_0x3628("0x14e")](i_SEwKkf, 0x0, 0x2), window["et"]["onaudioprocess"] = i_CHyYYf, window["et"]["connect"](i_NXdZKf[_0x3628("0x421")]));
  var _0x41ac78 = new XMLHttpRequest();
  _0x41ac78[_0x3628("0x3d8")](_0x3628("0x2c0"), _0x3628("0x51b"), !0x0), _0x41ac78["responseType"] = _0x3628("0x368"), _0x41ac78[_0x3628("0x278")] = function () {
    if (_0x3628("0x2c5") === _0x3628("0x2c5")) {
      0x4 === _0x41ac78["readyState"] && 0xc8 === _0x41ac78["status"] && i_NXdZKf[_0x3628("0x31e")](this[_0x3628("0x3f")], function (_0x3a651c) {
        ClickSound = _0x3a651c[_0x3628("0x403")](0x0);
      });
    } else {
      i_WwiRv_(_0x3628("0x97"), _0x3628("0x8c"));
    }
  }, _0x41ac78[_0x3628("0x559")]();
}
function i_dZfEjf() {
  i_CEeRff || (i_CEeRff = (i_kbxYqf() || i_KGzBVf(), !0x0));
}
function i_CHyYYf(_0x167a59) {
  var _0x279222 = _0x167a59["outputBuffer"][_0x3628("0x403")](0x0),
    _0x46417f = _0x167a59[_0x3628("0x1b4")][_0x3628("0x403")](0x1);
  if (!i_mktmvf) {
    if (i_jKHfdf && i_WmyDOu["gCtlSound"] && (i_asXxpf[_0x3628("0x4a3")](ClickSound, 0x0), i_iDPymf[_0x3628("0x4a3")](ClickSound, 0x0), i_jKHfdf = !0x1), i_iWFTUf < i_pTEbDf + i_SEwKkf) return _0x279222[_0x3628("0x3d9")](0x0), void _0x46417f[_0x3628("0x3d9")](0x0);
    var _0x43cdf1 = i_daHxIf[_0x3628("0xce")](i_RSxB$f, i_RSxB$f + i_SEwKkf);
    _0x279222[_0x3628("0x4a3")](_0x43cdf1), _0x46417f[_0x3628("0x4a3")](_0x43cdf1), _0x43cdf1["fill"](0x0), 0x19000 <= (i_RSxB$f += i_SEwKkf) && (i_RSxB$f = 0x0), i_pTEbDf += i_SEwKkf;
  }
}
function i_RttEHf() {
  i_NXdZKf[_0x3628("0x2d9")](), _0x3628("0x15") == i_NXdZKf[_0x3628("0x382")] ? setTimeout(i_RttEHf, 0x7d0) : i_bhkMMf = !0x0;
}
var i_FNijWf = 0x0;
function i_kkffJf() {
  0x3 < i_FNijWf && (i_iDQBme || i_Wkbybr(_0x3628("0x385"))), 0x0 == i_PAkc_f ? (i_PAkc_f = !0x0, i_dZfEjf(), i_RttEHf(), i_iZEzAl["kn"](0x1), i_FNijWf++) : (i_PAkc_f = !0x1, i_NXdZKf[_0x3628("0x531")](), i_iZEzAl["kn"](0x0));
}
var i_DdTaQf = null;
function i_DjetZf() {
  $[_0x3628("0x276")]({
    type: _0x3628("0x463"),
    url: _0x3628("0x21f"),
    async: !0x0,
    success: function (_0x28a235) {
      i_DdTaQf = _0x28a235["info"], i_iDQBme ? i_jypkDt() : i_AJbmKr(), (0x0 < i_DdTaQf[_0x3628("0x31")][_0x3628("0x33e")] || 0x0 < i_DdTaQf[_0x3628("0x31")][_0x3628("0x150")]) && i_mMmFe_(_0x3628("0x462"), _0x3628("0x268"), ["fc", gid, i_DdTaQf[_0x3628("0x187")], i_DdTaQf[_0x3628("0x31")][_0x3628("0x48f")], i_DdTaQf[_0x3628("0x31")][_0x3628("0x33e")], i_DdTaQf[_0x3628("0x31")]["Svip"]]);
    },
    complete: function (_0x178655, _0x4d6c1a) {
      if (_0x3628("0x464") !== _0x3628("0x464")) {
        i_JryzR_ = i_cWpGN_["at"](i_YBrGP_), i_YBrGP_[_0x3628("0x56")](i_JryzR_), luadata = i_YBrGP_[_0x3628("0x140")](), i_YBrGP_["activeTexture"](i_YBrGP_[_0x3628("0x2c4")]), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_["TEXTURE_2D"], luadata), i_YBrGP_[_0x3628("0x411")](i_YBrGP_[_0x3628("0x40e")], 0x0, i_YBrGP_["RGBA"], i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_["UNSIGNED_BYTE"], i_DHTJc), i_YBrGP_[_0x3628("0x297")](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x43d")], i_YBrGP_[_0x3628("0x36a")]), i_YBrGP_[_0x3628("0x297")](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x2b3")], i_YBrGP_[_0x3628("0x36a")]);
        var _0x4236e1 = i_YBrGP_[_0x3628("0x56d")](i_JryzR_, _0x3628("0xef")),
          _0x19e792 = i_YBrGP_["getAttribLocation"](i_JryzR_, _0x3628("0x3a2"));
        i_YBrGP_["enableVertexAttribArray"](_0x4236e1), i_YBrGP_[_0x3628("0x1e0")](_0x4236e1, 0x2, i_YBrGP_[_0x3628("0x1db")], !0x1, 0x0, 0x0), i_YBrGP_["enableVertexAttribArray"](_0x19e792), i_YBrGP_["vertexAttribPointer"](_0x19e792, 0x2, i_YBrGP_["FLOAT"], !0x1, 0x0, 0x0);
        var _0x5df7ac = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x30d")),
          _0x4493fb = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x1e7")),
          _0x3e7d7f = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x48")),
          _0x330567 = i_YBrGP_["getUniformLocation"](i_JryzR_, _0x3628("0x34c"));
        i_YBrGP_["uniform1i"](_0x5df7ac, 0x0), i_YBrGP_[_0x3628("0x205")](_0x4493fb, 0x1), i_YBrGP_[_0x3628("0x4fc")](_0x3e7d7f, 0x200, 0x200), i_YBrGP_[_0x3628("0x4fc")](_0x330567, 0x200, 0x200), i_YBrGP_[_0x3628("0x56")](shaderProgram), i_YBrGP_[_0x3628("0x53c")](i_YBrGP_[_0x3628("0x50f")]), i_YBrGP_["bindTexture"](i_YBrGP_["TEXTURE_2D"], texture), i_PafEQu(i_WmyDOu[_0x3628("0x16")]);
      } else {
        0xc8 != _0x178655[_0x3628("0x446")] && location[_0x3628("0x2c2")](!0x0);
      }
    }
  });
}
function i_mMmFe_(_0x57fb13, _0x1f803c, _0x111c74) {
  for (var _0x1a2dbc in _0x111c74) _0x3628("0x1a4") != typeof _0x111c74[_0x1a2dbc] && (_0x111c74[_0x1a2dbc] = _0x111c74[_0x1a2dbc] + "");
  i_WSEwg_(_0x3628("0x1be"), {
    Url: location["href"],
    Type: _0x57fb13,
    Event: _0x1f803c,
    EventData: _0x111c74,
    Sign: "ok"
  });
}
var i_aaCBn_ = {
  nt: "",
  addEventListener: function () {}
};
function i_YHmAt_(_0x580f10) {
  for (var _0x5bf618 = document[_0x3628("0x482")][_0x3628("0x11b")](";\x20"), _0x21f0e0 = 0x0; _0x21f0e0 < _0x5bf618[_0x3628("0x288")]; _0x21f0e0++) {
    var _0x1f2524 = _0x5bf618[_0x21f0e0]["split"]("=");
    if (_0x1f2524[0x0] == _0x580f10) return _0x1f2524[0x1];
  }
  return "";
}
function i_tdBda_(_0x2a3092) {
  var _0xcbaa08 = new RegExp("(^|&)" + _0x2a3092 + _0x3628("0x48d")),
    _0x2bf07b = window[_0x3628("0x497")][_0x3628("0x7")][_0x3628("0x26a")](0x1)[_0x3628("0x3b3")](_0xcbaa08);
  return null != _0x2bf07b ? decodeURI(_0x2bf07b[0x2]) : null;
}
var i_sxfci_ = function (_0x287652) {
  var _0xe98678 = document[_0x3628("0x416")](_0x287652);
  return null == _0xe98678 ? (i_aaCBn_["nt"] = _0x287652, _0xe98678 = i_aaCBn_, null) : _0xe98678;
};
function i_Brhwo_(_0x97b515) {
  return null == _0x97b515 ? "badele" : _0x97b515["getAttribute"]("key");
}
function i_kzkwc_(_0x2e1c63, _0x25ed11) {
  var _0x5b8f36 = document[_0x3628("0x416")](_0x2e1c63);
  null != _0x5b8f36 && _0x5b8f36[_0x3628("0x323")]("click", _0x25ed11);
}
function i_Hfbes_(_0x327736, _0x152ff1) {
  var _0x31f661 = document[_0x3628("0x416")](_0x327736);
  null != _0x31f661 && _0x31f661[_0x3628("0x314")](_0x3628("0x4ab"), _0x152ff1);
}
function i_tXrZr_(_0x40dad6, _0x120fc8, _0x16f51f) {
  var _0x305629 = document["getElementById"](_0x40dad6);
  null != _0x305629 && _0x305629[_0x3628("0x314")](_0x120fc8, _0x16f51f);
}
function i_cirYl_(_0x593c72, _0x2bc137) {
  document[_0x3628("0x566")](_0x593c72)[_0x3628("0x64")](function (_0x395467) {
    if (_0x3628("0x1a1") !== _0x3628("0x221")) {
      _0x395467[_0x3628("0x314")](_0x3628("0x4ab"), _0x2bc137);
    } else {
      i_YPnYt = 0x2 * i_dzZse[0x31 + i_jKmXn], i_Qdeha = 0x2 * i_dzZse[0x41 + i_jKmXn] + 0x2 * i_dzZse[0x51 + i_jKmXn] - 0x94;
      i_aPKPKe[_0x3628("0x46d")] = _0x3628("0x1ce"), i_aPKPKe["strokeStyle"] = _0x3628("0x13b");
      var _0x5d4e6b = "";
      i_EBCZln["M"][_0x3628("0x32d")] && (_0x5d4e6b = i_jKmXn + 0x1 + ".\x20"), i_EBCZln["T"] && (i_aPKPKe[_0x3628("0x1fd")](_0x5d4e6b + _0x3628("0x44c") + 0x64 * i_dzZse[0x11 + i_jKmXn], i_YPnYt + 0x1 - 0xa, i_Qdeha + 0x1, 0x64), i_aPKPKe["fillText"](_0x5d4e6b + _0x3628("0x44c") + 0x64 * i_dzZse[0x11 + i_jKmXn], i_YPnYt - 0xa, i_Qdeha, 0x64)), i_aPKPKe["strokeStyle"] = _0x3628("0x3a0"), i_aPKPKe[_0x3628("0x285")] = 0x1, i_aPKPKe["beginPath"](), i_aPKPKe[_0x3628("0x190")](i_YPnYt - 0x10, i_Qdeha - 0x14, 0x3c, 0x2), i_aPKPKe[_0x3628("0x324")](), i_aPKPKe[_0x3628("0x1f1")]();
      var _0x1df1b5 = i_dzZse[0x11 + i_jKmXn] / i_dzZse[0x21 + i_jKmXn] * 0x3c;
      i_aPKPKe[_0x3628("0x46d")] = 0x28 < _0x1df1b5 ? _0x3628("0xa2") : 0x14 < _0x1df1b5 ? _0x3628("0x220") : _0x3628("0x575"), i_aPKPKe["fillRect"](i_YPnYt - 0x10, i_Qdeha - 0x14, _0x1df1b5, 0x2), i_WTdn_++;
    }
  });
}
function i_dhBiu_(_0x51feeb, _0x2008fe) {
  document[_0x3628("0x566")](_0x51feeb)[_0x3628("0x64")](function (_0x55b357) {
    _0x2008fe(_0x55b357);
  });
}
function i_ZKQCf_(_0x5decb1, _0x58bff1, _0xb9cb7e) {
  document[_0x3628("0x566")](_0x5decb1)[_0x3628("0x64")](function (_0x55479a) {
    _0x55479a[_0x3628("0x314")](_0x58bff1, _0xb9cb7e);
  });
}
function i_jzyY__(_0xb04110, _0xe4e94a) {
  document[_0x3628("0x566")](_0xb04110)["forEach"](function (_0xd166a9) {
    _0xd166a9[_0x3628("0xea")][_0x3628("0x47a")](_0xe4e94a);
  });
}
function i_WwiRv_(_0x27894f, _0x18871c) {
  var _0x404dbd = i_sxfci_(_0x27894f);
  return _0x3628("0x47e") == _0x404dbd[_0x3628("0x3e")][_0x3628("0x3b5")] ? (_0x404dbd[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x18871c, !0x0) : !(_0x404dbd[_0x3628("0x3e")][_0x3628("0x3b5")] = _0x3628("0x47e"));
}
function i_wccDd_() {
  var _0x49dec4 = document[_0x3628("0x416")](_0x3628("0x552"));
  i_YBrGP_[_0x3628("0x114")](i_YBrGP_[_0x3628("0x152")], 0x0, 0x6);
  var _0x2c0ad9 = document["createElement"]("canvas");
  return _0x2c0ad9["width"] = 0xc8, _0x2c0ad9[_0x3628("0x9")] = 0x96, _0x2c0ad9[_0x3628("0x158")]("2d")[_0x3628("0x250")](_0x49dec4, 0x0, 0x0, 0xc8, 0x96), _0x2c0ad9["toDataURL"](_0x3628("0x471"));
}
function i_zMkcp_() {
  tempSrc = GetGameImg(), $("#downurl")[_0x3628("0x210")](_0x3628("0x3a"), tempSrc), $(_0x3628("0xca"))[_0x3628("0x210")]("src", tempSrc);
}
function i_PZAFm_(_0x2e0e83) {
  for (var _0x5d8ac4 = _0x2e0e83[_0x3628("0x11b")](","), _0x29f1b0 = _0x5d8ac4[0x0][_0x3628("0x3b3")](/:(.*?);/)[0x1], _0x339b56 = atob(_0x5d8ac4[0x1]), _0x43dde9 = _0x339b56[_0x3628("0x288")], _0x23bab6 = new Uint8Array(_0x43dde9); _0x43dde9--;) _0x23bab6[_0x43dde9] = _0x339b56["charCodeAt"](_0x43dde9);
  return new Blob([_0x23bab6], {
    type: _0x29f1b0
  });
}
function i_WSEwg_(_0x2b80ae, _0x1a39f8, _0x2f93f9, _0x1bc787) {
  var _0x198aef = new XMLHttpRequest();
  _0x198aef[_0x3628("0x3d8")](_0x3628("0x4b"), _0x2b80ae, !0x0), _0x198aef[_0x3628("0x59")](_0x3628("0x1de"), _0x3628("0x564")), _0x198aef[_0x3628("0x1c4")] = _0x3628("0x346"), _0x198aef[_0x3628("0x278")] = function () {
    _0x198aef["readyState"] == XMLHttpRequest[_0x3628("0x4e")] && (0xc8 == _0x198aef[_0x3628("0x446")] ? null != _0x2f93f9 && _0x2f93f9(_0x198aef["response"]) : null != _0x1bc787 && _0x1bc787(_0x198aef[_0x3628("0x3f")]));
  }, _0x198aef[_0x3628("0x559")](JSON[_0x3628("0x2ae")](_0x1a39f8));
}
var i_CHHkh_ = {};
function i_rBKib_(_0x53fddd, _0x51963) {
  i_CHHkh_[_0x53fddd] = 0x0, i_tXrZr_(_0x53fddd, _0x3628("0x87"), function (_0x11f30c) {
    if (this[_0x3628("0x306")]) _0x11f30c["preventDefault"]();else {
      var _0xbbfe66 = this[_0x3628("0x39f")](),
        _0x553e91 = i_QtJCKs(_0xbbfe66[0x0]["x"], _0xbbfe66[0x0]["y"]),
        _0x4d5af9 = i_QtJCKs(_0x11f30c[_0x3628("0x3db")][0x0][_0x3628("0x325")], _0x11f30c[_0x3628("0x3db")][0x0][_0x3628("0x16e")])["pn"];
      this[_0x3628("0x32d")] = (_0x4d5af9 - _0x553e91["pn"]) / (this[_0x3628("0x3a1")] / (parseInt(this["max"]) - parseInt(this[_0x3628("0x359")]))) + parseInt(this[_0x3628("0x359")]), _0x51963(this), _0x11f30c[_0x3628("0x23")]();
    }
  });
}
function i_QSwGy_() {
  cyc_queryID(_0x3628("0x54f"))[_0x3628("0x306")] = !0x0, $["get"](_0x3628("0x258") + gid, function (_0x591eb9) {
    if (_0x3628("0x3aa") === _0x3628("0x3aa")) {
      cyc_queryID(_0x3628("0x54f"))[_0x3628("0x24d")] = _0x3628("0x4ce"), cyc_queryID(_0x3628("0x54f"))[_0x3628("0x3e")][_0x3628("0x70")] = "#3fdd2e61", setTimeout(function () {
        cyc_queryID(_0x3628("0x54f"))["style"][_0x3628("0x3b5")] = _0x3628("0x47e");
      }, 0x1388);
    } else {
      i_iZhRvt(_0x3628("0x3fa"));
    }
  });
}
var i_thkew_ = null,
  i_zMDwk_ = null;
function i_SSyQT_() {
  i_thkew_ = i_sxfci_(_0x3628("0x552")), i_zMDwk_ = i_sxfci_(i_iDQBme ? _0x3628("0x565") : _0x3628("0xa9"));
}
function i_CrAfx_() {
  var _0x5992ea = 0x200 / i_kRDkS_,
    _0x4b9458 = 0x200 / i_kPQcC_,
    _0x32fca7 = _0x3628("0x11e") + _0x5992ea + ".0)\x20-\x201.0,\x20(a_position.y\x20*\x202.0\x20*\x20" + _0x4b9458 + _0x3628("0x17b");
  return i_TBJJz_ && (_0x32fca7 = "attribute\x20vec2\x20a_position;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20varying\x20highp\x20vec2\x20v_textureCoord;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20void\x20main()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20vec4((a_position.y\x20*\x202.0\x20*\x20" + _0x4b9458 + _0x3628("0x4e0") + _0x5992ea + ")\x20*\x20-1.0\x20+\x201.0),\x200,\x201);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20v_textureCoord\x20=\x20vec2(a_position.x,\x20a_position.y);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}"), _0x32fca7;
}
var i_chNJM_ = "\x20\x20varying\x20highp\x20vec2\x20v_textureCoord;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20sampler2D\x20u_sampler;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20void\x20main(void)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_FragColor\x20=\x20texture2D(u_sampler,\x20vec2(v_textureCoord.s,\x20v_textureCoord.t));\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}",
  i_YBrGP_ = null,
  i_ecCpE_ = "",
  i_dHGrA_ = "",
  i_kRDkS_ = 0x0,
  i_kPQcC_ = 0x0,
  i_zcjCK_ = null,
  i_yhDYO_ = 0x4,
  i_HSeGX_ = 0x14,
  i_JryzR_ = null,
  i_TBJJz_ = !0x1,
  i_JYQZI_ = !0x1,
  i_cWpGN_ = {
    compileShader: function (_0xbb1277, _0x643ee1, _0x3a46ba) {
      var _0xe568d1 = _0xbb1277[_0x3628("0x44b")](_0x3a46ba);
      if (_0xbb1277["shaderSource"](_0xe568d1, _0x643ee1), _0xbb1277[_0x3628("0x4f2")](_0xe568d1), !_0xbb1277[_0x3628("0x1d3")](_0xe568d1, _0xbb1277["COMPILE_STATUS"])) throw new Error(_0x3628("0x254") + _0xbb1277["getShaderInfoLog"](_0xe568d1));
      return _0xe568d1;
    },
    createProgram: function (_0x32d189, _0x27f6b4, _0x3605c3) {
      var _0x16b9d3 = _0x32d189[_0x3628("0x3cd")]();
      if (_0x32d189[_0x3628("0x100")](_0x16b9d3, _0x27f6b4), _0x32d189[_0x3628("0x100")](_0x16b9d3, _0x3605c3), _0x32d189[_0x3628("0x543")](_0x16b9d3), !_0x32d189[_0x3628("0x3d5")](_0x16b9d3, _0x32d189[_0x3628("0x490")])) throw new Error(_0x3628("0x435") + _0x32d189[_0x3628("0x11d")](_0x16b9d3));
      return _0x16b9d3;
    },
    tt: function (_0x25df16, _0x848b76, _0x42c27b) {
      var _0x5d4732 = "";
      if (!_0x42c27b) if (_0x3628("0x26") === _0x848b76) _0x42c27b = _0x25df16[_0x3628("0x72")], _0x5d4732 = i_ecCpE_;else {
        if (_0x3628("0x503") !== _0x848b76) throw new Error("Unreachable");
        _0x42c27b = _0x25df16[_0x3628("0x378")], _0x5d4732 = i_dHGrA_;
      }
      return this["compileShader"](_0x25df16, _0x5d4732, _0x42c27b);
    },
    createProgramFromScripts: function (_0x50646d) {
      var _0x31fd00 = this["tt"](_0x50646d, _0x3628("0x26")),
        _0x5cb30e = this["tt"](_0x50646d, _0x3628("0x503"));
      return this["createProgram"](_0x50646d, _0x31fd00, _0x5cb30e);
    },
    at: function (_0x11dc2a) {
      var _0x1fbf71 = this["compileShader"](_0x11dc2a, i_pKdHV_(i_kRDkS_, i_kPQcC_), _0x11dc2a[_0x3628("0x72")]),
        _0xa689c5 = this[_0x3628("0x4f2")](_0x11dc2a, i_QaNAj_, _0x11dc2a[_0x3628("0x378")]);
      return this["createProgram"](_0x11dc2a, _0x1fbf71, _0xa689c5);
    }
  };
function i_kNJaU_() {
  if (i_iDQBme ? null == (i_YBrGP_ = i_thkew_[_0x3628("0x158")](_0x3628("0x103"), {
    alpha: !0x1,
    it: !0x1
  })) && (i_YBrGP_ = i_thkew_[_0x3628("0x158")](_0x3628("0x46c"), {
    alpha: !0x1,
    it: !0x1
  })) : i_YBrGP_ = i_thkew_["getContext"](_0x3628("0x46c"), {
    alpha: !0x1,
    it: !0x1
  }), i_ankaua["g"]()) {
    if (_0x3628("0x4a2") === _0x3628("0x85")) {
      i_JhjKFs(i_sdCNe);
    } else {
      var _0x47d943 = i_ankaua["Ke"](_0x3628("0x50"));
      0x0 <= _0x47d943["opengl"]["indexOf"](_0x3628("0x306")) && (i_JYQZI_ = !0x0);
    }
  }
  if (null == i_YBrGP_) return i_iZhRvt(_0x3628("0x5e")), void (i_DYNBTe[_0x3628("0x286")] = function () {});
  var _0x54f1df = i_YBrGP_[_0x3628("0x455")](_0x3628("0x104"));
  0x0 <= (_0x54f1df && i_YBrGP_["getParameter"](_0x54f1df[_0x3628("0x558")]))[_0x3628("0xb8")]()["indexOf"](_0x3628("0x112")) && (i_JYQZI_ = !0x0, i_iZhRvt(_0x3628("0x534"))), i_YBrGP_[_0x3628("0x51c")](0x0, 0x0, i_thkew_[_0x3628("0x191")], i_thkew_["height"]), shaderProgram = i_cWpGN_["createProgramFromScripts"](i_YBrGP_), texture = i_YBrGP_[_0x3628("0x140")](), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_[_0x3628("0x40e")], texture);
  var _0x59bacc = new Uint16Array(0x40000);
  i_YBrGP_["texImage2D"](i_YBrGP_[_0x3628("0x40e")], 0x0, i_YBrGP_[_0x3628("0x4bb")], 0x200, 0x200, 0x0, i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_[_0x3628("0x21a")], _0x59bacc), i_YBrGP_[_0x3628("0x297")](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x43d")], i_YBrGP_[_0x3628("0x36a")]), i_YBrGP_[_0x3628("0x297")](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x2b3")], i_YBrGP_[_0x3628("0x36a")]), i_YBrGP_[_0x3628("0x297")](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_["TEXTURE_WRAP_S"], i_YBrGP_["CLAMP_TO_EDGE"]), i_YBrGP_["texParameteri"](i_YBrGP_["TEXTURE_2D"], i_YBrGP_[_0x3628("0x1c7")], i_YBrGP_[_0x3628("0x484")]);
  var _0x2eb330 = i_YBrGP_[_0x3628("0x4a1")]();
  i_YBrGP_[_0x3628("0xac")](i_YBrGP_[_0x3628("0x2d")], _0x2eb330), i_YBrGP_[_0x3628("0x519")](i_YBrGP_["ARRAY_BUFFER"], new Float32Array([0x0, 0x0, 0x1, 0x0, 0x0, 0x1, 0x0, 0x1, 0x1, 0x0, 0x1, 0x1]), i_YBrGP_[_0x3628("0x2b4")]);
  var _0x2aae0f = i_YBrGP_[_0x3628("0x56d")](shaderProgram, _0x3628("0x18a")),
    _0x3e8ce6 = i_YBrGP_[_0x3628("0x4e6")](shaderProgram, _0x3628("0x124"));
  if (i_YBrGP_[_0x3628("0x56")](shaderProgram), i_YBrGP_["bindBuffer"](i_YBrGP_[_0x3628("0x2d")], _0x2eb330), i_YBrGP_[_0x3628("0x201")](_0x2aae0f), i_YBrGP_["vertexAttribPointer"](_0x2aae0f, 0x2, i_YBrGP_[_0x3628("0x1db")], !0x1, 0x0, 0x0), i_iDQBme) {
    if (_0x3628("0x120") !== _0x3628("0x120")) {
      i_RMfDe["readyState"] == XMLHttpRequest[_0x3628("0x4e")] && i_RMfDe[_0x3628("0x446")];
    } else {
      var _0x534cf9 = new Image();
      _0x534cf9[_0x3628("0x1d")] = function () {
        i_JryzR_ = i_cWpGN_["at"](i_YBrGP_), i_YBrGP_[_0x3628("0x56")](i_JryzR_), luadata = i_YBrGP_["createTexture"](), i_YBrGP_["activeTexture"](i_YBrGP_[_0x3628("0x2c4")]), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_["TEXTURE_2D"], luadata), i_YBrGP_["texImage2D"](i_YBrGP_[_0x3628("0x40e")], 0x0, i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_[_0x3628("0x3e2")], _0x534cf9), i_YBrGP_["texParameteri"](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x43d")], i_YBrGP_[_0x3628("0x36a")]), i_YBrGP_["texParameteri"](i_YBrGP_[_0x3628("0x40e")], i_YBrGP_[_0x3628("0x2b3")], i_YBrGP_[_0x3628("0x36a")]);
        var _0x2d9aef = i_YBrGP_["getAttribLocation"](i_JryzR_, _0x3628("0xef")),
          _0x2ce811 = i_YBrGP_[_0x3628("0x56d")](i_JryzR_, "TexCoord");
        i_YBrGP_[_0x3628("0x201")](_0x2d9aef), i_YBrGP_[_0x3628("0x1e0")](_0x2d9aef, 0x2, i_YBrGP_[_0x3628("0x1db")], !0x1, 0x0, 0x0), i_YBrGP_["enableVertexAttribArray"](_0x2ce811), i_YBrGP_[_0x3628("0x1e0")](_0x2ce811, 0x2, i_YBrGP_[_0x3628("0x1db")], !0x1, 0x0, 0x0);
        var _0x138686 = i_YBrGP_["getUniformLocation"](i_JryzR_, _0x3628("0x30d")),
          _0x55851e = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x1e7")),
          _0x1c91e5 = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x48")),
          _0xf59007 = i_YBrGP_[_0x3628("0x4e6")](i_JryzR_, _0x3628("0x34c"));
        i_YBrGP_[_0x3628("0x205")](_0x138686, 0x0), i_YBrGP_[_0x3628("0x205")](_0x55851e, 0x1), i_YBrGP_["uniform2f"](_0x1c91e5, 0x200, 0x200), i_YBrGP_[_0x3628("0x4fc")](_0xf59007, 0x200, 0x200), i_YBrGP_[_0x3628("0x56")](shaderProgram), i_YBrGP_["activeTexture"](i_YBrGP_[_0x3628("0x50f")]), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_[_0x3628("0x40e")], texture), i_PafEQu(i_WmyDOu["sHQX"]);
      }, _0x534cf9[_0x3628("0x1c5")] = "./img/hq4x.png";
    }
  }
  i_YBrGP_["activeTexture"](i_YBrGP_[_0x3628("0x50f")]), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_[_0x3628("0x40e")], texture), i_YBrGP_[_0x3628("0x205")](_0x3e8ce6, 0x0);
}
function i_QCDwD_(_0x4b0824) {
  null != i_YBrGP_ && null != i_JryzR_ && (i_JYQZI_ && i_iZhRvt(_0x3628("0x29e")), _0x4b0824 && 0x0 == i_JYQZI_ ? (i_yhDYO_ = 0x4, i_thkew_[_0x3628("0x191")] = i_kRDkS_ * i_yhDYO_, i_thkew_[_0x3628("0x9")] = i_kPQcC_ * i_yhDYO_, i_YBrGP_["useProgram"](i_JryzR_), i_YBrGP_[_0x3628("0x51c")](0x0, 0x0, i_kRDkS_ * i_yhDYO_, i_kPQcC_ * i_yhDYO_)) : (i_yhDYO_ = 0x1, i_thkew_["width"] = i_kRDkS_, i_thkew_[_0x3628("0x9")] = i_kPQcC_, i_YBrGP_["activeTexture"](i_YBrGP_["TEXTURE0"]), i_YBrGP_[_0x3628("0x46e")](i_YBrGP_[_0x3628("0x40e")], texture), i_YBrGP_["useProgram"](shaderProgram), i_YBrGP_[_0x3628("0x51c")](0x0, 0x0, i_kRDkS_, i_kPQcC_)));
}
function i_bxjR$_(_0x1d29af) {
  i_YBrGP_[_0x3628("0x1bb")](i_YBrGP_[_0x3628("0x40e")], 0x0, 0x0, 0x0, i_kRDkS_, i_kPQcC_, i_YBrGP_["RGBA"], i_YBrGP_[_0x3628("0x21a")], _0x1d29af), i_YBrGP_[_0x3628("0x114")](i_YBrGP_[_0x3628("0x152")], 0x0, 0x6);
}
var i_cNjsB_ = 0x2e3,
  i_rGEfG_ = Math[_0x3628("0x11f")](0x989680 * Math[_0x3628("0x175")]());
function i_WYEaF_(_0x314152, _0x1fce64, _0xf609fe) {
  return _0x314152["split"](_0x1fce64)[_0x3628("0x37c")](_0xf609fe);
}
function i_fJnBL_(_0x47bd72) {
  var _0x4969b0 = _0x3628("0x246"),
    _0x177a00 = _0x3628("0x231"),
    _0x53b3e2 = _0x4969b0[_0x3628("0x288")],
    _0x35cfcd = _0x4969b0[_0x3628("0x3e6")](i_rGEfG_ / 0x908 % _0x53b3e2),
    _0x50a2f3 = _0x177a00[_0x3628("0x3e6")](i_rGEfG_ / 0x908 % _0x53b3e2),
    _0x173acf = _0x4969b0["charAt"](i_rGEfG_ / 0x37ab % _0x53b3e2),
    _0x203026 = _0x177a00[_0x3628("0x3e6")](i_rGEfG_ / 0x37ab % _0x53b3e2),
    _0x311fe9 = _0x4969b0[_0x3628("0x3e6")](i_rGEfG_ / 0xa751e % _0x53b3e2),
    _0x3cfbae = _0x177a00[_0x3628("0x3e6")](i_rGEfG_ / 0xa751e % _0x53b3e2),
    _0x5cb2d9 = _0x4969b0[_0x3628("0x3e6")](i_rGEfG_ / 0x7c % _0x53b3e2),
    _0x2eb5f4 = _0x177a00[_0x3628("0x3e6")](i_rGEfG_ / 0x7c % _0x53b3e2);
  return _0x47bd72 = i_WYEaF_(_0x47bd72 = i_WYEaF_(_0x47bd72 = i_WYEaF_(_0x47bd72 = i_WYEaF_(_0x47bd72, _0x35cfcd, _0x50a2f3), _0x173acf, _0x203026), _0x311fe9, _0x3cfbae), _0x5cb2d9, _0x2eb5f4), window[_0x3628("0x4f3")](_0x47bd72);
}
function i_QMCCq_(_0x5c76d9) {
  if (0x4 != _0x5c76d9[_0x3628("0x2e7")][_0x3628("0x22")]) {
    if (0x18 != _0x5c76d9[_0x3628("0x2e7")]["byteLength"]) if (0x1 == i_iDpHde && _0x5c76d9[_0x3628("0x2e7")][_0x3628("0x22")] < 0x800) i_KatGXo(_0x5c76d9[_0x3628("0x2e7")]);else {
      if (++i_MChKiu % i_WmyDOu[_0x3628("0x2ba")] == 0x0) {
        var _0x432453 = GetAutoPlay();
        null != _0x432453 && SendKey(i_GHKWya["$e"], _0x432453);
      }
      if (i_MChKiu < 0x1388 && (0x1 == i_MChKiu && i_ZSfTwa && i_cxmKmu(), i_MChKiu % 0x3b == 0x0 && 0x1 == i_WmyDOu["gSkipFrame"])) {
        var _0x2d8676 = performance[_0x3628("0x30b")]();
        0x96 < i_MChKiu && 0x0 != i_CCRpou && (0x4b0 <= _0x2d8676 - i_CCRpou && 0x0 < i_hipxcu ? i_xCyTvu() : 0x1 == i_iDpHde && 0x618 <= _0x2d8676 - i_CCRpou && i_xCyTvu()), i_CCRpou = _0x2d8676;
      }
      if (i_PAkc_f) {
        var _0x34047d = !0x1,
          _0x4dd94c = new Int16Array(_0x5c76d9["data"], 0x0, 0x800);
        0x0 < i_KzHiyf && 0x0 == i_bjXRwf && (i_iDPymf[_0x3628("0x4a3")](i_mSNPbf), i_asXxpf[_0x3628("0x4a3")](i_nZPkhf), i_bjXRwf = i_KzHiyf, i_KzHiyf = 0x0);
        for (var _0x55af9f = 0x0; _0x55af9f < i_cNjsB_; _0x55af9f++) if (i_SEwKkf <= i_bjXRwf) {
          if (i_SEwKkf / 0x2 <= i_KzHiyf && 0x1 != i_iDpHde && 0x0 == _0x34047d && (SendCommand(i_cZwcUl["Nn"], 0x0, 0x0), _0x34047d = !0x0), i_SEwKkf <= i_KzHiyf) break;
          i_mSNPbf[i_KzHiyf] = _0x4dd94c[0x2 * _0x55af9f] / 0x8000, i_nZPkhf[i_KzHiyf] = _0x4dd94c[0x2 * _0x55af9f + 0x1] / 0x8000, i_KzHiyf++;
        } else i_iDPymf[i_bjXRwf] = _0x4dd94c[0x2 * _0x55af9f] / 0x8000, i_asXxpf[i_bjXRwf] = _0x4dd94c[0x2 * _0x55af9f + 0x1] / 0x8000, i_bjXRwf++;
      }
      if (0x1000 < _0x5c76d9[_0x3628("0x2e7")][_0x3628("0x22")]) {
        var _0x1812a9 = new Uint16Array(_0x5c76d9[_0x3628("0x2e7")], 0x1000);
        i_YBrGP_[_0x3628("0x1bb")](i_YBrGP_[_0x3628("0x40e")], 0x0, 0x0, 0x0, i_thkew_["width"], i_thkew_[_0x3628("0x9")], i_YBrGP_[_0x3628("0x4bb")], i_YBrGP_[_0x3628("0x21a")], _0x1812a9), i_YBrGP_[_0x3628("0x114")](i_YBrGP_[_0x3628("0x152")], 0x0, 0x6);
      }
      i_ZSfTwa && setTimeout(i_TBswui, 0x5);
    }
  } else ProcessRecvCommand(_0x5c76d9[_0x3628("0x2e7")]);
}
function i_pKdHV_(_0x388713, _0x454ca1) {
  var _0x454ccf = 0x200 / _0x388713,
    _0x22faa6 = 0x200 / _0x454ca1;
  if (i_TBJJz_) _0xdb8380 = _0x3628("0x555") + _0x22faa6 + _0x3628("0x302") + _0x454ccf + ")\x20*\x20-1.0\x20+\x201.0),\x200,\x201);\x0a\x20\x20\x20\x20_ps\x20=\x201.00000000E+00/TextureSize;\x0a\x20\x20\x20\x20_OUT._t1\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x20float(float(-_ps.y)));\x0a\x20\x20\x20\x20_OUT._t2\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x200.00000000E+00);\x0a\x20\x20\x20\x20_OUT._t3\x20=\x20TexCoord.xxxy\x20+\x20vec4(float(float(-_ps.x)),\x200.00000000E+00,\x20float(float(_ps.x)),\x20float(float(_ps.y)));\x0a\x20\x20\x20\x20_ret_0._position1\x20=\x20_OUT._position1;\x0a\x20\x20\x20\x20_ret_0._color1\x20=\x20COLOR;\x0a\x20\x20\x20\x20_ret_0._texCoord1\x20=\x20TexCoord.xy;\x0a\x20\x20\x20\x20_ret_0._t1\x20=\x20_OUT._t1;\x0a\x20\x20\x20\x20_ret_0._t2\x20=\x20_OUT._t2;\x0a\x20\x20\x20\x20_ret_0._t3\x20=\x20_OUT._t3;\x0a\x20\x20\x20\x20VARps\x20=\x20_ps;\x0a\x20\x20\x20\x20gl_Position\x20=\x20_OUT._position1;\x0a\x20\x20\x20\x20COL0\x20=\x20COLOR;\x0a\x20\x20\x20\x20TEX0.xy\x20=\x20TexCoord.xy;\x0a\x20\x20\x20\x20TEX1\x20=\x20_OUT._t1;\x0a\x20\x20\x20\x20TEX2\x20=\x20_OUT._t2;\x0a\x20\x20\x20\x20TEX3\x20=\x20_OUT._t3;\x0a\x20\x20\x20\x20return;\x0a\x20\x20\x20\x20COL0\x20=\x20_ret_0._color1;\x0a\x20\x20\x20\x20TEX0.xy\x20=\x20_ret_0._texCoord1;\x0a\x20\x20\x20\x20TEX1\x20=\x20_ret_0._t1;\x0a\x20\x20\x20\x20TEX2\x20=\x20_ret_0._t2;\x0a\x20\x20\x20\x20TEX3\x20=\x20_ret_0._t3;\x0a\x20\x20\x20\x20return;\x0a}\x20";else var _0xdb8380 = _0x3628("0x468") + _0x454ccf + _0x3628("0x283") + _0x22faa6 + _0x3628("0xf4");
  return _0xdb8380;
}
var i_QaNAj_ = "#if\x20__VERSION__\x20>=\x20130\x0a#define\x20COMPAT_VARYING\x20in\x0a#define\x20COMPAT_TEXTURE\x20texture\x0aout\x20vec4\x20FragColor;\x0a#else\x0a#define\x20COMPAT_VARYING\x20varying\x0a#define\x20FragColor\x20gl_FragColor\x0a#define\x20COMPAT_TEXTURE\x20texture2D\x0a#endif\x0a\x0a#ifdef\x20GL_ES\x0a#ifdef\x20GL_FRAGMENT_PRECISION_HIGH\x0aprecision\x20highp\x20float;\x0a#else\x0aprecision\x20mediump\x20float;\x0a#endif\x0a#define\x20COMPAT_PRECISION\x20mediump\x0a#else\x0a#define\x20COMPAT_PRECISION\x0a#endif\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20VARps;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t3;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t2;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_t1;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec2\x20_texCoord;\x0aCOMPAT_VARYING\x20\x20\x20\x20\x20vec4\x20_color;\x0astruct\x20input_dummy\x20{\x0a\x20\x20\x20\x20vec2\x20_video_size;\x0a\x20\x20\x20\x20vec2\x20_texture_size;\x0a\x20\x20\x20\x20vec2\x20_output_dummy_size;\x0a};\x0astruct\x20out_vertex\x20{\x0a\x20\x20\x20\x20vec4\x20_color;\x0a\x20\x20\x20\x20vec2\x20_texCoord;\x0a\x20\x20\x20\x20vec4\x20_t1;\x0a\x20\x20\x20\x20vec4\x20_t2;\x0a\x20\x20\x20\x20vec4\x20_t3;\x0a\x20\x20\x20\x20vec2\x20VARps;\x0a};\x0avec4\x20_ret_0;\x0afloat\x20_TMP44;\x0avec4\x20_TMP40;\x0afloat\x20_TMP39;\x0afloat\x20_TMP37;\x0avec2\x20_TMP38;\x0afloat\x20_TMP36;\x0afloat\x20_TMP35;\x0afloat\x20_TMP34;\x0afloat\x20_TMP43;\x0abool\x20_TMP33;\x0abool\x20_TMP32;\x0abool\x20_TMP31;\x0abool\x20_TMP30;\x0avec3\x20_TMP42;\x0abool\x20_TMP29;\x0abool\x20_TMP28;\x0abool\x20_TMP27;\x0abool\x20_TMP26;\x0abool\x20_TMP25;\x0abool\x20_TMP24;\x0abool\x20_TMP23;\x0abool\x20_TMP22;\x0avec4\x20_TMP20;\x0avec4\x20_TMP18;\x0avec4\x20_TMP16;\x0avec4\x20_TMP14;\x0avec4\x20_TMP11;\x0avec4\x20_TMP9;\x0avec4\x20_TMP7;\x0avec4\x20_TMP5;\x0avec4\x20_TMP4;\x0avec4\x20_TMP3;\x0avec4\x20_TMP2;\x0avec4\x20_TMP1;\x0avec2\x20_TMP0;\x0aout_vertex\x20_VAR1;\x0auniform\x20sampler2D\x20Texture;\x0ainput_dummy\x20_IN1;\x0auniform\x20sampler2D\x20LUT;\x0avec2\x20_x0063;\x0avec2\x20_val0065;\x0avec2\x20_a0065;\x0avec2\x20_c0069;\x0avec2\x20_c0071;\x0avec2\x20_c0073;\x0avec3\x20_r0077;\x0avec3\x20_v0077;\x0avec3\x20_r0087;\x0avec3\x20_v0087;\x0avec3\x20_r0097;\x0avec3\x20_v0097;\x0avec3\x20_r0107;\x0avec3\x20_v0107;\x0avec3\x20_r0115;\x0avec3\x20_v0115;\x0avec3\x20_r0125;\x0avec3\x20_v0125;\x0avec3\x20_r0135;\x0avec3\x20_v0135;\x0avec3\x20_r0145;\x0avec3\x20_v0145;\x0avec3\x20_r0155;\x0avec3\x20_v0155;\x0abvec3\x20_res0163;\x0avec3\x20_a0165;\x0abvec3\x20_res0167;\x0avec3\x20_a0169;\x0abvec3\x20_res0171;\x0avec3\x20_a0173;\x0abvec3\x20_res0175;\x0avec3\x20_a0177;\x0abvec3\x20_res0179;\x0avec3\x20_a0181;\x0abvec3\x20_res0183;\x0avec3\x20_a0185;\x0abvec3\x20_res0187;\x0avec3\x20_a0189;\x0abvec3\x20_res0191;\x0avec3\x20_a0193;\x0abvec3\x20_res0195;\x0avec3\x20_a0197;\x0abvec3\x20_res0199;\x0avec3\x20_a0201;\x0abvec3\x20_res0203;\x0avec3\x20_a0205;\x0abvec3\x20_res0207;\x0avec3\x20_a0209;\x0avec3\x20_a0211;\x0avec3\x20_a0213;\x0avec3\x20_a0215;\x0avec4\x20_a0217;\x0avec2\x20_x0219;\x0avec2\x20_c0223;\x0avec3\x20_r0229;\x0avec4\x20_v0229;\x0aCOMPAT_VARYING\x20vec4\x20TEX0;\x0aCOMPAT_VARYING\x20vec4\x20TEX1;\x0aCOMPAT_VARYING\x20vec4\x20TEX2;\x0aCOMPAT_VARYING\x20vec4\x20TEX3;\x0a\x20\x0auniform\x20COMPAT_PRECISION\x20vec2\x20TextureSize;\x0auniform\x20COMPAT_PRECISION\x20vec2\x20sTextureSize;\x0avoid\x20main()\x0a{\x0a\x20\x20\x20\x20vec2\x20_fp;\x0a\x20\x20\x20\x20vec2\x20_quad;\x0a\x20\x20\x20\x20vec3\x20_w1;\x0a\x20\x20\x20\x20vec3\x20_w2;\x0a\x20\x20\x20\x20vec3\x20_w3;\x0a\x20\x20\x20\x20vec3\x20_w4;\x0a\x20\x20\x20\x20vec3\x20_w5;\x0a\x20\x20\x20\x20vec3\x20_w6;\x0a\x20\x20\x20\x20vec3\x20_w7;\x0a\x20\x20\x20\x20vec3\x20_w8;\x0a\x20\x20\x20\x20vec3\x20_w9;\x0a\x20\x20\x20\x20bvec4\x20_cross;\x0a\x20\x20\x20\x20vec2\x20_index;\x0a\x20\x20\x20\x20vec4\x20_weights;\x0a\x20\x20\x20\x20float\x20_sum;\x0a\x20\x20\x20\x20bvec3\x20_TMP50[3];\x0a\x20\x20\x20\x20_x0063\x20=\x20TEX0.xy*TextureSize;\x0a\x20\x20\x20\x20_fp\x20=\x20fract(_x0063);\x0a\x20\x20\x20\x20_a0065\x20=\x20-5.00000000E-01\x20+\x20_fp;\x0a\x20\x20\x20\x20_val0065\x20=\x20vec2(float((_a0065.x\x20>\x200.00000000E+00)),\x20float((_a0065.y\x20>\x200.00000000E+00)));\x0a\x20\x20\x20\x20_TMP0\x20=\x20_val0065\x20-\x20vec2(float((_a0065.x\x20<\x200.00000000E+00)),\x20float((_a0065.y\x20<\x200.00000000E+00)));\x0a\x20\x20\x20\x20_quad\x20=\x20vec2(float(_TMP0.x),\x20float(_TMP0.y));\x0a\x20\x20\x20\x20_TMP1\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX0.xy);\x0a\x20\x20\x20\x20_c0069\x20=\x20TEX0.xy\x20+\x20vec2(VARps.x,\x20VARps.y)*vec2(float(_quad.x),\x20float(_quad.y));\x0a\x20\x20\x20\x20_TMP2\x20=\x20COMPAT_TEXTURE(Texture,\x20_c0069);\x0a\x20\x20\x20\x20_c0071\x20=\x20TEX0.xy\x20+\x20vec2(VARps.x,\x200.00000000E+00)*vec2(float(_quad.x),\x20float(_quad.y));\x0a\x20\x20\x20\x20_TMP3\x20=\x20COMPAT_TEXTURE(Texture,\x20_c0071);\x0a\x20\x20\x20\x20_c0073\x20=\x20TEX0.xy\x20+\x20vec2(0.00000000E+00,\x20VARps.y)*vec2(float(_quad.x),\x20float(_quad.y));\x0a\x20\x20\x20\x20_TMP4\x20=\x20COMPAT_TEXTURE(Texture,\x20_c0073);\x0a\x20\x20\x20\x20_TMP5\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX1.xw);\x0a\x20\x20\x20\x20_v0077\x20=\x20vec3(float(_TMP5.x),\x20float(_TMP5.y),\x20float(_TMP5.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0077.x),\x20float(_v0077.y),\x20float(_v0077.z)));\x0a\x20\x20\x20\x20_r0077.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0077.x),\x20float(_v0077.y),\x20float(_v0077.z)));\x0a\x20\x20\x20\x20_r0077.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0077.x),\x20float(_v0077.y),\x20float(_v0077.z)));\x0a\x20\x20\x20\x20_r0077.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w1\x20=\x20vec3(float(_r0077.x),\x20float(_r0077.y),\x20float(_r0077.z));\x0a\x20\x20\x20\x20_TMP7\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX1.yw);\x0a\x20\x20\x20\x20_v0087\x20=\x20vec3(float(_TMP7.x),\x20float(_TMP7.y),\x20float(_TMP7.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0087.x),\x20float(_v0087.y),\x20float(_v0087.z)));\x0a\x20\x20\x20\x20_r0087.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0087.x),\x20float(_v0087.y),\x20float(_v0087.z)));\x0a\x20\x20\x20\x20_r0087.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0087.x),\x20float(_v0087.y),\x20float(_v0087.z)));\x0a\x20\x20\x20\x20_r0087.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w2\x20=\x20vec3(float(_r0087.x),\x20float(_r0087.y),\x20float(_r0087.z));\x0a\x20\x20\x20\x20_TMP9\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX1.zw);\x0a\x20\x20\x20\x20_v0097\x20=\x20vec3(float(_TMP9.x),\x20float(_TMP9.y),\x20float(_TMP9.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0097.x),\x20float(_v0097.y),\x20float(_v0097.z)));\x0a\x20\x20\x20\x20_r0097.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0097.x),\x20float(_v0097.y),\x20float(_v0097.z)));\x0a\x20\x20\x20\x20_r0097.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0097.x),\x20float(_v0097.y),\x20float(_v0097.z)));\x0a\x20\x20\x20\x20_r0097.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w3\x20=\x20vec3(float(_r0097.x),\x20float(_r0097.y),\x20float(_r0097.z));\x0a\x20\x20\x20\x20_TMP11\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX2.xw);\x0a\x20\x20\x20\x20_v0107\x20=\x20vec3(float(_TMP11.x),\x20float(_TMP11.y),\x20float(_TMP11.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0107.x),\x20float(_v0107.y),\x20float(_v0107.z)));\x0a\x20\x20\x20\x20_r0107.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0107.x),\x20float(_v0107.y),\x20float(_v0107.z)));\x0a\x20\x20\x20\x20_r0107.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0107.x),\x20float(_v0107.y),\x20float(_v0107.z)));\x0a\x20\x20\x20\x20_r0107.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w4\x20=\x20vec3(float(_r0107.x),\x20float(_r0107.y),\x20float(_r0107.z));\x0a\x20\x20\x20\x20_v0115\x20=\x20vec3(float(_TMP1.x),\x20float(_TMP1.y),\x20float(_TMP1.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0115.x),\x20float(_v0115.y),\x20float(_v0115.z)));\x0a\x20\x20\x20\x20_r0115.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0115.x),\x20float(_v0115.y),\x20float(_v0115.z)));\x0a\x20\x20\x20\x20_r0115.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0115.x),\x20float(_v0115.y),\x20float(_v0115.z)));\x0a\x20\x20\x20\x20_r0115.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w5\x20=\x20vec3(float(_r0115.x),\x20float(_r0115.y),\x20float(_r0115.z));\x0a\x20\x20\x20\x20_TMP14\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX2.zw);\x0a\x20\x20\x20\x20_v0125\x20=\x20vec3(float(_TMP14.x),\x20float(_TMP14.y),\x20float(_TMP14.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0125.x),\x20float(_v0125.y),\x20float(_v0125.z)));\x0a\x20\x20\x20\x20_r0125.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0125.x),\x20float(_v0125.y),\x20float(_v0125.z)));\x0a\x20\x20\x20\x20_r0125.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0125.x),\x20float(_v0125.y),\x20float(_v0125.z)));\x0a\x20\x20\x20\x20_r0125.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w6\x20=\x20vec3(float(_r0125.x),\x20float(_r0125.y),\x20float(_r0125.z));\x0a\x20\x20\x20\x20_TMP16\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX3.xw);\x0a\x20\x20\x20\x20_v0135\x20=\x20vec3(float(_TMP16.x),\x20float(_TMP16.y),\x20float(_TMP16.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0135.x),\x20float(_v0135.y),\x20float(_v0135.z)));\x0a\x20\x20\x20\x20_r0135.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0135.x),\x20float(_v0135.y),\x20float(_v0135.z)));\x0a\x20\x20\x20\x20_r0135.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0135.x),\x20float(_v0135.y),\x20float(_v0135.z)));\x0a\x20\x20\x20\x20_r0135.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w7\x20=\x20vec3(float(_r0135.x),\x20float(_r0135.y),\x20float(_r0135.z));\x0a\x20\x20\x20\x20_TMP18\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX3.yw);\x0a\x20\x20\x20\x20_v0145\x20=\x20vec3(float(_TMP18.x),\x20float(_TMP18.y),\x20float(_TMP18.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0145.x),\x20float(_v0145.y),\x20float(_v0145.z)));\x0a\x20\x20\x20\x20_r0145.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0145.x),\x20float(_v0145.y),\x20float(_v0145.z)));\x0a\x20\x20\x20\x20_r0145.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0145.x),\x20float(_v0145.y),\x20float(_v0145.z)));\x0a\x20\x20\x20\x20_r0145.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w8\x20=\x20vec3(float(_r0145.x),\x20float(_r0145.y),\x20float(_r0145.z));\x0a\x20\x20\x20\x20_TMP20\x20=\x20COMPAT_TEXTURE(Texture,\x20TEX3.zw);\x0a\x20\x20\x20\x20_v0155\x20=\x20vec3(float(_TMP20.x),\x20float(_TMP20.y),\x20float(_TMP20.z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x202.99072266E-01,\x205.86914062E-01,\x201.14013672E-01),\x20vec3(float(_v0155.x),\x20float(_v0155.y),\x20float(_v0155.z)));\x0a\x20\x20\x20\x20_r0155.x\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x20-1.68945312E-01,\x20-3.31054688E-01,\x205.00000000E-01),\x20vec3(float(_v0155.x),\x20float(_v0155.y),\x20float(_v0155.z)));\x0a\x20\x20\x20\x20_r0155.y\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(\x205.00000000E-01,\x20-4.18945312E-01,\x20-8.09936523E-02),\x20vec3(float(_v0155.x),\x20float(_v0155.y),\x20float(_v0155.z)));\x0a\x20\x20\x20\x20_r0155.z\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_w9\x20=\x20vec3(float(_r0155.x),\x20float(_r0155.y),\x20float(_r0155.z));\x0a\x20\x20\x20\x20_a0165\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w1\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0165);\x0a\x20\x20\x20\x20_res0163\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP22\x20=\x20_res0163.x\x20||\x20_res0163.y\x20||\x20_res0163.z;\x0a\x20\x20\x20\x20_a0169\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w2\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0169);\x0a\x20\x20\x20\x20_res0167\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP23\x20=\x20_res0167.x\x20||\x20_res0167.y\x20||\x20_res0167.z;\x0a\x20\x20\x20\x20_a0173\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w3\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0173);\x0a\x20\x20\x20\x20_res0171\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP24\x20=\x20_res0171.x\x20||\x20_res0171.y\x20||\x20_res0171.z;\x0a\x20\x20\x20\x20_a0177\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w4\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0177);\x0a\x20\x20\x20\x20_res0175\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP25\x20=\x20_res0175.x\x20||\x20_res0175.y\x20||\x20_res0175.z;\x0a\x20\x20\x20\x20_a0181\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w6\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0181);\x0a\x20\x20\x20\x20_res0179\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP26\x20=\x20_res0179.x\x20||\x20_res0179.y\x20||\x20_res0179.z;\x0a\x20\x20\x20\x20_a0185\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w7\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0185);\x0a\x20\x20\x20\x20_res0183\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP27\x20=\x20_res0183.x\x20||\x20_res0183.y\x20||\x20_res0183.z;\x0a\x20\x20\x20\x20_a0189\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w8\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0189);\x0a\x20\x20\x20\x20_res0187\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP28\x20=\x20_res0187.x\x20||\x20_res0187.y\x20||\x20_res0187.z;\x0a\x20\x20\x20\x20_a0193\x20=\x20(_w5\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w9\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0193);\x0a\x20\x20\x20\x20_res0191\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP29\x20=\x20_res0191.x\x20||\x20_res0191.y\x20||\x20_res0191.z;\x0a\x20\x20\x20\x20_TMP50[0]\x20=\x20bvec3(_TMP22,\x20_TMP23,\x20_TMP24);\x0a\x20\x20\x20\x20_TMP50[1]\x20=\x20bvec3(_TMP25,\x20false,\x20_TMP26);\x0a\x20\x20\x20\x20_TMP50[2]\x20=\x20bvec3(_TMP27,\x20_TMP28,\x20_TMP29);\x0a\x20\x20\x20\x20_a0197\x20=\x20(_w4\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w2\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0197);\x0a\x20\x20\x20\x20_res0195\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP30\x20=\x20_res0195.x\x20||\x20_res0195.y\x20||\x20_res0195.z;\x0a\x20\x20\x20\x20_a0201\x20=\x20(_w2\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w6\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0201);\x0a\x20\x20\x20\x20_res0199\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP31\x20=\x20_res0199.x\x20||\x20_res0199.y\x20||\x20_res0199.z;\x0a\x20\x20\x20\x20_a0205\x20=\x20(_w8\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w4\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0205);\x0a\x20\x20\x20\x20_res0203\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP32\x20=\x20_res0203.x\x20||\x20_res0203.y\x20||\x20_res0203.z;\x0a\x20\x20\x20\x20_a0209\x20=\x20(_w6\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01))\x20-\x20(_w8\x20+\x20vec3(\x200.00000000E+00,\x205.00000000E-01,\x205.00000000E-01));\x0a\x20\x20\x20\x20_TMP42\x20=\x20abs(_a0209);\x0a\x20\x20\x20\x20_res0207\x20=\x20bvec3(_TMP42.x\x20>\x201.88232422E-01,\x20_TMP42.y\x20>\x202.74505615E-02,\x20_TMP42.z\x20>\x202.35290527E-02);\x0a\x20\x20\x20\x20_TMP33\x20=\x20_res0207.x\x20||\x20_res0207.y\x20||\x20_res0207.z;\x0a\x20\x20\x20\x20_cross\x20=\x20bvec4(_TMP30,\x20_TMP31,\x20_TMP32,\x20_TMP33);\x0a\x20\x20\x20\x20_a0211\x20=\x20vec3(float(_TMP50[0].x),\x20float(_TMP50[0].y),\x20float(_TMP50[0].z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(float(_a0211.x),\x20float(_a0211.y),\x20float(_a0211.z)),\x20vec3(\x201.00000000E+00,\x202.00000000E+00,\x204.00000000E+00));\x0a\x20\x20\x20\x20_TMP34\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_a0213\x20=\x20vec3(float(_TMP50[1].x),\x20float(_TMP50[1].y),\x20float(_TMP50[1].z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(float(_a0213.x),\x20float(_a0213.y),\x20float(_a0213.z)),\x20vec3(\x208.00000000E+00,\x200.00000000E+00,\x201.60000000E+01));\x0a\x20\x20\x20\x20_TMP35\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_a0215\x20=\x20vec3(float(_TMP50[2].x),\x20float(_TMP50[2].y),\x20float(_TMP50[2].z));\x0a\x20\x20\x20\x20_TMP43\x20=\x20dot(vec3(float(_a0215.x),\x20float(_a0215.y),\x20float(_a0215.z)),\x20vec3(\x203.20000000E+01,\x206.40000000E+01,\x201.28000000E+02));\x0a\x20\x20\x20\x20_TMP36\x20=\x20float(_TMP43);\x0a\x20\x20\x20\x20_index.x\x20=\x20_TMP34\x20+\x20_TMP35\x20+\x20_TMP36;\x0a\x20\x20\x20\x20_a0217\x20=\x20vec4(float(_cross.x),\x20float(_cross.y),\x20float(_cross.z),\x20float(_cross.w));\x0a\x20\x20\x20\x20_TMP44\x20=\x20dot(vec4(float(_a0217.x),\x20float(_a0217.y),\x20float(_a0217.z),\x20float(_a0217.w)),\x20vec4(\x201.00000000E+00,\x202.00000000E+00,\x204.00000000E+00,\x208.00000000E+00));\x0a\x20\x20\x20\x20_TMP37\x20=\x20float(_TMP44);\x0a\x20\x20\x20\x20_x0219\x20=\x20_fp*4.00000000E+00;\x0a\x20\x20\x20\x20_TMP38\x20=\x20floor(_x0219);\x0a\x20\x20\x20\x20_TMP39\x20=\x20dot(_TMP38,\x20vec2(\x201.00000000E+00,\x204.00000000E+00));\x0a\x20\x20\x20\x20_index.y\x20=\x20float((float((_TMP37*1.60000000E+01))\x20+\x20_TMP39));\x0a\x20\x20\x20\x20_c0223\x20=\x20vec2(float((_index*vec2(\x203.90625000E-03,\x203.90625000E-03)\x20+\x20vec2(\x201.95312500E-03,\x201.95312500E-03)).x),\x20float((_index*vec2(\x203.90625000E-03,\x203.90625000E-03)\x20+\x20vec2(\x201.95312500E-03,\x201.95312500E-03)).y));\x0a\x20\x20\x20\x20_TMP40\x20=\x20COMPAT_TEXTURE(LUT,\x20_c0223);\x0a\x20\x20\x20\x20_weights\x20=\x20vec4(float(_TMP40.x),\x20float(_TMP40.y),\x20float(_TMP40.z),\x20float(_TMP40.w));\x0a\x20\x20\x20\x20_TMP44\x20=\x20dot(vec4(float(_weights.x),\x20float(_weights.y),\x20float(_weights.z),\x20float(_weights.w)),\x20vec4(\x201.00000000E+00,\x201.00000000E+00,\x201.00000000E+00,\x201.00000000E+00));\x0a\x20\x20\x20\x20_sum\x20=\x20float(_TMP44);\x0a\x20\x20\x20\x20_v0229\x20=\x20vec4(float((_weights/_sum).x),\x20float((_weights/_sum).y),\x20float((_weights/_sum).z),\x20float((_weights/_sum).w));\x0a\x20\x20\x20\x20_r0229\x20=\x20_v0229.x*_TMP1.xyz;\x0a\x20\x20\x20\x20_r0229\x20=\x20_r0229\x20+\x20_v0229.y*_TMP2.xyz;\x0a\x20\x20\x20\x20_r0229\x20=\x20_r0229\x20+\x20_v0229.z*_TMP3.xyz;\x0a\x20\x20\x20\x20_r0229\x20=\x20_r0229\x20+\x20_v0229.w*_TMP4.xyz;\x0a\x20\x20\x20\x20_ret_0\x20=\x20vec4(_r0229.x,\x20_r0229.y,\x20_r0229.z,\x201.00000000E+00);\x0a\x20\x20\x20\x20FragColor\x20=\x20_ret_0;\x0a\x20\x20\x20\x20return;\x0a}\x20";